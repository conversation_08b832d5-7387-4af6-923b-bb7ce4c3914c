import { Injectable, Logger } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { ConfigService, ConfigType, ServicesConfig } from '@config';
import { AppException, ErrorCode } from '@common/exceptions/app.exception';
import { GoogleGenAI } from '@google/genai';
import { firstValueFrom } from 'rxjs';

/**
 * Interface cho Google AI Model
 * Khớp chính xác với cấu trúc JSON mà API trả về
 */
interface GoogleAIModel {
  name: string;
  baseModelId: string;
  version: string;
  displayName?: string;
  description?: string;
  inputTokenLimit?: number;
  outputTokenLimit?: number;
  supportedGenerationMethods?: string[];
  temperature?: number;
  maxTemperature?: number;
  topP?: number;
  topK?: number;
}

/**
 * Interface cho Google AI Models Response
 */
interface GoogleAIModelsResponse {
  models: GoogleAIModel[];
}

/**
 * Service tương tác với Google AI API
 */
@Injectable()
export class GoogleAIService {
  private readonly googleAI: GoogleGenAI;
  private readonly logger = new Logger(GoogleAIService.name);
  private readonly apiBaseUrl = 'https://generativelanguage.googleapis.com/v1';

  constructor(
    private readonly configService: ConfigService,
    private readonly httpService: HttpService,
  ) {
    const servicesConfig = this.configService.getConfig<ServicesConfig>(
      ConfigType.Services,
    );
    const apiKey = servicesConfig.googleAI?.apiKey;

    if (!apiKey) {
      this.logger.warn(
        'GOOGLE_AI_API_KEY is not defined in environment variables',
      );
    }

    this.googleAI = new GoogleGenAI({
      apiKey: apiKey || '',
    });
  }

  /**
   * Tạo một instance Google AI mới với API key tùy chỉnh
   * @param apiKey API key tùy chỉnh
   * @returns Instance Google AI mới
   */
  createClient(apiKey: string): GoogleGenAI {
    if (!apiKey) {
      this.logger.warn('Empty API key provided to createClient');
    }

    return new GoogleGenAI({
      apiKey: apiKey || '',
    });
  }

  /**
   * Lấy danh sách model từ Google AI sử dụng REST API
   * @param pageSize Số lượng model tối đa trên mỗi trang (mặc định: 50, tối đa: 1000)
   * @param pageToken Mã thông báo trang để lấy trang tiếp theo
   * @param options Tùy chọn bổ sung (apiKey)
   * @returns Danh sách model từ Google AI và mã thông báo trang tiếp theo (nếu có)
   * @throws AppException nếu có lỗi khi lấy danh sách model
   */
  async getModels(
    pageSize?: number,
    pageToken?: string,
    options?: {
      apiKey?: string;
    },
  ): Promise<GoogleAIModel[]> {
    try {
      // Lấy API key từ options hoặc từ cấu hình
      const servicesConfig = this.configService.getConfig<ServicesConfig>(
        ConfigType.Services,
      );
      const apiKey = options?.apiKey || servicesConfig.googleAI?.apiKey || '';

      if (!apiKey) {
        throw new Error('API key is required to list models');
      }

      // Thiết lập timeout cho request
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 30000); // 30 seconds timeout

      // Xây dựng URL với các tham số phân trang
      let url = `${this.apiBaseUrl}/models?key=${apiKey}`;

      // Thêm tham số pageSize nếu được cung cấp
      if (pageSize) {
        // Đảm bảo pageSize nằm trong khoảng hợp lệ (1-1000)
        const validPageSize = Math.min(Math.max(1, pageSize), 1000);
        url += `&pageSize=${validPageSize}`;
      }

      // Thêm tham số pageToken nếu được cung cấp
      if (pageToken) {
        url += `&pageToken=${encodeURIComponent(pageToken)}`;
      }

      // Gọi REST API để lấy danh sách model
      const response = await firstValueFrom(
        this.httpService.get<GoogleAIModelsResponse>(url, {
          headers: {
            'Content-Type': 'application/json',
          },
          signal: controller.signal,
        }),
      );

      clearTimeout(timeoutId);

      // Lấy danh sách model và mã thông báo trang tiếp theo từ response
      return response.data.models || [];
    } catch (error: any) {
      this.logger.error(
        `Error retrieving models from Google AI: ${error.message}`,
        error.stack,
      );

      // Xử lý các lỗi khi kết nối Google AI API
      if (error.response) {
        // Lỗi từ API response
        const status = error.response.status;
        const data = error.response.data;

        if (status === 429) {
          throw new AppException(
            ErrorCode.OPENAI_QUOTA_EXCEEDED,
            'Đã vượt quá giới hạn sử dụng Google AI API',
          );
        }

        if (status === 401 || status === 403) {
          throw new AppException(
            ErrorCode.OPENAI_API_ERROR,
            'Lỗi xác thực API key: ' +
              (data?.error?.message || 'Không có quyền truy cập'),
          );
        }

        throw new AppException(
          ErrorCode.OPENAI_API_ERROR,
          `Lỗi API (${status}): ${data?.error?.message || 'Không xác định'}`,
        );
      }

      if (error.code === 'ECONNABORTED' || error.message.includes('timeout')) {
        throw new AppException(
          ErrorCode.OPENAI_TIMEOUT,
          'Kết nối đến Google AI API bị gián đoạn hoặc quá thời gian chờ',
        );
      }

      if (error.code === 'ENOTFOUND' || error.message.includes('network')) {
        throw new AppException(
          ErrorCode.OPENAI_TIMEOUT,
          'Lỗi kết nối đến Google AI API',
        );
      }

      // Các lỗi khác
      throw new AppException(
        ErrorCode.OPENAI_API_ERROR,
        'Lỗi khi lấy danh sách model: ' + error.message,
      );
    }
  }
}
