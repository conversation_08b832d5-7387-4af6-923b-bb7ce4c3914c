# Product Requirements Document: HRM Org-Units Module TenantId Implementation

## Project Overview
Update the HRM Org-Units module (`src/modules/hrm/org-units`) to implement manual tenantId injection pattern for proper tenant isolation. This module manages organizational units (departments) and their hierarchical structure.

## Background
The HRM org-units module currently has partial tenantId implementation. The repository layer has been updated but controllers and services need to be updated to follow the manual tenantId injection pattern established in other modules like todolists.

## Objectives
1. **Department Tenant Isolation**: Ensure all department operations are properly isolated by tenant
2. **Hierarchical Security**: Maintain tenant boundaries in department tree structures
3. **API Consistency**: Follow established patterns from other updated modules
4. **Data Security**: Prevent cross-tenant access to department data
5. **Maintainability**: Use simple, explicit tenantId handling throughout the module

## Scope

### In Scope
- All repository classes and their query methods
- All service classes that interact with repositories
- All controller endpoints that handle tenant-specific data
- Database query builders and raw SQL queries
- Entity relationships and joins
- Pagination and filtering logic
- Create, Read, Update, Delete operations
- Search and reporting functionality

### Out of Scope
- System-level entities (users, companies, permissions)
- Public endpoints without tenant context
- Authentication and authorization logic
- Database schema changes

## Technical Requirements

### 1. Repository Layer Updates
- Add tenantId parameter to all repository methods
- Update all TypeORM find/findOne/createQueryBuilder calls
- Add tenantId to WHERE clauses in all queries
- Handle entity relationships with tenantId filtering
- Update pagination logic to include tenantId

### 2. Service Layer Updates
- Pass tenantId from controllers to repositories
- Update business logic to handle tenantId
- Ensure all repository calls include tenantId
- Update validation logic for tenant-specific data

### 3. Controller Layer Updates
- Use @CurrentUser() decorator to extract tenantId from JWT payload
- Pass Number(user.tenantId) as first parameter to service methods
- Update API documentation for tenant-aware endpoints

## HRM Org-Units Module Specific Requirements

### Module Structure
```
src/modules/hrm/org-units/
├── controllers/
│   ├── department.controller.ts          ❌ Needs tenantId extraction
│   ├── department-members.controller.ts  ❌ Needs tenantId extraction
│   └── department-tree.controller.ts     ❌ Needs tenantId extraction
├── services/
│   ├── department.service.ts             ❌ Needs tenantId parameters
│   ├── department-members.service.ts     ❌ Needs tenantId parameters
│   └── department-tree.service.ts        ❌ Needs tenantId parameters
├── repositories/
│   └── department.repository.ts          ✅ Already has tenantId filtering
├── entities/
│   └── department.entity.ts              ⚠️ Needs TenantEntity interface removed
```

### Implementation Tasks

#### Task 1: Entity Cleanup
- Remove TenantEntity interface from Department entity
- Keep tenantId field for manual handling
- Update imports to remove TenantEntity dependency

#### Task 2: Repository Verification
- Verify DepartmentRepository has complete tenantId filtering
- Ensure all CRUD methods include tenantId conditions
- Add any missing tenantId filtering

#### Task 3: Service Layer Updates
- Update DepartmentService to accept tenantId parameter
- Update DepartmentMembersService for tenant isolation
- Update DepartmentTreeService for hierarchical tenant filtering

#### Task 4: Controller Layer Updates
- Update DepartmentController with @CurrentUser() decorator
- Update DepartmentMembersController with tenantId extraction
- Update DepartmentTreeController with tenant-aware tree operations

#### Task 5: Testing & Validation
- Test department operations with multiple tenants
- Verify department hierarchy respects tenant boundaries
- Validate no data leakage between tenants
- Add tenantId validation where needed

### 4. Entity Relationships
- Update JOIN queries to include tenantId filtering
- Handle foreign key relationships across tenants
- Ensure cascade operations respect tenant boundaries

### 5. Testing Strategy
- Unit tests for repository methods with tenantId
- Integration tests for service layer
- End-to-end tests for API endpoints
- Security tests to verify tenant isolation

## Modules to Update

### ✅ COMPLETED MODULES
1. **Todolists Module** (100% COMPLETE)
   - ✅ ProjectController, ProjectService, ProjectRepository
   - ✅ TodoController, TodoService, TodoRepository
   - ✅ TodoTagService, TodoTagRepository
   - ✅ All related repositories (ProjectMember, TodoCollaborator, TodoAttachment, TaskKr)

### 🔄 IN PROGRESS MODULES
2. **HRM Module** (70% COMPLETE)
   - ✅ Employee Module (EmployeeController, EmployeeService, EmployeeRepository)
   - ✅ Org-Units Module (Department, DepartmentMembers, DepartmentTree)
   - ❌ Contracts Module (ContractController, ContractService, ContractRepository)
   - ❌ Attendance Management Module
   - ❌ Human Resources Module
   - ❌ Recruitment Module

### ❌ PENDING MODULES
3. **OKRs Module** (objectives, key-results, cycles)
4. **Calendar Module** (events, schedules)
5. **Chat Module** (messages, conversations)
6. **Notifications Module** (notifications, alerts)
7. **File Management Module** (uploads, documents)
8. **Reports Module** (analytics, exports)
9. **Email Module** (templates, logs)
10. **Common Module** (audit logs, system data)

## Implementation Strategy

### Phase 1: Repository Layer (Week 1)
- Update all repository classes to accept tenantId parameters
- Add tenantId to all WHERE clauses
- Update query builders and raw SQL queries
- Test repository methods in isolation

### Phase 2: Service Layer (Week 2)
- Update service methods to pass tenantId to repositories
- Ensure business logic respects tenant boundaries
- Update validation and error handling

### Phase 3: Controller Layer (Week 3)
- Update controllers to use @CurrentUser() decorator
- Pass Number(user.tenantId) to service methods
- Update API documentation and Swagger specs

### Phase 4: Testing & Validation (Week 4)
- Comprehensive testing of tenant isolation
- Performance testing with tenantId indexes
- Security testing to prevent cross-tenant access
- Documentation updates

## Success Criteria
1. All database queries include tenantId filtering
2. No cross-tenant data access possible
3. All tests pass with tenant isolation
4. Performance meets requirements
5. Code is maintainable and well-documented

## Risk Mitigation
- Systematic approach to avoid missing any queries
- Comprehensive testing at each layer
- Code review process for all changes
- Rollback plan if issues are discovered

## Deliverables
1. Updated repository classes with tenantId filtering
2. Updated service classes with tenantId handling
3. Updated controller classes with @CurrentTenant() usage
4. Comprehensive test suite
5. Updated documentation
6. Performance optimization recommendations

## Current Progress Status
- **Todolists Module**: 100% Complete ✅
- **HRM Employee Module**: 100% Complete ✅
- **HRM Org-Units Module**: 100% Complete ✅
- **HRM Contracts Module**: 0% Complete ❌
- **Other HRM Modules**: 0% Complete ❌
- **All Other Modules**: 0% Complete ❌

**Overall Progress: ~25% Complete**
