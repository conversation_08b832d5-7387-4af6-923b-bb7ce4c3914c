# Email System Queue Service

Service để quản lý việc thêm job gửi email vào queue `EMAIL_SYSTEM` với processor ở worker.

## Cấu trúc

### 1. Queue Constants
- **QueueName.EMAIL_SYSTEM**: `'email-system'`
- **EmailSystemJobName.SEND_TEMPLATE_EMAIL**: `'send-template-email'`

### 2. DTO Interface
```typescript
interface EmailSystemJobDto {
  category: string;           // Danh mục email trong admin_template_email
  data: Record<string, any>;  // Dữ liệu thay thế placeholder
  to: string | string[];      // Email người nhận
}
```

## Cách sử dụng

### 1. Import Service

```typescript
import { EmailSystemQueueService, EmailSystemJobDto } from '@shared/queue/email-system-queue.service';
// hoặc
import { QueueService } from '@shared/queue/queue.service';
```

### 2. Inject vào Constructor

```typescript
constructor(
  private readonly emailSystemQueueService: EmailSystemQueueService,
  // hoặc
  private readonly queueService: QueueService,
) {}
```

### 3. Sử dụng Service

#### Cách 1: Sử dụng EmailSystemQueueService (Khuyến nghị)

```typescript
// Gửi email cơ bản
const emailData: EmailSystemJobDto = {
  category: 'welcome',
  to: '<EMAIL>',
  data: {
    userName: 'John Doe',
    loginUrl: 'https://example.com/login'
  }
};

const jobId = await this.emailSystemQueueService.addEmailSystemJob(emailData);
```

#### Cách 2: Sử dụng QueueService

```typescript
const jobId = await this.queueService.addEmailSystemJob(emailData);
```

## Các Method Có Sẵn

### EmailSystemQueueService

1. **addEmailSystemJob(data, opts?)**: Thêm job cơ bản
2. **addHighPriorityEmailSystemJob(data)**: Thêm job ưu tiên cao
3. **addDelayedEmailSystemJob(data, delayMs)**: Thêm job với delay
4. **getEmailSystemJobStatus(jobId)**: Kiểm tra trạng thái job
5. **cancelEmailSystemJob(jobId)**: Hủy job
6. **getEmailSystemQueueStats()**: Lấy thống kê queue

### QueueService

1. **addEmailSystemJob(data, opts?)**: Thêm job vào EMAIL_SYSTEM queue

## Ví dụ Thực Tế

### 1. Gửi Email Chào Mừng

```typescript
async sendWelcomeEmail(userEmail: string, userName: string) {
  const emailData: EmailSystemJobDto = {
    category: 'welcome',
    to: userEmail,
    data: {
      userName: userName,
      loginUrl: 'https://example.com/login',
      supportEmail: '<EMAIL>'
    }
  };

  return await this.emailSystemQueueService.addEmailSystemJob(emailData);
}
```

### 2. Gửi Email Reset Password (Ưu tiên cao)

```typescript
async sendPasswordResetEmail(userEmail: string, resetToken: string) {
  const emailData: EmailSystemJobDto = {
    category: 'password-reset',
    to: userEmail,
    data: {
      resetToken: resetToken,
      resetUrl: `https://example.com/reset-password?token=${resetToken}`,
      expiryTime: '24 giờ'
    }
  };

  return await this.emailSystemQueueService.addHighPriorityEmailSystemJob(emailData);
}
```

### 3. Gửi Email Cho Nhiều Người

```typescript
async sendBulkEmail(emails: string[], subject: string, content: string) {
  const emailData: EmailSystemJobDto = {
    category: 'bulk-notification',
    to: emails, // Array of emails
    data: {
      subject: subject,
      content: content,
      sentAt: new Date().toISOString()
    }
  };

  return await this.emailSystemQueueService.addEmailSystemJob(emailData);
}
```

### 4. Gửi Email Với Delay

```typescript
async sendDelayedNotification(userEmail: string, message: string, delayMinutes: number) {
  const emailData: EmailSystemJobDto = {
    category: 'notification',
    to: userEmail,
    data: {
      message: message,
      timestamp: new Date().toISOString()
    }
  };

  const delayMs = delayMinutes * 60 * 1000;
  return await this.emailSystemQueueService.addDelayedEmailSystemJob(emailData, delayMs);
}
```

### 5. Kiểm Tra Trạng Thái Job

```typescript
async checkJobStatus(jobId: string | number) {
  const status = await this.emailSystemQueueService.getEmailSystemJobStatus(jobId);
  console.log('Job status:', status);
  return status;
}
```

### 6. Lấy Thống Kê Queue

```typescript
async getQueueStatistics() {
  const stats = await this.emailSystemQueueService.getEmailSystemQueueStats();
  console.log('Queue stats:', stats);
  // Output: { waiting: 5, active: 2, completed: 100, failed: 3, delayed: 1, total: 111 }
  return stats;
}
```

## Job Options

Bạn có thể tùy chỉnh job options:

```typescript
const customOptions = {
  priority: 1,              // Ưu tiên (số càng nhỏ, ưu tiên càng cao)
  attempts: 5,              // Số lần thử lại
  delay: 5000,              // Delay 5 giây
  backoff: {
    type: 'exponential',    // Kiểu backoff
    delay: 2000
  },
  removeOnComplete: false,  // Giữ lại job sau khi hoàn thành
  removeOnFail: false       // Giữ lại job khi thất bại
};

await this.emailSystemQueueService.addEmailSystemJob(emailData, customOptions);
```

## Xử Lý Lỗi

```typescript
try {
  const jobId = await this.emailSystemQueueService.addEmailSystemJob(emailData);
  return { success: true, jobId };
} catch (error) {
  console.error('Lỗi khi thêm job:', error.message);
  return { success: false, error: error.message };
}
```

## Processor (Worker)

Processor sẽ nhận job với:
- **Queue Name**: `QueueName.EMAIL_SYSTEM` (`'email-system'`)
- **Job Name**: `EmailSystemJobName.SEND_TEMPLATE_EMAIL` (`'send-template-email'`)
- **Data**: `EmailSystemJobDto`

Processor cần được implement để xử lý job này.

## Lưu Ý

1. **Validation**: Service tự động validate dữ liệu đầu vào
2. **Email Format**: Tự động kiểm tra format email hợp lệ
3. **Logging**: Tự động log các hoạt động quan trọng
4. **Error Handling**: Sử dụng AppException cho error handling nhất quán
5. **Priority**: Mặc định sử dụng HIGH_PRIORITY_JOB_OPTIONS
6. **Redis**: Cần cấu hình Redis URL trong biến môi trường REDIS_URL
