import { ApiProperty } from '@nestjs/swagger';
import { ReferenceType } from '../enum/reference-type.enum';

/**
 * DTO cho sự kiện lịch
 */
export class CalendarEventDto {
  /**
   * ID của sự kiện
   */
  @ApiProperty({ description: 'ID của sự kiện', example: 1 })
  id: number;

  /**
   * Tiêu đề sự kiện
   */
  @ApiProperty({ description: 'Tiêu đề sự kiện', example: 'Họp team' })
  title: string;

  /**
   * Thời gian bắt đầu sự kiện
   */
  @ApiProperty({
    description: 'Thời gian bắt đầu sự kiện (Unix timestamp)',
    example: 1620000000000,
  })
  startTime: number;

  /**
   * Thời gian kết thúc sự kiện
   */
  @ApiProperty({
    description: 'Thời gian kết thúc sự kiện (Unix timestamp)',
    example: 1620003600000,
    nullable: true,
  })
  endTime: number | null;

  /**
   * <PERSON><PERSON> tả sự kiện
   */
  @ApiProperty({
    description: '<PERSON><PERSON> tả sự kiện',
    example: 'Thả<PERSON> luận về kế hoạch Q2',
    nullable: true,
  })
  description: string | null;

  /**
   * Loại sự kiện
   */
  @ApiProperty({
    description: 'Loại sự kiện',
    enum: ReferenceType,
    example: ReferenceType.CALENDAR,
  })
  type: ReferenceType;

  /**
   * ID tham chiếu đến bản ghi gốc
   */
  @ApiProperty({
    description: 'ID tham chiếu đến bản ghi gốc',
    example: 123,
    nullable: true,
  })
  referenceId: number | null;

  /**
   * Màu hiển thị
   */
  @ApiProperty({ description: 'Màu hiển thị', example: '#FF5733' })
  color: string;

  /**
   * Icon hiển thị
   */
  @ApiProperty({
    description: 'Icon hiển thị',
    example: 'event',
    nullable: true,
  })
  icon: string | null;

  /**
   * Thông tin bổ sung
   */
  @ApiProperty({
    description: 'Thông tin bổ sung',
    example: { location: 'Phòng họp A', attendees: ['user1', 'user2'] },
    nullable: true,
  })
  additionalInfo: any;

  /**
   * Có thể chỉnh sửa không
   */
  @ApiProperty({ description: 'Có thể chỉnh sửa không', example: true })
  editable: boolean;
}

/**
 * DTO cho việc tạo sự kiện lịch
 */
export class CreateCalendarEventDto {
  /**
   * Tiêu đề sự kiện
   */
  @ApiProperty({ description: 'Tiêu đề sự kiện', example: 'Họp team' })
  title: string;

  /**
   * Thời gian bắt đầu sự kiện
   */
  @ApiProperty({
    description: 'Thời gian bắt đầu sự kiện (Unix timestamp)',
    example: 1620000000000,
  })
  startTime: number;

  /**
   * Thời gian kết thúc sự kiện
   */
  @ApiProperty({
    description: 'Thời gian kết thúc sự kiện (Unix timestamp)',
    example: 1620003600000,
    nullable: true,
  })
  endTime?: number;

  /**
   * Mô tả sự kiện
   */
  @ApiProperty({
    description: 'Mô tả sự kiện',
    example: 'Thảo luận về kế hoạch Q2',
    nullable: true,
  })
  description?: string;

  /**
   * Loại sự kiện
   */
  @ApiProperty({
    description: 'Loại sự kiện',
    enum: ReferenceType,
    example: ReferenceType.CALENDAR,
    nullable: true,
  })
  referenceType?: ReferenceType;

  /**
   * ID tham chiếu đến bản ghi gốc
   */
  @ApiProperty({
    description: 'ID tham chiếu đến bản ghi gốc',
    example: 123,
    nullable: true,
  })
  referenceId?: number;

  /**
   * Thông tin bổ sung
   */
  @ApiProperty({
    description: 'Thông tin bổ sung',
    example: { location: 'Phòng họp A', attendees: ['user1', 'user2'] },
    nullable: true,
  })
  info?: any;
}

/**
 * DTO cho việc cập nhật sự kiện lịch
 */
export class UpdateCalendarEventDto extends CreateCalendarEventDto {}
