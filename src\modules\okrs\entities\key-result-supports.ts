import { Entity, Column, PrimaryColumn } from 'typeorm';

/**
 * Entity đại diện cho bảng key_result_supports
 * Bảng này lưu trữ quan hệ nhiều-nhiều: Key Result được hỗ trợ bởi các Key Result khác.
 */
@Entity({ name: 'key_result_supports' })
export class KeyResultSupport {
  /**
   * ID của Key Result chính (được hỗ trợ).
   * Là một phần của khóa chính và không được phép null.
   */
  @PrimaryColumn({ name: 'parent_id', type: 'integer', nullable: false })
  parentId: number;

  /**
   * ID của Key Result phụ trợ (hỗ trợ cho parent_id).
   * Là một phần của khóa chính và không được phép null.
   */
  @PrimaryColumn({ name: 'child_id', type: 'integer', nullable: false })
  childId: number;
}
