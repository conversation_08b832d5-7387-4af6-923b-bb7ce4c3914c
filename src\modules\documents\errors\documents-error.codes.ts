import { HttpStatus } from '@nestjs/common';
import { ErrorCode } from '@/common/exceptions/app.exception';

/**
 * Mã lỗi cho module Documents (15000-15099)
 * Phạm vi: 15000-15099
 */
export const DOCUMENTS_ERROR_CODES = {
  // Document Folder Errors (15000-15019)

  /**
   * Lỗi khi không tìm thấy thư mục
   */
  FOLDER_NOT_FOUND: new ErrorCode(
    15000,
    'Không tìm thấy thư mục',
    HttpStatus.NOT_FOUND,
  ),

  /**
   * Lỗi khi thư mục cha không hoạt động
   */
  PARENT_FOLDER_INACTIVE: new ErrorCode(
    15001,
    'Thư mục cha không hoạt động',
    HttpStatus.BAD_REQUEST,
  ),

  /**
   * Lỗi khi thư mục cha không tồn tại
   */
  PARENT_FOLDER_NOT_FOUND: new ErrorCode(
    15002,
    '<PERSON><PERSON><PERSON> mục cha không tồn tại',
    HttpStatus.NOT_FOUND,
  ),

  /**
   * Lỗi khi tạo tham chiếu vòng trong cấu trúc thư mục
   */
  CIRCULAR_REFERENCE: new ErrorCode(
    15003,
    'Thư mục không thể là cha của chính nó',
    HttpStatus.BAD_REQUEST,
  ),

  /**
   * Lỗi khi thư mục có chứa thư mục con
   */
  FOLDER_HAS_CHILDREN: new ErrorCode(
    15004,
    'Không thể xóa thư mục có chứa thư mục con',
    HttpStatus.BAD_REQUEST,
  ),

  /**
   * Lỗi khi thư mục có chứa tài liệu
   */
  FOLDER_HAS_DOCUMENTS: new ErrorCode(
    15005,
    'Không thể xóa thư mục có chứa tài liệu',
    HttpStatus.BAD_REQUEST,
  ),

  /**
   * Lỗi khi thư mục không hoạt động
   */
  FOLDER_INACTIVE: new ErrorCode(
    15006,
    'Thư mục không hoạt động',
    HttpStatus.BAD_REQUEST,
  ),

  // Document Errors (15020-15049)

  /**
   * Lỗi khi không tìm thấy tài liệu
   */
  DOCUMENT_NOT_FOUND: new ErrorCode(
    15020,
    'Không tìm thấy tài liệu',
    HttpStatus.NOT_FOUND,
  ),

  /**
   * Lỗi khi S3 key của tài liệu đã tồn tại
   */
  DOCUMENT_S3_KEY_EXISTS: new ErrorCode(
    15021,
    'Tài liệu với S3 key này đã tồn tại',
    HttpStatus.CONFLICT,
  ),

  /**
   * Lỗi khi format tags không hợp lệ
   */
  INVALID_TAGS_FORMAT: new ErrorCode(
    15022,
    'Format tags không hợp lệ',
    HttpStatus.BAD_REQUEST,
  ),

  /**
   * Lỗi khi format metadata không hợp lệ
   */
  INVALID_METADATA_FORMAT: new ErrorCode(
    15023,
    'Format metadata không hợp lệ',
    HttpStatus.BAD_REQUEST,
  ),

  /**
   * Lỗi khi tài liệu đang được xử lý
   */
  DOCUMENT_PROCESSING: new ErrorCode(
    15024,
    'Tài liệu đang được xử lý',
    HttpStatus.CONFLICT,
  ),

  /**
   * Lỗi khi xử lý tài liệu thất bại
   */
  DOCUMENT_PROCESSING_FAILED: new ErrorCode(
    15025,
    'Xử lý tài liệu thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  /**
   * Lỗi khi tài liệu đã đạt giới hạn thử lại
   */
  DOCUMENT_RETRY_LIMIT_EXCEEDED: new ErrorCode(
    15026,
    'Tài liệu đã đạt giới hạn thử lại',
    HttpStatus.BAD_REQUEST,
  ),

  // Permission Errors (15050-15069)

  /**
   * Lỗi khi target phân quyền không hợp lệ
   */
  INVALID_PERMISSION_TARGET: new ErrorCode(
    15050,
    'Phải chỉ định đúng một target: userId, roleId hoặc departmentId',
    HttpStatus.BAD_REQUEST,
  ),

  /**
   * Lỗi khi quyền đã tồn tại
   */
  PERMISSION_ALREADY_EXISTS: new ErrorCode(
    15051,
    'Quyền cho target này đã tồn tại',
    HttpStatus.CONFLICT,
  ),

  /**
   * Lỗi khi không tìm thấy quyền
   */
  PERMISSION_NOT_FOUND: new ErrorCode(
    15052,
    'Không tìm thấy quyền',
    HttpStatus.NOT_FOUND,
  ),

  /**
   * Lỗi khi quyền đã hết hạn
   */
  PERMISSION_EXPIRED: new ErrorCode(
    15053,
    'Quyền đã hết hạn',
    HttpStatus.FORBIDDEN,
  ),

  /**
   * Lỗi khi không có quyền truy cập
   */
  ACCESS_DENIED: new ErrorCode(
    15054,
    'Không có quyền truy cập tài liệu này',
    HttpStatus.FORBIDDEN,
  ),

  /**
   * Lỗi khi cấp độ quyền không đủ
   */
  INSUFFICIENT_PERMISSION: new ErrorCode(
    15055,
    'Cấp độ quyền không đủ để thực hiện hành động này',
    HttpStatus.FORBIDDEN,
  ),

  // OpenAI Integration Errors (15070-15089)

  /**
   * Lỗi khi không tìm thấy Vector Store
   */
  VECTOR_STORE_NOT_FOUND: new ErrorCode(
    15070,
    'Không tìm thấy Vector Store',
    HttpStatus.NOT_FOUND,
  ),

  /**
   * Lỗi khi Vector Store đã hết hạn
   */
  VECTOR_STORE_EXPIRED: new ErrorCode(
    15071,
    'Vector Store đã hết hạn',
    HttpStatus.BAD_REQUEST,
  ),

  /**
   * Lỗi khi không thể kết nối với OpenAI
   */
  OPENAI_CONNECTION_ERROR: new ErrorCode(
    15072,
    'Không thể kết nối với OpenAI',
    HttpStatus.SERVICE_UNAVAILABLE,
  ),

  /**
   * Lỗi khi OpenAI file không tồn tại
   */
  OPENAI_FILE_NOT_FOUND: new ErrorCode(
    15073,
    'File OpenAI không tồn tại',
    HttpStatus.NOT_FOUND,
  ),

  /**
   * Lỗi khi upload file lên OpenAI thất bại
   */
  OPENAI_UPLOAD_FAILED: new ErrorCode(
    15074,
    'Upload file lên OpenAI thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  // General Errors (15090-15099)

  /**
   * Lỗi khi file không được hỗ trợ
   */
  UNSUPPORTED_FILE_TYPE: new ErrorCode(
    15090,
    'Loại file không được hỗ trợ',
    HttpStatus.BAD_REQUEST,
  ),

  /**
   * Lỗi khi file quá lớn
   */
  FILE_TOO_LARGE: new ErrorCode(15091, 'File quá lớn', HttpStatus.BAD_REQUEST),

  /**
   * Lỗi khi file bị hỏng
   */
  FILE_CORRUPTED: new ErrorCode(
    15092,
    'File bị hỏng hoặc không thể đọc',
    HttpStatus.BAD_REQUEST,
  ),

  /**
   * Lỗi khi không thể trích xuất text từ file
   */
  TEXT_EXTRACTION_FAILED: new ErrorCode(
    15093,
    'Không thể trích xuất text từ file',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  /**
   * Lỗi khi tìm kiếm thất bại
   */
  SEARCH_FAILED: new ErrorCode(
    15094,
    'Tìm kiếm thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
};
