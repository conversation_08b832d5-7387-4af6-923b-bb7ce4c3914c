// Test script để login và test API employees với token thực
const http = require('http');

// Cấu hình test
const BASE_URL = 'http://localhost:3001';

// Function để gọi API
function makeRequest(path, method = 'GET', data = null, token = null) {
  return new Promise((resolve, reject) => {
    const url = new URL(BASE_URL + path);
    const options = {
      hostname: url.hostname,
      port: url.port,
      path: url.pathname + url.search,
      method: method,
      headers: {
        'Content-Type': 'application/json',
      }
    };

    if (token) {
      options.headers['Authorization'] = `Bearer ${token}`;
    }

    if (data) {
      const postData = JSON.stringify(data);
      options.headers['Content-Length'] = Buffer.byteLength(postData);
    }

    const req = http.request(options, (res) => {
      let responseData = '';
      
      res.on('data', (chunk) => {
        responseData += chunk;
      });
      
      res.on('end', () => {
        try {
          const parsedData = JSON.parse(responseData);
          resolve({
            status: res.statusCode,
            headers: res.headers,
            data: parsedData
          });
        } catch (e) {
          resolve({
            status: res.statusCode,
            headers: res.headers,
            data: responseData
          });
        }
      });
    });

    req.on('error', (err) => {
      reject(err);
    });

    if (data) {
      req.write(JSON.stringify(data));
    }
    
    req.end();
  });
}

// Test cases
async function runTests() {
  console.log('🚀 Bắt đầu test Login và API Employees...\n');

  let authToken = null;

  // Test 1: Login để lấy token
  console.log('🔐 Test 1: Login để lấy token');
  try {
    const loginData = {
      email: '<EMAIL>', // Thay đổi email phù hợp
      password: 'password123'     // Thay đổi password phù hợp
    };
    
    const response = await makeRequest('/v1/auth/user/login', 'POST', loginData);
    console.log(`Status: ${response.status}`);
    
    if (response.status === 200 && response.data.data?.accessToken) {
      authToken = response.data.data.accessToken;
      console.log('✅ Login thành công! Token đã được lấy.');
      console.log(`Token preview: ${authToken.substring(0, 50)}...`);
    } else {
      console.log('❌ Login failed:', response.data);
      console.log('💡 Hãy kiểm tra email/password hoặc tạo user test trước');
      return;
    }
  } catch (error) {
    console.log('❌ Login error:', error.message);
    return;
  }
  console.log('');

  if (!authToken) {
    console.log('❌ Không có token, dừng test');
    return;
  }

  // Test 2: GET employees without search
  console.log('📋 Test 2: GET /v1/api/hrm/employees (no search)');
  try {
    const response = await makeRequest('/v1/api/hrm/employees?page=1&limit=5', 'GET', null, authToken);
    console.log(`Status: ${response.status}`);
    if (response.status === 200) {
      console.log(`✅ Success! Found ${response.data.data?.meta?.totalItems || 0} employees`);
      if (response.data.data?.items?.length > 0) {
        console.log('Sample employees:');
        response.data.data.items.forEach((emp, index) => {
          console.log(`  ${index + 1}. ${emp.employeeCode} - ${emp.employeeName}`);
        });
      }
    } else {
      console.log('❌ Failed:', response.data);
    }
  } catch (error) {
    console.log('❌ Error:', error.message);
  }
  console.log('');

  // Test 3: Search by employee code
  console.log('🔍 Test 3: Search by employee code (REDAI)');
  try {
    const response = await makeRequest('/v1/api/hrm/employees?page=1&limit=5&search=REDAI', 'GET', null, authToken);
    console.log(`Status: ${response.status}`);
    if (response.status === 200) {
      console.log(`✅ Search results: ${response.data.data?.meta?.totalItems || 0} employees found`);
      if (response.data.data?.items?.length > 0) {
        response.data.data.items.forEach((emp, index) => {
          console.log(`  ${index + 1}. ${emp.employeeCode} - ${emp.employeeName}`);
        });
      }
    } else {
      console.log('❌ Failed:', response.data);
    }
  } catch (error) {
    console.log('❌ Error:', error.message);
  }
  console.log('');

  // Test 4: Search by employee name
  console.log('🔍 Test 4: Search by employee name');
  try {
    const response = await makeRequest('/v1/api/hrm/employees?page=1&limit=5&search=test', 'GET', null, authToken);
    console.log(`Status: ${response.status}`);
    if (response.status === 200) {
      console.log(`✅ Search results: ${response.data.data?.meta?.totalItems || 0} employees found`);
      if (response.data.data?.items?.length > 0) {
        response.data.data.items.forEach((emp, index) => {
          console.log(`  ${index + 1}. ${emp.employeeCode} - ${emp.employeeName}`);
        });
      }
    } else {
      console.log('❌ Failed:', response.data);
    }
  } catch (error) {
    console.log('❌ Error:', error.message);
  }
  console.log('');

  // Test 5: Create a test employee để test search
  console.log('➕ Test 5: Create test employee');
  try {
    const employeeData = {
      employeeName: 'Nguyễn Văn Test',
      email: '<EMAIL>',
      status: 'active'
    };
    
    const response = await makeRequest('/v1/api/hrm/employees', 'POST', employeeData, authToken);
    console.log(`Status: ${response.status}`);
    if (response.status === 201) {
      console.log('✅ Employee created successfully!');
      console.log(`Created: ${response.data.data.employeeCode} - ${response.data.data.employeeName}`);
    } else {
      console.log('❌ Failed to create employee:', response.data);
    }
  } catch (error) {
    console.log('❌ Error:', error.message);
  }

  console.log('\n🏁 Test hoàn thành!');
}

// Chạy tests
runTests().catch(console.error);
