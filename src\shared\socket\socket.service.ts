import { Injectable, Logger } from '@nestjs/common';
import { Server, Socket } from 'socket.io';
import { SocketClient } from './interfaces/socket-client.interface';
import { SocketEvents } from './events/socket-events.enum';
import { JwtPayload } from '@/modules/auth/guards/jwt.util';

@Injectable()
export class SocketService {
  private readonly logger = new Logger(SocketService.name);
  private server: Server;
  private readonly connectedClients: Map<string, SocketClient> = new Map();
  private readonly userSocketMap: Map<string, Set<string>> = new Map();
  private readonly roomUserMap: Map<string, Set<string>> = new Map();

  /**
   * Thiết lập server Socket.IO
   * @param server Server Socket.IO
   */
  setServer(server: Server) {
    this.server = server;
  }

  /**
   * Lấy server Socket.IO
   * @returns Server Socket.IO
   */
  getServer(): Server {
    return this.server;
  }

  /**
   * Thêm client vào danh sách kết nối
   * @param client Client Socket.IO
   * @param user Thông tin người dùng
   */
  addClient(client: SocketClient, user?: JwtPayload) {
    this.connectedClients.set(client.id, client);

    if (user && user.id) {
      // Lưu mapping giữa userId và socketId
      const userId = user.id.toString();
      if (!this.userSocketMap.has(userId)) {
        this.userSocketMap.set(userId, new Set());
      }
      this.userSocketMap.get(userId)?.add(client.id);

      client.user = user;
      client.connectedAt = new Date();
      client.lastActiveAt = new Date();
      client.status = 'online';

      this.logger.log(`Client connected: ${client.id} (User: ${user.id})`);
    } else {
      this.logger.log(`Anonymous client connected: ${client.id}`);
    }
  }

  /**
   * Xóa client khỏi danh sách kết nối
   * @param clientId ID của client
   */
  removeClient(clientId: string) {
    const client = this.connectedClients.get(clientId);
    if (client && client.user && client.user.id) {
      // Xóa khỏi userSocketMap
      const userId = client.user.id.toString();
      const userSockets = this.userSocketMap.get(userId);
      if (userSockets) {
        userSockets.delete(clientId);
        if (userSockets.size === 0) {
          this.userSocketMap.delete(userId);
        }
      }

      this.logger.log(
        `Client disconnected: ${clientId} (User: ${client.user.id})`,
      );
    } else {
      this.logger.log(`Anonymous client disconnected: ${clientId}`);
    }

    this.connectedClients.delete(clientId);
  }

  /**
   * Thêm người dùng vào phòng
   * @param roomId ID của phòng
   * @param userId ID của người dùng
   */
  addUserToRoom(roomId: string, userId: string) {
    if (!this.roomUserMap.has(roomId)) {
      this.roomUserMap.set(roomId, new Set());
    }
    this.roomUserMap.get(roomId)?.add(userId);
    this.logger.log(`User ${userId} joined room ${roomId}`);
  }

  /**
   * Xóa người dùng khỏi phòng
   * @param roomId ID của phòng
   * @param userId ID của người dùng
   */
  removeUserFromRoom(roomId: string, userId: string) {
    const roomUsers = this.roomUserMap.get(roomId);
    if (roomUsers) {
      roomUsers.delete(userId);
      if (roomUsers.size === 0) {
        this.roomUserMap.delete(roomId);
      }
      this.logger.log(`User ${userId} left room ${roomId}`);
    }
  }

  /**
   * Gửi tin nhắn đến người dùng cụ thể
   * @param userId ID của người dùng
   * @param event Tên sự kiện
   * @param data Dữ liệu gửi đi
   */
  sendToUser(userId: string, event: string, data: any) {
    const socketIds = this.userSocketMap.get(userId);
    if (!socketIds || socketIds.size === 0) {
      this.logger.warn(`User ${userId} is not connected`);
      return;
    }

    socketIds.forEach((socketId) => {
      this.server.to(socketId).emit(event, {
        event,
        data,
        timestamp: Date.now(),
      });
    });

    this.logger.debug(`Sent event ${event} to user ${userId}`);
  }

  /**
   * Gửi tin nhắn đến phòng cụ thể
   * @param roomId ID của phòng
   * @param event Tên sự kiện
   * @param data Dữ liệu gửi đi
   * @param excludeUserId ID của người dùng bị loại trừ (không nhận tin nhắn)
   */
  sendToRoom(roomId: string, event: string, data: any, excludeUserId?: string) {
    if (excludeUserId) {
      // Gửi đến tất cả người dùng trong phòng trừ người gửi
      this.server
        .to(roomId)
        .except(this.getSocketIdsOfUser(excludeUserId))
        .emit(event, {
          event,
          data,
          timestamp: Date.now(),
          roomId,
        });
    } else {
      // Gửi đến tất cả người dùng trong phòng
      this.server.to(roomId).emit(event, {
        event,
        data,
        timestamp: Date.now(),
        roomId,
      });
    }

    this.logger.debug(`Sent event ${event} to room ${roomId}`);
  }

  /**
   * Gửi tin nhắn đến tất cả người dùng
   * @param event Tên sự kiện
   * @param data Dữ liệu gửi đi
   */
  broadcastToAll(event: string, data: any) {
    this.server.emit(event, {
      event,
      data,
      timestamp: Date.now(),
    });

    this.logger.debug(`Broadcasted event ${event} to all users`);
  }

  /**
   * Lấy danh sách người dùng trong phòng
   * @param roomId ID của phòng
   * @returns Danh sách ID người dùng
   */
  getUsersInRoom(roomId: string): string[] {
    const users = this.roomUserMap.get(roomId);
    return users ? Array.from(users) : [];
  }

  /**
   * Lấy danh sách phòng của người dùng
   * @param userId ID của người dùng
   * @returns Danh sách ID phòng
   */
  getRoomsOfUser(userId: string): string[] {
    const rooms: string[] = [];
    this.roomUserMap.forEach((users, roomId) => {
      if (users.has(userId)) {
        rooms.push(roomId);
      }
    });
    return rooms;
  }

  /**
   * Lấy danh sách socket ID của người dùng
   * @param userId ID của người dùng
   * @returns Danh sách socket ID
   */
  getSocketIdsOfUser(userId: string): string[] {
    const socketIds = this.userSocketMap.get(userId);
    return socketIds ? Array.from(socketIds) : [];
  }

  /**
   * Lấy thông tin người dùng từ socket ID
   * @param socketId ID của socket
   * @returns Thông tin người dùng
   */
  getUserFromSocketId(socketId: string): JwtPayload | undefined {
    return this.connectedClients.get(socketId)?.user;
  }

  /**
   * Cập nhật trạng thái của người dùng
   * @param userId ID của người dùng
   * @param status Trạng thái mới
   */
  updateUserStatus(
    userId: string,
    status: 'online' | 'away' | 'busy' | 'offline',
  ) {
    const socketIds = this.userSocketMap.get(userId);
    if (!socketIds) return;

    socketIds.forEach((socketId) => {
      const client = this.connectedClients.get(socketId);
      if (client) {
        client.status = status;
        client.lastActiveAt = new Date();
      }
    });

    // Thông báo cho tất cả người dùng về sự thay đổi trạng thái
    this.broadcastToAll(SocketEvents.USER_STATUS_CHANGED, {
      userId,
      status,
      timestamp: Date.now(),
    });
  }
}
