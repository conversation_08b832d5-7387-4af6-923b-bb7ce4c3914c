import { Entity, PrimaryGeneratedColumn, Column } from 'typeorm';
import { OkrCycleStatus } from '../enum/okr-cycle-status.enum';

/**
 * Entity representing OKR cycles (e.g., quarters, years)
 */
@Entity('okr_cycles')
export class OkrCycle {
  /**
   * Unique identifier for the OKR cycle
   */
  @PrimaryGeneratedColumn()
  id: number;

  /**
   * Cycle name (e.g., "Q1-2025", "2025", "H1-2025")
   */
  @Column({ type: 'varchar', length: 255, nullable: false })
  name: string;

  /**
   * Cycle start date
   */
  @Column({ name: 'start_date', type: 'date', nullable: false })
  startDate: Date;

  /**
   * Cycle end date
   */
  @Column({ name: 'end_date', type: 'date', nullable: false })
  endDate: Date;

  /**
   * Cycle status (active, closed, planning)
   */
  @Column({ type: 'enum', enum: OkrCycleStatus, nullable: true })
  status: OkrCycleStatus | null;

  /**
   * ID of the user who created the cycle
   */
  @Column({ name: 'created_by', type: 'integer', nullable: true })
  createdBy: number | null;

  /**
   * Creation timestamp (in milliseconds)
   */
  @Column({ name: 'created_at', type: 'bigint', nullable: true })
  createdAt: number | null;

  /**
   * ID of the company/organization that owns this record
   */
  @Column({ name: 'tenant_id', type: 'integer', nullable: true })
  tenantId: number | null;
}
