import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { AxiosRequestConfig } from 'axios';
import { BaseMarketingAiServiceImpl } from '../base-marketing-ai.service';
import {
  VideoGenerationOptions,
  VideoGenerationResult,
  VideoGenerationService,
  VideoResolution,
  // VideoFormat, // Unused import
  VideoStyle,
  MarketingAiResponse,
} from '../interfaces';

/**
 * Pika Labs service for video generation
 */
@Injectable()
export class PikaLabsService
  extends BaseMarketingAiServiceImpl
  implements VideoGenerationService
{
  readonly serviceName = 'Pika Labs';
  protected readonly baseUrl = 'https://api.pika.art/v1';
  protected readonly apiKey: string | undefined;

  constructor(private readonly configService: ConfigService) {
    super(PikaLabsService.name);
    this.apiKey = this.configService.get<string>('PIKA_API_KEY');

    if (!this.apiKey) {
      this.logger.warn('PIKA_API_KEY is not defined in environment variables');
    }
  }

  /**
   * Test the connection to the Pika Labs API
   * @returns A promise that resolves to a boolean indicating if the connection was successful
   */
  async testConnection(): Promise<boolean> {
    try {
      const url = `${this.baseUrl}/user`;
      const config = this.createRequestConfig();

      const response = await this.axiosInstance.get(url, config);
      return response.status === 200;
    } catch (error) {
      this.logger.error(
        `Connection test failed: ${error.message}`,
        error.stack,
      );
      return false;
    }
  }

  /**
   * Create a request configuration with authentication
   * @param config Additional request configuration
   * @returns Request configuration with authentication
   */
  protected createRequestConfig(
    config?: AxiosRequestConfig,
  ): AxiosRequestConfig {
    if (!this.apiKey) {
      throw new Error('Pika Labs API key is not defined');
    }

    return {
      ...config,
      headers: {
        ...config?.headers,
        Authorization: `Bearer ${this.apiKey}`,
        'Content-Type': 'application/json',
      },
    };
  }

  /**
   * Map resolution option to Pika Labs dimensions
   * @param resolution Resolution option
   * @returns Object with width and height
   */
  private mapResolutionToPikaDimensions(
    resolution: VideoResolution = VideoResolution.HD,
  ): { width: number; height: number } {
    switch (resolution) {
      case VideoResolution.SD:
        return { width: 640, height: 360 };
      case VideoResolution.HD:
        return { width: 1280, height: 720 };
      case VideoResolution.FULL_HD:
        return { width: 1920, height: 1080 };
      case VideoResolution.ULTRA_HD:
        // Pika might not support 4K, so we'll use Full HD
        return { width: 1920, height: 1080 };
      default:
        return { width: 1280, height: 720 };
    }
  }

  /**
   * Map style option to Pika Labs style
   * @param style Style option
   * @returns Pika Labs style
   */
  private mapStyleToPikaStyle(style?: VideoStyle): string {
    switch (style) {
      case VideoStyle.REALISTIC:
        return 'realistic';
      case VideoStyle.ANIMATED:
        return 'animated';
      case VideoStyle.CINEMATIC:
        return 'cinematic';
      case VideoStyle.DOCUMENTARY:
        return 'documentary';
      case VideoStyle.COMMERCIAL:
        return 'commercial';
      case VideoStyle.SOCIAL_MEDIA:
        return 'social_media';
      default:
        return 'cinematic';
    }
  }

  /**
   * Generate a video from a text prompt using Pika Labs
   * @param prompt Text prompt to generate video from
   * @param options Options for video generation
   * @returns A promise that resolves to a response containing the generated video
   */
  async generateVideo(
    prompt: string,
    options?: VideoGenerationOptions,
  ): Promise<MarketingAiResponse<VideoGenerationResult>> {
    try {
      const { width, height } = this.mapResolutionToPikaDimensions(
        options?.resolution,
      );

      const duration = options?.duration || 15;
      const style = this.mapStyleToPikaStyle(options?.style);

      // Pika Labs API endpoint for text-to-video
      const url = `${this.baseUrl}/generate`;

      const data = {
        prompt,
        negative_prompt: options?.referenceImageUrl
          ? ''
          : 'low quality, blurry, distorted',
        width,
        height,
        duration,
        style,
        fps: 30,
      };

      const config = this.createRequestConfig({
        timeout: options?.timeout || 300000, // 5 minutes timeout for video generation
      });

      // Start the generation
      const response = await this.axiosInstance.post(url, data, config);

      const jobId = response.data.id;

      // Return the job ID for status checking
      const result: VideoGenerationResult = {
        videoUrl: '',
        duration,
        prompt,
        status: 'processing',
        jobId,
      };

      return this.createSuccessResponse(result, response.data);
    } catch (error) {
      return this.handleApiError<VideoGenerationResult>(
        error,
        'Pika Labs video generation',
      );
    }
  }

  /**
   * Check the status of a video generation job
   * @param jobId ID of the video generation job
   * @returns A promise that resolves to a response containing the status of the job
   */
  async checkVideoGenerationStatus(
    jobId: string,
  ): Promise<MarketingAiResponse<VideoGenerationResult>> {
    try {
      const url = `${this.baseUrl}/generations/${jobId}`;
      const config = this.createRequestConfig();

      const response = await this.axiosInstance.get(url, config);

      const status = response.data.status;
      let videoStatus: 'completed' | 'processing' | 'failed' = 'processing';

      if (status === 'completed') {
        videoStatus = 'completed';
      } else if (status === 'failed') {
        videoStatus = 'failed';
      }

      const result: VideoGenerationResult = {
        videoUrl: response.data.output?.url || '',
        thumbnailUrl: response.data.output?.thumbnail || '',
        duration: response.data.output?.duration || 0,
        prompt: response.data.input?.prompt || '',
        status: videoStatus,
        jobId,
      };

      return this.createSuccessResponse(result, response.data);
    } catch (error) {
      return this.handleApiError<VideoGenerationResult>(
        error,
        'Pika Labs video status check',
      );
    }
  }

  /**
   * Generate a video from an image and text prompt
   * @param imageUrl URL of the image to use as a reference
   * @param prompt Text prompt to guide the video generation
   * @param options Options for video generation
   * @returns A promise that resolves to a response containing the generated video
   */
  async generateVideoFromImage(
    imageUrl: string,
    prompt: string,
    options?: VideoGenerationOptions,
  ): Promise<MarketingAiResponse<VideoGenerationResult>> {
    try {
      const { width, height } = this.mapResolutionToPikaDimensions(
        options?.resolution,
      );

      const duration = options?.duration || 15;
      const style = this.mapStyleToPikaStyle(options?.style);

      // Pika Labs API endpoint for image-to-video
      const url = `${this.baseUrl}/image-to-video`;

      const data = {
        image_url: imageUrl,
        prompt,
        negative_prompt: 'low quality, blurry, distorted',
        width,
        height,
        duration,
        style,
        fps: 30,
      };

      const config = this.createRequestConfig({
        timeout: options?.timeout || 300000, // 5 minutes timeout for video generation
      });

      // Start the generation
      const response = await this.axiosInstance.post(url, data, config);

      const jobId = response.data.id;

      // Return the job ID for status checking
      const result: VideoGenerationResult = {
        videoUrl: '',
        duration,
        prompt,
        status: 'processing',
        jobId,
      };

      return this.createSuccessResponse(result, response.data);
    } catch (error) {
      return this.handleApiError<VideoGenerationResult>(
        error,
        'Pika Labs image-to-video generation',
      );
    }
  }

  /**
   * Edit an existing video using a text prompt
   * @param videoUrl URL of the video to edit
   * @param prompt Text prompt to guide the editing
   * @param options Options for video editing
   * @returns A promise that resolves to a response containing the edited video
   */
  async editVideo(
    videoUrl: string,
    prompt: string,
    options?: VideoGenerationOptions,
  ): Promise<MarketingAiResponse<VideoGenerationResult>> {
    try {
      const { width, height } = this.mapResolutionToPikaDimensions(
        options?.resolution,
      );

      const duration = options?.duration || 15;
      const style = this.mapStyleToPikaStyle(options?.style);

      // Pika Labs API endpoint for video editing
      const url = `${this.baseUrl}/edit-video`;

      const data = {
        video_url: videoUrl,
        prompt,
        negative_prompt: 'low quality, blurry, distorted',
        width,
        height,
        duration,
        style,
        fps: 30,
      };

      const config = this.createRequestConfig({
        timeout: options?.timeout || 300000, // 5 minutes timeout for video generation
      });

      // Start the generation
      const response = await this.axiosInstance.post(url, data, config);

      const jobId = response.data.id;

      // Return the job ID for status checking
      const result: VideoGenerationResult = {
        videoUrl: '',
        duration,
        prompt,
        status: 'processing',
        jobId,
      };

      return this.createSuccessResponse(result, response.data);
    } catch (error) {
      return this.handleApiError<VideoGenerationResult>(
        error,
        'Pika Labs video editing',
      );
    }
  }
}
