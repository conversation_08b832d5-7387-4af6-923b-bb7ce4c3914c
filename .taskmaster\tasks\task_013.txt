# Task ID: 13
# Title: Update Calendar Module - Event Repository
# Status: done
# Dependencies: 1
# Priority: medium
# Description: Add tenantId filtering to all Calendar Event repository methods
# Details:
Update EventRepository methods:
- findAll(): Add tenantId to QueryBuilder
- findById(): Add tenantId to WHERE condition
- findByDateRange(): Add tenantId filtering
- findByAttendeeId(): Add tenantId filtering
- create(): Set tenantId on new entities
- update(): Add tenantId to WHERE condition
- delete(): Add tenantId to WHERE condition
- getCalendarView(): Add tenantId to calendar queries

# Test Strategy:
Unit tests for Calendar Event repository
