import { Injectable } from '@nestjs/common';
import { google, Auth } from 'googleapis';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class GoogleApiService {
  private auth: Auth.OAuth2Client;

  constructor(private readonly configService: ConfigService) {
    // Khởi tạo OAuth2 client
    this.auth = new google.auth.OAuth2(
      this.configService.get<string>('GOOGLE_CLIENT_ID'),
      this.configService.get<string>('GOOGLE_CLIENT_SECRET'),
      this.configService.get<string>('GOOGLE_REDIRECT_URI'),
    );
  }

  /**
   * Tạo URL xác thực OAuth2
   * @param scopes Danh sách quyền truy cập
   * @param state State token để xác thực callback (tùy chọn)
   * @param redirectUri URL callback sau khi xác thực (tùy chọn)
   * @returns URL xác thực
   */
  generateAuthUrl(
    scopes: string[],
    state?: string,
    redirectUri?: string,
  ): string {
    const options: any = {
      access_type: 'offline',
      scope: scopes,
      prompt: 'consent',
    };

    if (state) {
      options.state = state;
    }

    if (redirectUri) {
      options.redirect_uri = redirectUri;
    }

    return this.auth.generateAuthUrl(options);
  }

  /**
   * Lấy token từ code xác thực
   * @param code Code xác thực từ callback
   * @param redirectUri URL callback (tùy chọn)
   * @returns Token
   */
  async getToken(
    code: string,
    redirectUri?: string,
  ): Promise<Auth.Credentials> {
    const options: any = { code };

    if (redirectUri) {
      options.redirect_uri = redirectUri;
    }

    const { tokens } = await this.auth.getToken(options);
    this.auth.setCredentials(tokens);
    return tokens;
  }

  /**
   * Thiết lập token cho client
   * @param tokens Token
   */
  setCredentials(tokens: Auth.Credentials): void {
    this.auth.setCredentials(tokens);
  }

  /**
   * Làm mới access token từ refresh token
   * @param refreshToken Refresh token
   * @returns Token mới
   */
  async refreshAccessToken(refreshToken: string): Promise<Auth.Credentials> {
    this.auth.setCredentials({ refresh_token: refreshToken });
    const { credentials } = await this.auth.refreshAccessToken();
    return credentials;
  }

  /**
   * Lấy thông tin người dùng Google
   * @returns Thông tin người dùng
   */
  async getUserInfo(): Promise<any> {
    const oauth2 = google.oauth2({
      auth: this.auth,
      version: 'v2',
    });

    const { data } = await oauth2.userinfo.get();
    return data;
  }

  /**
   * Lấy danh sách file từ Google Drive
   * @param folderId ID của thư mục (tùy chọn)
   * @returns Danh sách file
   */
  async listDriveFiles(folderId?: string): Promise<any> {
    const drive = google.drive({
      version: 'v3',
      auth: this.auth,
    });

    const query = folderId ? `'${folderId}' in parents` : undefined;
    const { data } = await drive.files.list({
      q: query,
      fields: 'files(id, name, mimeType, webViewLink, thumbnailLink)',
    });

    return data.files;
  }

  /**
   * Tải file lên Google Drive
   * @param fileMetadata Metadata của file
   * @param media Media của file
   * @returns Thông tin file đã tải lên
   */
  async uploadFileToDrive(fileMetadata: any, media: any): Promise<any> {
    const drive = google.drive({
      version: 'v3',
      auth: this.auth,
    });

    const { data } = await drive.files.create({
      requestBody: fileMetadata,
      media: media,
      fields: 'id, name, webViewLink',
    });

    return data;
  }

  /**
   * Lấy Google Calendar API
   * @returns Google Calendar API
   */
  getCalendarApi() {
    return google.calendar({
      version: 'v3',
      auth: this.auth,
    });
  }

  /**
   * Lấy Google Sheets API
   * @returns Google Sheets API
   */
  getSheetsApi() {
    return google.sheets({
      version: 'v4',
      auth: this.auth,
    });
  }

  /**
   * Lấy Google Gmail API
   * @returns Google Gmail API
   */
  getGmailApi() {
    return google.gmail({
      version: 'v1',
      auth: this.auth,
    });
  }

  /**
   * Lấy Google YouTube API
   * @returns Google YouTube API
   */
  getYouTubeApi() {
    return google.youtube({
      version: 'v3',
      auth: this.auth,
    });
  }
}
