import { Entity, PrimaryGeneratedColumn, Column } from 'typeorm';

/**
 * Entity representing roles in the system
 */
@Entity('roles')
export class Role {
  /**
   * Unique identifier for the role
   */
  @PrimaryGeneratedColumn()
  id: number;

  /**
   * Role name
   */
  @Column({ type: 'varchar', length: 255, nullable: false })
  name: string;

  /**
   * Detailed description of the role
   */
  @Column({ type: 'text', nullable: true })
  description: string | null;

  /**
   * Creation timestamp (in milliseconds)
   */
  @Column({ name: 'created_at', type: 'bigint', nullable: true })
  createdAt: number | null;

  /**
   * Role type
   */
  @Column({ type: 'varchar', length: 50, nullable: true, unique: true })
  type: string | null;

  /**
   * ID of the company/organization that owns this record
   */
  @Column({ name: 'tenant_id', type: 'integer', nullable: true })
  tenantId: number | null;
}
