import { <PERSON>, Get, Param, Logger } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { EmailSystemQueueService } from '@shared/queue/email-system-queue.service';
import { EmailService } from '../services/email.service';
import { ApiResponseDto } from '@/common/response';

@ApiTags('Email Debug')
@Controller('api/v1/email/debug')
export class EmailDebugController {
  private readonly logger = new Logger(EmailDebugController.name);

  constructor(
    private readonly emailSystemQueueService: EmailSystemQueueService,
    private readonly emailService: EmailService,
  ) {}

  /**
   * Kiểm tra trạng thái queue
   */
  @Get('queue/status')
  @ApiOperation({ summary: 'Kiểm tra trạng thái email queue' })
  @ApiResponse({ status: 200, description: 'Trạng thái queue' })
  async getQueueStatus() {
    try {
      const stats =
        await this.emailSystemQueueService.getEmailSystemQueueStats();
      return ApiResponseDto.success(stats);
    } catch (error) {
      this.logger.error(
        `Error getting queue status: ${error.message}`,
        error.stack,
      );
    }
  }

  /**
   * Kiểm tra trạng thái job cụ thể
   */
  @Get('job/:jobId')
  @ApiOperation({ summary: 'Kiểm tra trạng thái job cụ thể' })
  @ApiResponse({ status: 200, description: 'Trạng thái job' })
  async getJobStatus(@Param('jobId') jobId: string) {
    try {
      const jobStatus =
        await this.emailSystemQueueService.getEmailSystemJobStatus(jobId);
      return ApiResponseDto.success(jobStatus);
    } catch (error) {
      this.logger.error(
        `Error getting job status: ${error.message}`,
        error.stack,
      );
    }
  }

  /**
   * Kiểm tra kết nối SMTP
   */
  @Get('smtp/verify')
  @ApiOperation({ summary: 'Kiểm tra kết nối SMTP' })
  @ApiResponse({ status: 200, description: 'Kết quả kiểm tra SMTP' })
  async verifySmtpConnection() {
    try {
      const isConnected = await this.emailService.verifyConnection();
      return ApiResponseDto.success({
        connected: isConnected,
        message: isConnected
          ? 'SMTP connection successful'
          : 'SMTP connection failed',
      });
    } catch (error) {
      this.logger.error(`Error verifying SMTP: ${error.message}`, error.stack);
      return ApiResponseDto.success({
        connected: false,
        message: `SMTP verification failed: ${error.message}`,
      });
    }
  }

  /**
   * Test gửi email đơn giản
   */
  @Get('test/send')
  @ApiOperation({ summary: 'Test gửi email đơn giản' })
  @ApiResponse({ status: 200, description: 'Kết quả test email' })
  async testSendEmail() {
    try {
      const result = await this.emailService.sendEmail({
        to: '<EMAIL>',
        subject: 'Test Email from RedAI',
        body: '<h1>Test Email</h1><p>This is a test email from RedAI system.</p>',
      });

      return ApiResponseDto.success({
        success: true,
        result,
        message: 'Test email sent successfully',
      });
    } catch (error) {
      this.logger.error(
        `Error sending test email: ${error.message}`,
        error.stack,
      );
      return ApiResponseDto.success({
        success: false,
        error: error.message,
        message: 'Test email failed',
      });
    }
  }
}
