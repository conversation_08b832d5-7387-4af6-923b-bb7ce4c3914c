# Tenant Audit Checklist - Manual TenantId Implementation

## 📋 **Repository Files Audit**

### ✅ **OKRs Module**
- [ ] `src/modules/okrs/repositories/objective.repository.ts` - **HAS tenantId** ⚠️ NEEDS UPDATE
- [ ] `src/modules/okrs/repositories/okr-cycle.repository.ts` - **HAS tenantId** ⚠️ NEEDS UPDATE  
- [ ] `src/modules/okrs/repositories/key-result.repository.ts` - **NO tenantId** ✅ SKIP
- [ ] `src/modules/okrs/repositories/key-result-update.repository.ts` - **NO tenantId** ✅ SKIP
- [ ] `src/modules/okrs/repositories/key-result-support.repository.ts` - **NO tenantId** ✅ SKIP

### ✅ **Todolists Module**  
- [ ] `src/modules/todolists/repositories/project.repository.ts` - **HAS tenantId** ⚠️ NEEDS UPDATE
- [ ] `src/modules/todolists/repositories/project-member.repository.ts` - **NO tenantId** ✅ SKIP
- [ ] `src/modules/todolists/repositories/todo.repository.ts` - **HAS tenantId** ⚠️ NEEDS UPDATE
- [ ] `src/modules/todolists/repositories/task-kr.repository.ts` - **NO tenantId** ✅ SKIP
- [ ] `src/modules/todolists/repositories/todo-tag.repository.ts` - **NO tenantId** ✅ SKIP
- [ ] `src/modules/todolists/repositories/todo-score.repository.ts` - **NO tenantId** ✅ SKIP
- [ ] `src/modules/todolists/repositories/todo-collaborator.repository.ts` - **NO tenantId** ✅ SKIP
- [ ] `src/modules/todolists/repositories/todo-attachment.repository.ts` - **NO tenantId** ✅ SKIP
- [ ] `src/modules/todolists/repositories/todo-comment.repository.ts` - **NO tenantId** ✅ SKIP

### ✅ **HRM Module**
- [ ] `src/modules/hrm/employees/repositories/employee.repository.ts` - **HAS tenantId** ⚠️ NEEDS UPDATE
- [ ] `src/modules/hrm/org-units/repositories/department.repository.ts` - **HAS tenantId** ⚠️ NEEDS UPDATE
- [ ] `src/modules/hrm/contracts/repositories/contract.repository.ts` - **HAS tenantId** ⚠️ NEEDS UPDATE

### ✅ **Calendar Module**
- [ ] `src/modules/calendar/repositories/calendar.repository.ts` - **HAS tenantId** ⚠️ NEEDS UPDATE

### ✅ **Chat Module**
- [ ] `src/modules/chat/repositories/facebook-page-config.repository.ts` - **HAS tenantId** ⚠️ NEEDS UPDATE
- [ ] `src/modules/chat/repositories/chat-conversation.repository.ts` - **MISSING FILE** ❌ CREATE
- [ ] `src/modules/chat/repositories/chat-message.repository.ts` - **MISSING FILE** ❌ CREATE

### ✅ **System Module**
- [ ] **NO REPOSITORIES FOUND** - Module chỉ có RedisTestController

### ✅ **Email Module**  
- [ ] **NO REPOSITORIES FOUND** - Module chỉ có services

## 📋 **Entity Files with TenantId**

### ✅ **Entities WITH tenantId (Need Repository Updates)**
- [ ] `src/modules/okrs/entities/objective.entity.ts` - **HAS tenantId**
- [ ] `src/modules/okrs/entities/okr-cycle.entity.ts` - **HAS tenantId**
- [ ] `src/modules/todolists/entities/project.entity.ts` - **HAS tenantId**
- [ ] `src/modules/todolists/entities/todo.entity.ts` - **HAS tenantId**
- [ ] `src/modules/hrm/employees/entities/employee.entity.ts` - **HAS tenantId**
- [ ] `src/modules/hrm/org-units/entities/department.entity.ts` - **HAS tenantId**
- [ ] `src/modules/hrm/contracts/entities/contract.entity.ts` - **HAS tenantId**
- [ ] `src/modules/calendar/entities/calendar.entity.ts` - **HAS tenantId**
- [ ] `src/modules/chat/entities/chat-conversation.entity.ts` - **HAS tenantId**
- [ ] `src/modules/chat/entities/chat-message.entity.ts` - **HAS tenantId**
- [ ] `src/modules/chat/entities/facebook-page-config.entity.ts` - **HAS tenantId**

### ✅ **Entities WITHOUT tenantId (Skip)**
- [ ] `src/modules/okrs/entities/key-result.entity.ts` - **NO tenantId**
- [ ] `src/modules/okrs/entities/key-result-update.entity.ts` - **NO tenantId**
- [ ] `src/modules/okrs/entities/key-result-support.entity.ts` - **NO tenantId**
- [ ] `src/modules/todolists/entities/project-members.entity.ts` - **NO tenantId**
- [ ] `src/modules/todolists/entities/task-kr.entity.ts` - **NO tenantId**
- [ ] Other todo-related entities without tenantId

## 📋 **Service Files Audit**

### ✅ **Services Need Updates (Pass tenantId to repositories)**
- [ ] `src/modules/okrs/services/objective.service.ts` - Update to pass tenantId
- [ ] `src/modules/okrs/services/okr-cycle.service.ts` - Update to pass tenantId
- [ ] `src/modules/todolists/services/project.service.ts` - Update to pass tenantId
- [ ] `src/modules/todolists/services/todo.service.ts` - Update to pass tenantId
- [ ] `src/modules/hrm/employees/services/employee.service.ts` - Update to pass tenantId
- [ ] `src/modules/hrm/org-units/services/department.service.ts` - Update to pass tenantId
- [ ] `src/modules/hrm/contracts/services/contract.service.ts` - Update to pass tenantId
- [ ] `src/modules/calendar/services/calendar.service.ts` - Update to pass tenantId
- [ ] `src/modules/chat/services/facebook.service.ts` - Update to pass tenantId

## 📋 **Controller Files Audit**

### ✅ **Controllers Need Updates (Add @CurrentTenant())**
- [ ] `src/modules/okrs/controllers/objective.controller.ts` - Add @CurrentTenant()
- [ ] `src/modules/okrs/controllers/okr-cycle.controller.ts` - Add @CurrentTenant()
- [ ] `src/modules/todolists/controllers/project.controller.ts` - Add @CurrentTenant()
- [ ] `src/modules/todolists/controllers/todo.controller.ts` - Add @CurrentTenant()
- [ ] `src/modules/hrm/employees/controllers/employee.controller.ts` - Add @CurrentTenant()
- [ ] `src/modules/hrm/org-units/controllers/department.controller.ts` - Add @CurrentTenant()
- [ ] `src/modules/hrm/contracts/controllers/contract.controller.ts` - Add @CurrentTenant()
- [ ] `src/modules/calendar/controllers/calendar.controller.ts` - Add @CurrentTenant()
- [ ] `src/modules/chat/controllers/facebook-integration.controller.ts` - Add @CurrentTenant()

## 📋 **Priority Implementation Order**

### 🔥 **Phase 1: Critical Business Data (Week 1)**
1. **OKRs Module** (Tasks 2-6)
   - ObjectiveRepository ✅ STARTED
   - OkrCycleRepository  
   - Services & Controllers

2. **Todolists Module** (Tasks 7-9)
   - TodoRepository
   - ProjectRepository
   - Services & Controllers

### 🔥 **Phase 2: Supporting Data (Week 2)**
3. **HRM Module** (Tasks 10-12)
   - EmployeeRepository
   - DepartmentRepository
   - ContractRepository
   - Services & Controllers

4. **Calendar Module** (Tasks 13-14)
   - CalendarRepository
   - Services & Controllers

### 🔥 **Phase 3: Communication Data (Week 3)**
5. **Chat Module** (Tasks 15-16)
   - Create missing repositories
   - FacebookPageConfigRepository
   - Services & Controllers

### 🔥 **Phase 4: Testing & Optimization (Week 4)**
6. **Entity Relationships** (Task 22)
7. **Comprehensive Testing** (Task 23)
8. **Performance Optimization** (Task 24)
9. **Documentation** (Task 25)

## 📋 **Missing Files to Create**
- [ ] `src/modules/chat/repositories/chat-conversation.repository.ts`
- [ ] `src/modules/chat/repositories/chat-message.repository.ts`

## 📋 **Files to Clean Up**
- [x] `src/common/decorators/auto-tenant.decorator.ts` - **DELETED**
- [x] `src/common/repositories/base-tenant.repository.ts` - **DELETED**

## 📋 **Current Status**
- **Total Repositories**: 11 need updates + 2 need creation = 13 total
- **Total Services**: 9 need updates
- **Total Controllers**: 9 need updates
- **Completed**: ObjectiveRepository partially updated
- **Next**: Complete ObjectiveRepository, then OkrCycleRepository
