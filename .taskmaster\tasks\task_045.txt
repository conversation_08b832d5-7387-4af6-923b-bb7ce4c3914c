# Task ID: 45
# Title: <PERSON><PERSON><PERSON><PERSON>p Database Schema cho Module Documents
# Status: done
# Dependencies: None
# Priority: high
# Description: Tạo database schema hoàn chỉnh cho module quản lý tài liệu với tenant isolation và OpenAI integration
# Details:
Tạo các migration files cho documents, document_folders, document_permissions, document_access_logs, document_upload_sessions, và openai_vector_stores tables. Bao gồm tenant isolation, performance indexes, và OpenAI integration fields.

# Test Strategy:
Verify migrations run successfully và tạo đúng schema với indexes và constraints
