import { ApiProperty } from '@nestjs/swagger';

/**
 * DTO cho thông tin manager của phòng ban
 */
export class DepartmentManagerDto {
  /**
   * ID của manager
   */
  @ApiProperty({
    description: 'ID của người quản lý phòng ban',
    example: 1,
  })
  id: number;

  /**
   * Tên đầy đủ của manager
   */
  @ApiProperty({
    description: 'Họ tên đầy đủ của người quản lý',
    example: 'Nguyễn Văn A',
    nullable: true,
  })
  fullName: string | null;

  /**
   * Email của manager
   */
  @ApiProperty({
    description: 'Email của người quản lý',
    example: '<EMAIL>',
  })
  email: string;

  /**
   * Vị trí công việc của manager
   */
  @ApiProperty({
    description: 'Vị trí công việc của người quản lý',
    example: 'Trưởng phòng',
    nullable: true,
  })
  position: string | null;
}

/**
 * DTO cho thông tin phòng ban cấp trên
 */
export class ParentDepartmentDto {
  /**
   * ID của phòng ban cấp trên
   */
  @ApiProperty({
    description: 'ID của phòng ban cấp trên',
    example: 2,
  })
  id: number;

  /**
   * Tên phòng ban cấp trên
   */
  @ApiProperty({
    description: 'Tên phòng ban cấp trên',
    example: 'Ban Giám đốc',
  })
  name: string;
}

/**
 * DTO for department response
 */
export class DepartmentResponseDto {
  /**
   * Department ID
   */
  @ApiProperty({
    description: 'ID của phòng ban',
    example: 1,
  })
  id: number;

  /**
   * Department name
   */
  @ApiProperty({
    description: 'Tên phòng ban',
    example: 'Phòng Kỹ thuật',
  })
  name: string;

  /**
   * Department description
   */
  @ApiProperty({
    description: 'Mô tả chi tiết về phòng ban',
    example: 'Phòng ban phụ trách các vấn đề kỹ thuật của công ty',
    nullable: true,
  })
  description: string | null;

  /**
   * Department manager ID
   */
  @ApiProperty({
    description: 'ID của người quản lý phòng ban',
    example: 1,
    nullable: true,
  })
  managerId: number | null;

  /**
   * Thông tin chi tiết của manager
   */
  @ApiProperty({
    description: 'Thông tin chi tiết của người quản lý phòng ban',
    type: DepartmentManagerDto,
    nullable: true,
  })
  manager: DepartmentManagerDto | null;

  /**
   * Parent department ID
   */
  @ApiProperty({
    description: 'ID của phòng ban cấp trên (nếu có)',
    example: 2,
    nullable: true,
  })
  parentId: number | null;

  /**
   * Thông tin chi tiết của phòng ban cấp trên
   */
  @ApiProperty({
    description: 'Thông tin chi tiết của phòng ban cấp trên',
    type: ParentDepartmentDto,
    nullable: true,
  })
  parentDepartment: ParentDepartmentDto | null;

  /**
   * Creation timestamp
   */
  @ApiProperty({
    description: 'Thời gian tạo phòng ban (timestamp)',
    example: 1640995200000,
    nullable: true,
  })
  createdAt: number | null;
}
