import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsNotEmpty, IsNumber, ArrayMinSize } from 'class-validator';
import { Type } from 'class-transformer';

/**
 * DTO cho việc xóa nhiều vai trò
 */
export class BulkDeleteRoleDto {
  /**
   * Danh sách ID các vai trò cần xóa
   */
  @ApiProperty({
    description: 'Danh sách ID các vai trò cần xóa',
    example: [1, 2, 3],
    type: [Number],
    required: true,
  })
  @IsNotEmpty()
  @IsArray()
  @ArrayMinSize(1, { message: '<PERSON><PERSON>i có ít nhất 1 vai trò để xóa' })
  @IsNumber({}, { each: true, message: 'Mỗi ID phải là số' })
  @Type(() => Number)
  ids: number[];
}
