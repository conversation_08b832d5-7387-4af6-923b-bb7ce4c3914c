# Task ID: 4
# Title: Update OKRs Module - KeyResult Repository
# Status: cancelled
# Dependencies: 1
# Priority: high
# Description: Add tenantId filtering to all KeyResult repository methods
# Details:
Update KeyResultRepository methods:
- findAll(): Add tenantId filtering
- findById(): Add tenantId to WHERE condition
- findByObjectiveId(): Add tenantId filtering
- create(): Set tenantId on new entities
- update(): Add tenantId to WHERE condition
- delete(): Add tenantId to WHERE condition
- updateProgress(): Add tenantId filtering

# Test Strategy:
Unit tests to ensure KeyResults are isolated by tenant
