import { Injectable, Logger } from '@nestjs/common';
import { QueueService } from '@shared/queue/queue.service';
import { AppException, ErrorCode } from '@/common';
import { QueueName } from '@shared/queue/queue.constants';
import { EmailJobData, TemplateEmailJobData } from '@shared/queue/queue.types';

/**
 * Service xử lý gửi email
 */
@Injectable()
export class EmailClientService {
  private readonly logger = new Logger(EmailClientService.name);

  constructor(private readonly queueService: QueueService) {}

  /**
   * Gửi email thông báo
   * @param to Người nhận
   * @param subject Tiêu đề
   * @param content Nội dung
   * @param cc Danh sách CC (optional)
   * @param bcc Danh sách BCC (optional)
   * @returns Promise với job ID
   */
  async sendEmail(
    to: string,
    subject: string,
    content: string,
    cc?: string[],
    bcc?: string[],
  ): Promise<string | undefined> {
    try {
      // <PERSON><PERSON> log thông tin gửi email
      this.logger.log(`Chuẩn bị gửi email tới: ${to}, tiêu đề: ${subject}`);

      // Tạo dữ liệu email
      const emailData: EmailJobData = {
        to,
        subject,
        content,
        timestamp: Date.now(),
      };

      // Thêm các trường tùy chọn nếu có
      if (cc && cc.length > 0) {
        emailData.cc = cc;
      }

      if (bcc && bcc.length > 0) {
        emailData.bcc = bcc;
      }

      // Thêm job vào queue để xử lý bất đồng bộ thay vì gửi ngay lập tức
      const jobId = await this.queueService.addEmailJob(emailData);

      this.logger.log(`Đã thêm email vào queue với jobId: ${jobId}`);
      return jobId;
    } catch (error) {
      this.logger.error(`Lỗi khi gửi email: ${error.message}`, error.stack);
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        'Không thể gửi email',
      );
    }
  }

  /**
   * Gửi email OTP - phương thức đơn giản hóa không sử dụng cc và bcc
   * @param to Người nhận
   * @param subject Tiêu đề
   * @param content Nội dung chứa mã OTP
   * @returns Promise với job ID
   */
  async sendOtpEmail(
    to: string,
    subject: string,
    content: string,
  ): Promise<string | undefined> {
    try {
      // Ghi log thông tin gửi email OTP
      this.logger.log(`Chuẩn bị gửi email OTP tới: ${to}`);

      // Tạo dữ liệu email OTP, không sử dụng cc và bcc
      const emailData: EmailJobData = {
        to,
        subject,
        content,
        timestamp: Date.now(),
      };

      // Thêm job vào queue để xử lý bất đồng bộ với độ ưu tiên cao
      const jobId = await this.queueService.addEmailJob(emailData, {
        priority: 1, // Độ ưu tiên cao cho email OTP
        attempts: 5, // Nhiều lần thử lại hơn
      });

      this.logger.log(`Đã thêm email OTP vào queue với jobId: ${jobId}`);
      return jobId;
    } catch (error) {
      this.logger.error(`Lỗi khi gửi email OTP: ${error.message}`, error.stack);
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        'Không thể gửi email OTP',
      );
    }
  }

  /**
   * Gửi email theo mẫu
   * @param to Người nhận
   * @param templateId ID mẫu email
   * @param data Dữ liệu điền vào mẫu
   * @param cc Danh sách CC (optional)
   * @param bcc Danh sách BCC (optional)
   * @returns Promise với job ID
   */
  async sendTemplateEmail(
    to: string,
    templateId: string,
    data: Record<string, any>,
    cc?: string[],
    bcc?: string[],
  ): Promise<string | undefined> {
    try {
      this.logger.log(`Chuẩn bị gửi email mẫu ${templateId} tới: ${to}`);

      // Tạo dữ liệu email mẫu
      const templateData: TemplateEmailJobData = {
        to,
        templateId,
        data,
        timestamp: Date.now(),
      };

      // Thêm các trường tùy chọn nếu có
      if (cc && cc.length > 0) {
        templateData.cc = cc;
      }

      if (bcc && bcc.length > 0) {
        templateData.bcc = bcc;
      }

      // Sử dụng phương thức mới addTemplateEmailJob thay vì addEmailJob với config thủ công
      const jobId = await this.queueService.addTemplateEmailJob(templateData);

      this.logger.log(`Đã thêm email mẫu vào queue với jobId: ${jobId}`);
      return jobId;
    } catch (error) {
      this.logger.error(`Lỗi khi gửi email mẫu: ${error.message}`, error.stack);
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        'Không thể gửi email mẫu',
      );
    }
  }

  /**
   * Kiểm tra trạng thái gửi email
   * @param jobId ID của job
   * @returns Thông tin về trạng thái
   */
  async getEmailStatus(jobId: string | undefined): Promise<any> {
    try {
      return await this.queueService.getJobStatus(QueueName.EMAIL, jobId);
    } catch (error) {
      this.logger.error(
        `Lỗi khi kiểm tra trạng thái email: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        'Không thể kiểm tra trạng thái email',
      );
    }
  }

  /**
   * Gửi thông báo cho admin khi người dùng ký hợp đồng
   * @param data Dữ liệu thông báo
   * @returns Promise với job ID
   */
  async sendContractSignedNotification(data: {
    contractId: number;
    contractType: any;
    userName: string;
    userEmail: string;
    userPhone: string;
  }): Promise<string | undefined> {
    try {
      // Lấy email admin từ cấu hình hoặc hardcode tạm thời
      const adminEmail = '<EMAIL>'; // Thay bằng email admin thực tế hoặc lấy từ cấu hình

      // Tạo tiêu đề email
      const subject = `Thông báo: Hợp đồng #${data.contractId} đã được ký`;

      // Tạo nội dung email
      const content = `
        <h2>Thông báo ký hợp đồng</h2>
        <p>Hợp đồng #${data.contractId} đã được ký bởi người dùng.</p>
        <p><strong>Thông tin người dùng:</strong></p>
        <ul>
          <li>Họ tên: ${data.userName}</li>
          <li>Email: ${data.userEmail}</li>
          <li>Số điện thoại: ${data.userPhone}</li>
        </ul>
        <p>Vui lòng đăng nhập vào hệ thống để xem chi tiết và phê duyệt hợp đồng.</p>
      `;

      // Gửi email thông báo
      return await this.sendEmail(adminEmail, subject, content);
    } catch (error) {
      this.logger.error(
        `Lỗi khi gửi thông báo ký hợp đồng: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        'Không thể gửi thông báo ký hợp đồng',
      );
    }
  }

  /**
   * Gửi thông báo cho người dùng khi hợp đồng được phê duyệt
   * @param data Dữ liệu thông báo
   * @returns Promise với job ID
   */
  async sendContractApprovedNotification(data: {
    contractId: number;
    contractType: any;
    userName: string;
    userEmail: string;
  }): Promise<string | undefined> {
    try {
      // Tạo tiêu đề email
      const subject = `Thông báo: Hợp đồng #${data.contractId} đã được phê duyệt`;

      // Tạo nội dung email
      const content = `
        <h2>Thông báo phê duyệt hợp đồng</h2>
        <p>Xin chào ${data.userName},</p>
        <p>Hợp đồng #${data.contractId} của bạn đã được phê duyệt.</p>
        <p>Vui lòng đăng nhập vào hệ thống để xem chi tiết.</p>
        <p>Trân trọng,<br>Đội ngũ hỗ trợ</p>
      `;

      // Gửi email thông báo
      return await this.sendEmail(data.userEmail, subject, content);
    } catch (error) {
      this.logger.error(
        `Lỗi khi gửi thông báo phê duyệt hợp đồng: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        'Không thể gửi thông báo phê duyệt hợp đồng',
      );
    }
  }

  /**
   * Gửi thông báo cho người dùng khi hợp đồng bị từ chối
   * @param data Dữ liệu thông báo
   * @returns Promise với job ID
   */
  async sendContractRejectedNotification(data: {
    contractId: number;
    contractType: any;
    userName: string;
    userEmail: string;
    rejectionReason?: string;
  }): Promise<string | undefined> {
    try {
      // Tạo tiêu đề email
      const subject = `Thông báo: Hợp đồng #${data.contractId} đã bị từ chối`;

      // Tạo nội dung email
      const content = `
        <h2>Thông báo từ chối hợp đồng</h2>
        <p>Xin chào ${data.userName},</p>
        <p>Hợp đồng #${data.contractId} của bạn đã bị từ chối.</p>
        ${data.rejectionReason ? `<p><strong>Lý do:</strong> ${data.rejectionReason}</p>` : ''}
        <p>Vui lòng đăng nhập vào hệ thống để xem chi tiết và gửi lại hợp đồng nếu cần.</p>
        <p>Trân trọng,<br>Đội ngũ hỗ trợ</p>
      `;

      // Gửi email thông báo
      return await this.sendEmail(data.userEmail, subject, content);
    } catch (error) {
      this.logger.error(
        `Lỗi khi gửi thông báo từ chối hợp đồng: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        'Không thể gửi thông báo từ chối hợp đồng',
      );
    }
  }
}
