import { ApiProperty } from '@nestjs/swagger';
import { IsInt, IsOptional, Min } from 'class-validator';
import { Type } from 'class-transformer';
import { QueryDto } from '@/common/dto/query.dto';

/**
 * DTO cho truy vấn danh sách tệp đính kèm của công việc
 */
export class TodoAttachmentQueryDto extends QueryDto {
  /**
   * Lọc theo ID công việc
   * @example 1
   */
  @ApiProperty({
    description: 'Lọc theo ID công việc',
    example: 1,
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt({ message: 'ID công việc phải là số nguyên' })
  @Min(1, { message: 'ID công việc phải lớn hơn 0' })
  todoId?: number;

  /**
   * Lọc theo ID người tạo
   * @example 1
   */
  @ApiProperty({
    description: '<PERSON>ọ<PERSON> theo ID người tạo',
    example: 1,
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt({ message: 'ID người tạo phải là số nguyên' })
  @Min(1, { message: 'ID người tạo phải lớn hơn 0' })
  createdBy?: number;
}
