import { MiddlewareConsumer, Module, NestModule } from '@nestjs/common';
import { DatabaseModule } from '@database/database.module';
import { ServicesModule } from '@shared/services/services.module';
import { ResilienceModule } from '@shared/resilience/resilience.module';
import { ConfigModule } from '@config';
import { CommonModule, RequestLoggerMiddleware } from '@/common';
import { EmailModule } from '@/modules/email/email.module';
import { OkrsModule } from '@/modules/okrs/okrs.module';
import { HrmModule } from './modules/hrm/hrm.module';
import { TodolistsModule } from './modules/todolists/todolists.module';
import { SystemModule } from './modules/system/system.module';
import { CalendarModule } from './modules/calendar/calendar.module';
import { ChatModule } from './modules/chat/chat.module';

@Module({
  imports: [
    ConfigModule,
    ResilienceModule,
    DatabaseModule,
    ServicesModule,
    CommonModule,
    EmailModule,
    OkrsModule,
    HrmModule,
    TodolistsModule,
    SystemModule,
    CalendarModule,
    ChatModule,
  ],
})
export class AppModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer.apply(RequestLoggerMiddleware).forRoutes('*');
  }
}
