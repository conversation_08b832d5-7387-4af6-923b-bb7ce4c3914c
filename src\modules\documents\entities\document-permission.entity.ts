import { Entity, PrimaryGeneratedColumn, Column, Index, Check } from 'typeorm';
import { PermissionLevel } from '../enums';

@Entity('document_permissions')
@Index(['tenantId'])
@Index(['documentId'])
@Index(['userId'])
@Index(['roleId'])
@Index(['departmentId'])
@Index(['permissionLevel'])
@Index(['expiresAt'])
@Index(['isInherited'])
@Index(['documentId', 'userId', 'tenantId'], { unique: true })
@Index(['documentId', 'roleId', 'tenantId'], { unique: true })
@Index(['documentId', 'departmentId', 'tenantId'], { unique: true })
@Check(`(
  (user_id IS NOT NULL AND role_id IS NULL AND department_id IS NULL) OR
  (user_id IS NULL AND role_id IS NOT NULL AND department_id IS NULL) OR
  (user_id IS NULL AND role_id IS NULL AND department_id IS NOT NULL)
)`)
export class DocumentPermission {
  /**
   * Khóa chính
   */
  @PrimaryGeneratedColumn()
  id: number;

  /**
   * ID tài liệu
   */
  @Column({ name: 'document_id', type: 'integer' })
  documentId: number;

  /**
   * ID người dùng cho phân quyền cụ thể theo user
   */
  @Column({ name: 'user_id', type: 'integer', nullable: true })
  userId: number | null;

  /**
   * ID vai trò cho phân quyền theo role
   */
  @Column({ name: 'role_id', type: 'integer', nullable: true })
  roleId: number | null;

  /**
   * ID phòng ban cho phân quyền theo department
   */
  @Column({ name: 'department_id', type: 'integer', nullable: true })
  departmentId: number | null;

  /**
   * Cấp độ phân quyền
   */
  @Column({ name: 'permission_level', type: 'varchar', length: 20 })
  permissionLevel: PermissionLevel;

  /**
   * ID người dùng đã cấp quyền này
   */
  @Column({ name: 'granted_by', type: 'integer' })
  grantedBy: number;

  /**
   * Thời điểm cấp quyền
   */
  @Column({ name: 'granted_at', type: 'bigint' })
  grantedAt: number;

  /**
   * Thời điểm hết hạn quyền (tùy chọn)
   */
  @Column({ name: 'expires_at', type: 'bigint', nullable: true })
  expiresAt: number | null;

  /**
   * Quyền có được kế thừa từ thư mục không
   */
  @Column({ name: 'is_inherited', type: 'boolean', default: false })
  isInherited: boolean;

  /**
   * Ghi chú về quyền (tùy chọn)
   */
  @Column({ name: 'notes', type: 'text', nullable: true })
  notes: string | null;

  /**
   * Thời điểm tạo (timestamp tính bằng milliseconds)
   */
  @Column({ name: 'created_at', type: 'bigint' })
  createdAt: number;

  /**
   * Thời điểm cập nhật cuối (timestamp tính bằng milliseconds)
   */
  @Column({ name: 'updated_at', type: 'bigint', nullable: true })
  updatedAt: number | null;

  /**
   * ID tenant để phân tách dữ liệu multi-tenant
   */
  @Column({ name: 'tenant_id', type: 'integer' })
  tenantId: number;

  // =====================================================
  // THUỘC TÍNH TÍNH TOÁN
  // =====================================================

  /**
   * Kiểm tra quyền đã hết hạn chưa
   */
  get isExpired(): boolean {
    if (!this.expiresAt) return false;
    return Date.now() > this.expiresAt;
  }

  /**
   * Kiểm tra quyền hiện tại có hợp lệ không
   */
  get isValid(): boolean {
    return !this.isExpired;
  }

  /**
   * Lấy loại đối tượng được phân quyền
   */
  get targetType(): 'user' | 'role' | 'department' {
    if (this.userId) return 'user';
    if (this.roleId) return 'role';
    if (this.departmentId) return 'department';
    throw new Error('Đối tượng phân quyền không hợp lệ');
  }

  /**
   * Lấy ID của đối tượng được phân quyền
   */
  get targetId(): number {
    if (this.userId) return this.userId;
    if (this.roleId) return this.roleId;
    if (this.departmentId) return this.departmentId;
    throw new Error('Đối tượng phân quyền không hợp lệ');
  }

  /**
   * Lấy số ngày còn lại đến khi hết hạn
   */
  get daysUntilExpiration(): number | null {
    if (!this.expiresAt) return null;
    const msUntilExpiration = this.expiresAt - Date.now();
    return Math.ceil(msUntilExpiration / (1000 * 60 * 60 * 24));
  }
}
