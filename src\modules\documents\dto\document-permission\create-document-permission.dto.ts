import {
  IsN<PERSON>ber,
  IsEnum,
  IsOptional,
  IsString,
  IsBoolean,
} from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { PermissionLevel } from '../../enums';

/**
 * DTO để tạo quyền tài liệu mới
 */
export class CreateDocumentPermissionDto {
  /**
   * ID tài liệu
   */
  @ApiProperty({
    description: 'ID tài liệu cần phân quyền',
    example: 1,
  })
  @IsNumber()
  documentId: number;

  /**
   * ID người dùng (cho phân quyền user-specific)
   */
  @ApiPropertyOptional({
    description: 'ID người dùng (chỉ dùng cho phân quyền user-specific)',
    example: 1,
  })
  @IsOptional()
  @IsNumber()
  userId?: number;

  /**
   * ID vai trò (cho phân quyền role-based)
   */
  @ApiPropertyOptional({
    description: 'ID vai trò (chỉ dùng cho phân quyền role-based)',
    example: 1,
  })
  @IsOptional()
  @IsNumber()
  roleId?: number;

  /**
   * ID phòng ban (cho phân quyền department-based)
   */
  @ApiPropertyOptional({
    description: 'ID phòng ban (chỉ dùng cho phân quyền department-based)',
    example: 1,
  })
  @IsOptional()
  @IsNumber()
  departmentId?: number;

  /**
   * Cấp độ phân quyền
   */
  @ApiProperty({
    description: 'Cấp độ phân quyền',
    enum: PermissionLevel,
    example: PermissionLevel.READ,
  })
  @IsEnum(PermissionLevel)
  permissionLevel: PermissionLevel;

  /**
   * Thời điểm hết hạn quyền
   */
  @ApiPropertyOptional({
    description: 'Thời điểm hết hạn quyền (timestamp, null = không hết hạn)',
    example: 1640995200000,
  })
  @IsOptional()
  @IsNumber()
  expiresAt?: number;

  /**
   * Quyền có được kế thừa từ thư mục không
   */
  @ApiPropertyOptional({
    description: 'Quyền có được kế thừa từ thư mục không',
    example: false,
    default: false,
  })
  @IsOptional()
  @IsBoolean()
  isInherited?: boolean = false;

  /**
   * Ghi chú về quyền
   */
  @ApiPropertyOptional({
    description: 'Ghi chú về quyền này',
    example: 'Quyền đọc tạm thời cho dự án X',
  })
  @IsOptional()
  @IsString()
  notes?: string;
}
