:root {
    --primary-color: #0366d6;
    --sidebar-bg: #f6f8fa;
    --border-color: #e1e4e8;
    --text-color: #24292e;
    --code-bg: #f6f8fa;
    --hover-color: #f1f1f1;
    --header-bg: #ffffff;
    --link-color: #0366d6;
    --blockquote-border: #dfe2e5;
    --table-border: #dfe2e5;
    --table-header-bg: #f6f8fa;
    --scrollbar-thumb: #c1c1c1;
}

* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Helvetica, Arial, sans-serif;
    line-height: 1.6;
    color: var(--text-color);
    background-color: #fff;
}

.container {
    display: flex;
    height: 100vh;
    overflow: hidden;
}

/* Sidebar */
.sidebar {
    width: 300px;
    background-color: var(--sidebar-bg);
    border-right: 1px solid var(--border-color);
    display: flex;
    flex-direction: column;
    transition: all 0.3s ease;
}

.sidebar-header {
    padding: 16px;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: var(--header-bg);
}

.sidebar-content {
    flex: 1;
    overflow-y: auto;
    padding: 16px;
}

.sidebar.collapsed {
    width: 0;
    border: none;
}

/* TOC */
#toc {
    font-size: 14px;
}

#toc ul {
    list-style-type: none;
    padding-left: 15px;
}

#toc > ul {
    padding-left: 0;
}

#toc li {
    margin: 8px 0;
}

#toc a {
    color: var(--text-color);
    text-decoration: none;
    display: block;
    padding: 5px 10px;
    border-radius: 5px;
    transition: background-color 0.2s;
}

#toc a:hover {
    background-color: var(--hover-color);
}

#toc a.active {
    background-color: var(--hover-color);
    font-weight: bold;
    color: var(--primary-color);
}

/* Content */
.content {
    flex: 1;
    overflow-y: auto;
    padding: 0;
    display: flex;
    flex-direction: column;
}

.content-header {
    padding: 16px;
    border-bottom: 1px solid var(--border-color);
    background-color: var(--header-bg);
    display: flex;
    align-items: center;
}

#markdown-content {
    padding: 30px;
    max-width: 900px;
    margin: 0 auto;
    width: 100%;
}

/* Markdown Styling */
#markdown-content h1,
#markdown-content h2,
#markdown-content h3,
#markdown-content h4,
#markdown-content h5,
#markdown-content h6 {
    margin-top: 24px;
    margin-bottom: 16px;
    font-weight: 600;
    line-height: 1.25;
}

#markdown-content h1 {
    font-size: 2em;
    border-bottom: 1px solid var(--border-color);
    padding-bottom: 0.3em;
}

#markdown-content h2 {
    font-size: 1.5em;
    border-bottom: 1px solid var(--border-color);
    padding-bottom: 0.3em;
}

#markdown-content h3 {
    font-size: 1.25em;
}

#markdown-content p {
    margin-top: 0;
    margin-bottom: 16px;
}

#markdown-content a {
    color: var(--link-color);
    text-decoration: none;
}

#markdown-content a:hover {
    text-decoration: underline;
}

#markdown-content code {
    font-family: SFMono-Regular, Consolas, "Liberation Mono", Menlo, monospace;
    padding: 0.2em 0.4em;
    margin: 0;
    font-size: 85%;
    background-color: var(--code-bg);
    border-radius: 3px;
}

#markdown-content pre {
    font-family: SFMono-Regular, Consolas, "Liberation Mono", Menlo, monospace;
    padding: 16px;
    overflow: auto;
    font-size: 85%;
    line-height: 1.45;
    background-color: var(--code-bg);
    border-radius: 6px;
    margin-bottom: 16px;
}

#markdown-content pre code {
    padding: 0;
    margin: 0;
    background-color: transparent;
    border: 0;
}

#markdown-content blockquote {
    padding: 0 1em;
    color: #6a737d;
    border-left: 0.25em solid var(--blockquote-border);
    margin-bottom: 16px;
}

#markdown-content ul,
#markdown-content ol {
    padding-left: 2em;
    margin-bottom: 16px;
}

#markdown-content table {
    border-collapse: collapse;
    width: 100%;
    margin-bottom: 16px;
}

#markdown-content table th,
#markdown-content table td {
    padding: 6px 13px;
    border: 1px solid var(--table-border);
}

#markdown-content table th {
    background-color: var(--table-header-bg);
    font-weight: 600;
}

#markdown-content table tr:nth-child(2n) {
    background-color: #f6f8fa;
}

/* Buttons */
.toggle-btn {
    background: none;
    border: none;
    cursor: pointer;
    font-size: 18px;
    color: var(--text-color);
}

.toggle-btn:hover {
    color: var(--primary-color);
}

.mobile-only {
    display: none;
}

/* File selector */
.file-selector {
    display: flex;
    align-items: center;
    margin-left: auto;
}

#file-select {
    padding: 8px 12px;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    margin-right: 10px;
    font-size: 14px;
}

#upload-btn {
    background: none;
    border: none;
    cursor: pointer;
    font-size: 16px;
    color: var(--text-color);
    padding: 8px;
}

#upload-btn:hover {
    color: var(--primary-color);
}

/* Modal */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
}

.modal-content {
    background-color: #fff;
    margin: 10% auto;
    width: 80%;
    max-width: 800px;
    border-radius: 8px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    display: flex;
    flex-direction: column;
    max-height: 80vh;
}

.modal-header {
    padding: 16px;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-body {
    padding: 16px;
    flex: 1;
    overflow-y: auto;
}

.modal-footer {
    padding: 16px;
    border-top: 1px solid var(--border-color);
    display: flex;
    justify-content: flex-end;
}

.close {
    color: #aaa;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
}

.close:hover {
    color: #000;
}

#markdown-editor {
    width: 100%;
    height: 300px;
    padding: 12px;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    font-family: SFMono-Regular, Consolas, "Liberation Mono", Menlo, monospace;
    resize: vertical;
}

#save-markdown, #cancel-markdown {
    padding: 8px 16px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    margin-left: 10px;
}

#save-markdown {
    background-color: var(--primary-color);
    color: white;
}

#save-markdown:hover {
    background-color: #0256b9;
}

#cancel-markdown {
    background-color: #f1f1f1;
}

#cancel-markdown:hover {
    background-color: #e1e1e1;
}

/* Scrollbar */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: transparent;
}

::-webkit-scrollbar-thumb {
    background: var(--scrollbar-thumb);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a1a1a1;
}

/* Responsive */
@media (max-width: 768px) {
    .sidebar {
        position: fixed;
        z-index: 100;
        height: 100%;
        width: 80%;
        max-width: 300px;
        transform: translateX(-100%);
    }

    .sidebar.active {
        transform: translateX(0);
    }

    .mobile-only {
        display: block;
    }

    .content-header {
        justify-content: space-between;
    }
}
