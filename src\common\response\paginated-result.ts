import { ApiProperty } from '@nestjs/swagger';

/**
 * Meta information about pagination
 */
export class PaginationMeta {
  @ApiProperty({
    description: 'Tổng số items',
    example: 100,
  })
  totalItems: number;

  @ApiProperty({
    description: 'Số items trong trang hiện tại',
    example: 10,
  })
  itemCount: number;

  @ApiProperty({
    description: 'Số items mỗi trang',
    example: 10,
  })
  itemsPerPage: number;

  @ApiProperty({
    description: 'Tổng số trang',
    example: 10,
  })
  totalPages: number;

  @ApiProperty({
    description: 'Trang hiện tại',
    example: 1,
  })
  currentPage: number;
}

/**
 * Paginated result interface
 */
export class PaginatedResult<T> {
  @ApiProperty({
    description: 'Mảng các items',
    type: 'array',
    isArray: true,
  })
  items: T[];

  @ApiProperty({
    description: 'Thông tin phân trang',
    type: PaginationMeta,
  })
  meta: PaginationMeta;
}
