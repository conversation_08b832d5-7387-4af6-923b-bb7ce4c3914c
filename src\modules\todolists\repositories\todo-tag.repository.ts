import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { TodoTag } from '../entities/todo-tag.entity';

/**
 * Repository cho entity TodoTag
 */
@Injectable()
export class TodoTagRepository {
  constructor(
    @InjectRepository(TodoTag)
    private readonly repository: Repository<TodoTag>,
  ) {}

  /**
   * Tạo liên kết giữa todo và label
   * @param tenantId ID tenant (required for tenant isolation)
   * @param data Dữ liệu liên kết
   * @returns Liên kết đã tạo
   */
  async create(tenantId: number, data: Partial<TodoTag>): Promise<TodoTag> {
    const todoTag = this.repository.create({ ...data, tenantId });
    return this.repository.save(todoTag);
  }

  /**
   * Tìm tất cả tag của một todo
   * @param tenantId ID tenant (required for tenant isolation)
   * @param todoId ID của todo
   * @returns Danh sách tag
   */
  async findByTodoId(tenantId: number, todoId: number): Promise<TodoTag[]> {
    return this.repository.find({
      where: { todoId, tenantId },
    });
  }

  /**
   * Tìm tag theo ID
   * @param tenantId ID tenant (required for tenant isolation)
   * @param id ID của tag
   * @returns Tag hoặc null nếu không tìm thấy
   */
  async findById(tenantId: number, id: number): Promise<TodoTag | null> {
    return this.repository.findOne({
      where: { id, tenantId },
    });
  }

  /**
   * Tìm tag theo todoId và labelsId
   * @param tenantId ID tenant (required for tenant isolation)
   * @param todoId ID của todo
   * @param labelsId ID của label
   * @returns Tag hoặc null nếu không tìm thấy
   */
  async findByTodoIdAndLabelsId(
    tenantId: number,
    todoId: number,
    labelsId: number,
  ): Promise<TodoTag | null> {
    return this.repository.findOne({
      where: {
        todoId,
        labelsId,
        tenantId,
      },
    });
  }

  /**
   * Xóa tag
   * @param tenantId ID tenant (required for tenant isolation)
   * @param id ID của tag
   * @returns true nếu xóa thành công
   */
  async delete(tenantId: number, id: number): Promise<boolean> {
    const result = await this.repository.delete({ id, tenantId });
    return (
      result.affected !== null &&
      result.affected !== undefined &&
      result.affected > 0
    );
  }

  /**
   * Xóa tag theo todoId và labelsId
   * @param tenantId ID tenant (required for tenant isolation)
   * @param todoId ID của todo
   * @param labelsId ID của label
   * @returns true nếu xóa thành công
   */
  async deleteByTodoIdAndLabelsId(
    tenantId: number,
    todoId: number,
    labelsId: number,
  ): Promise<boolean> {
    const result = await this.repository.delete({
      todoId,
      labelsId,
      tenantId,
    });
    return (
      result.affected !== null &&
      result.affected !== undefined &&
      result.affected > 0
    );
  }
}
