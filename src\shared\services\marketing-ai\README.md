# Marketing AI Services

Module này cung cấp tích hợp với các dịch vụ <PERSON> bên thứ ba để tạo tài nguyên marketing (hình ảnh, nội dung, video) từ văn bản.

## Cài đặt

### <PERSON><PERSON> thuộc

Tùy thuộc vào các nhà cung cấp bạn muốn sử dụng, bạn cần cài đặt các gói sau:

```bash
# OpenAI
npm install openai

# Axios (đã được cài đặt trong dự án)
npm install @nestjs/axios
```

### Cấu hình

Thêm các biến môi trường sau vào file `.env` của bạn:

```bash
# OpenAI
OPENAI_API_KEY=your_openai_api_key
OPENAI_ORGANIZATION_ID=your_openai_org_id

# Stability AI
STABILITY_API_KEY=your_stability_api_key

# Leonardo AI
LEONARDO_API_KEY=your_leonardo_api_key

# Anthropic
ANTHROPIC_API_KEY=your_anthropic_api_key

# Cohere
COHERE_API_KEY=your_cohere_api_key

# Runway ML
RUNWAY_API_KEY=your_runway_api_key

# Synthesia
SYNTHESIA_API_KEY=your_synthesia_api_key

# Pika Labs
PIKA_API_KEY=your_pika_api_key
```

## Sử dụng

### Tạo hình ảnh

```typescript
import { ImageGenerationFactoryService, ImageGenerationProviderType, ImageSize, ImageStyle } from '@shared/services/marketing-ai';

@Injectable()
export class YourService {
  constructor(
    private readonly imageGenerationFactory: ImageGenerationFactoryService,
  ) {}

  async generateImage(prompt: string): Promise<string[]> {
    // Lấy dịch vụ tạo hình ảnh
    const imageService = this.imageGenerationFactory.getProvider(
      ImageGenerationProviderType.OPENAI_DALLE,
    );

    // Tạo hình ảnh
    const result = await imageService.generateImage(prompt, {
      size: ImageSize.LARGE,
      style: ImageStyle.REALISTIC,
      count: 1,
    });

    if (!result.success) {
      throw new Error(`Failed to generate image: ${result.error}`);
    }

    return result.data.imageUrls;
  }
}
```

### Tạo nội dung

```typescript
import { ContentGenerationFactoryService, ContentGenerationProviderType, ContentType, ContentTone, ContentLanguage } from '@shared/services/marketing-ai';

@Injectable()
export class YourService {
  constructor(
    private readonly contentGenerationFactory: ContentGenerationFactoryService,
  ) {}

  async generateContent(prompt: string): Promise<string> {
    // Lấy dịch vụ tạo nội dung
    const contentService = this.contentGenerationFactory.getProvider(
      ContentGenerationProviderType.OPENAI_GPT,
    );

    // Tạo nội dung
    const result = await contentService.generateContent(prompt, {
      contentType: ContentType.BLOG_POST,
      tone: ContentTone.PROFESSIONAL,
      language: ContentLanguage.VIETNAMESE,
      keywords: ['marketing', 'AI', 'content'],
    });

    if (!result.success) {
      throw new Error(`Failed to generate content: ${result.error}`);
    }

    return result.data.content;
  }
}
```

### Tạo video

```typescript
import { VideoGenerationFactoryService, VideoGenerationProviderType, VideoResolution, VideoStyle } from '@shared/services/marketing-ai';

@Injectable()
export class YourService {
  constructor(
    private readonly videoGenerationFactory: VideoGenerationFactoryService,
  ) {}

  async generateVideo(prompt: string): Promise<string> {
    // Lấy dịch vụ tạo video
    const videoService = this.videoGenerationFactory.getProvider(
      VideoGenerationProviderType.RUNWAY_ML,
    );

    // Tạo video
    const result = await videoService.generateVideo(prompt, {
      resolution: VideoResolution.HD,
      style: VideoStyle.CINEMATIC,
      duration: 15,
    });

    if (!result.success) {
      throw new Error(`Failed to generate video: ${result.error}`);
    }

    // Kiểm tra trạng thái tạo video
    const jobId = result.data.jobId;
    
    // Trong thực tế, bạn sẽ cần lưu jobId và kiểm tra trạng thái sau
    // Đây chỉ là ví dụ đơn giản
    const statusResult = await videoService.checkVideoGenerationStatus(jobId);
    
    if (!statusResult.success) {
      throw new Error(`Failed to check video status: ${statusResult.error}`);
    }
    
    if (statusResult.data.status === 'completed') {
      return statusResult.data.videoUrl;
    } else {
      return `Video is still processing. Job ID: ${jobId}`;
    }
  }
}
```

## Các nhà cung cấp được hỗ trợ

### Tạo hình ảnh
- **OpenAI DALL-E**: Tạo hình ảnh nâng cao từ văn bản
- **Stability AI**: Tạo hình ảnh mã nguồn mở với Stable Diffusion
- **Leonardo.AI**: Tạo hình ảnh AI cho các chuyên gia sáng tạo

### Tạo nội dung
- **OpenAI GPT-4**: Tạo văn bản nâng cao cho nội dung marketing
- **Anthropic Claude**: Mô hình AI thay thế cho tạo nội dung
- **Cohere**: Chuyên về tạo nội dung marketing

### Tạo video
- **Runway ML**: Tạo video AI nâng cao
- **Synthesia**: Tạo video AI với avatar ảo
- **Pika Labs**: Tạo video từ văn bản

## Mở rộng

Để thêm một nhà cung cấp mới:

1. Tạo một lớp dịch vụ mới trong thư mục tương ứng
2. Triển khai giao diện tương ứng (ImageGenerationService, ContentGenerationService, hoặc VideoGenerationService)
3. Thêm nhà cung cấp vào factory tương ứng
4. Thêm nhà cung cấp vào MarketingAiModule
5. Cập nhật file index.ts để xuất nhà cung cấp mới
