## 2.2.2.13 <PERSON><PERSON> tả use case Chấm điểm công việc

**<PERSON><PERSON> tả ngắn:** Cho phép người có thẩm quyền chấm điểm và đánh giá kết quả của một công việc đã hoàn thành.

### Luồng các sự kiện:

#### Luồng cơ bản:
Khi người dùng chọn "Chấm điểm" trên màn hình chi tiết công việc đã hoàn thành, hệ thống hiển thị form chấm điểm.
Người dùng chọn số sao đánh giá (1-5 sao) tương ứng với mức độ hoàn thành công việc.
Người dùng nhập nhận xét đánh giá chi tiết về kết quả công việc.
Ng<PERSON>ời dùng nhấn "<PERSON><PERSON><PERSON> nhận đánh giá", hệ thống lưu thông tin chấm điểm và cập nhật số sao cho công việc.
<PERSON><PERSON> thống gửi thông báo cho người thực hiện công việc về kết quả đánh giá và hiển thị thông báo "Đã chấm điểm thành công".
Use case kết thúc.

#### Luồng rẽ nhánh:
Nếu người dùng không chọn số sao, hệ thống hiển thị cảnh báo "Vui lòng chọn số sao đánh giá".
Nếu người dùng không nhập nhận xét, hệ thống kiểm tra nếu đánh giá dưới 3 sao thì bắt buộc phải có nhận xét và hiển thị cảnh báo "Vui lòng nhập nhận xét đánh giá".
Nếu người dùng đã chấm điểm công việc này trước đó, hệ thống hiển thị điểm cũ và cho phép cập nhật điểm mới.
Nếu người dùng nhấn "Hủy", hệ thống trở về màn hình chi tiết công việc mà không lưu đánh giá.
Khi có lỗi hệ thống/DB, hiển thị thông báo "Lỗi hệ thống, vui lòng thử lại sau" và kết thúc use case.

### Các yêu cầu đặc biệt:
Khi người dùng chọn dưới 3 sao, bắt buộc phải nhập nhận xét chi tiết.
Chỉ cho phép chấm điểm công việc đã ở trạng thái "Hoàn thành" (COMPLETED).
Lưu lại lịch sử chấm điểm để xem được ai đã chấm điểm, thời điểm nào và đánh giá ra sao.

### Tiền điều kiện:
Người dùng đã đăng nhập.
Người dùng có quyền chấm điểm (người tạo công việc, admin dự án, hoặc quản lý).
Công việc đã ở trạng thái "Hoàn thành" (COMPLETED).

### Hậu điều kiện:
Công việc được cập nhật với số sao đánh giá (awardedStars).
Hệ thống lưu lại thông tin chi tiết về lần chấm điểm (người chấm, điểm số, nhận xét, thời gian).
Người thực hiện công việc nhận được thông báo về kết quả đánh giá.

### Điểm mở rộng:
Tích hợp với hệ thống đánh giá hiệu suất để tổng hợp kết quả vào đánh giá định kỳ của nhân viên.
Bổ sung tính năng phản hồi đánh giá, cho phép người thực hiện công việc giải thích hoặc phản hồi về điểm số nhận được.
Thống kê và phân tích điểm số theo thời gian, theo dự án, hoặc theo người thực hiện để đánh giá xu hướng hiệu suất. 