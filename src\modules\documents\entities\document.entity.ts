import { Entity, PrimaryGeneratedColumn, Column, Index } from 'typeorm';
import { DocumentType, ProcessingStatus } from '../enums';

@Entity('documents')
@Index(['tenantId'])
@Index(['folderId'])
@Index(['documentType', 'tenantId'])
@Index(['createdBy'])
@Index(['createdAt'])
@Index(['isActive', 'tenantId'])
@Index(['isPublic', 'tenantId'])
@Index(['processingStatus'])
@Index(['openaiFileId'])
@Index(['openaiVectorStoreId'])
@Index(['contentHash'])
@Index(['s3Key', 'tenantId'], { unique: true })
@Index(['openaiFileId'], { unique: true })
export class Document {
  /**
   * Khóa chính
   */
  @PrimaryGeneratedColumn()
  id: number;

  /**
   * Tiêu đề tài liệu
   */
  @Column({ name: 'title', type: 'varchar', length: 500 })
  title: string;

  /**
   * <PERSON>ô tả tài liệu
   */
  @Column({ name: 'description', type: 'text', nullable: true })
  description: string | null;

  /**
   * Loại tài liệu
   */
  @Column({ name: 'document_type', type: 'varchar', length: 50 })
  documentType: DocumentType;

  /**
   * Tên file gốc
   */
  @Column({ name: 'file_name', type: 'varchar', length: 255 })
  fileName: string;

  /**
   * Kích thước file tính bằng bytes
   */
  @Column({ name: 'file_size', type: 'bigint' })
  fileSize: number;

  /**
   * Loại MIME
   */
  @Column({ name: 'mime_type', type: 'varchar', length: 100 })
  mimeType: string;

  /**
   * ID thư mục để tổ chức
   */
  @Column({ name: 'folder_id', type: 'integer', nullable: true })
  folderId: number | null;

  // =====================================================
  // CÁC TRƯỜNG LƯU TRỮ S3
  // =====================================================

  /**
   * Tên bucket S3
   */
  @Column({ name: 's3_bucket', type: 'varchar', length: 100, nullable: true })
  s3Bucket: string | null;

  /**
   * Khóa object S3
   */
  @Column({ name: 's3_key', type: 'varchar', length: 500 })
  s3Key: string;

  /**
   * Vùng S3
   */
  @Column({ name: 's3_region', type: 'varchar', length: 50, nullable: true })
  s3Region: string | null;

  /**
   * ETag S3
   */
  @Column({ name: 's3_etag', type: 'varchar', length: 100, nullable: true })
  s3Etag: string | null;

  /**
   * ID phiên bản S3
   */
  @Column({
    name: 's3_version_id',
    type: 'varchar',
    length: 100,
    nullable: true,
  })
  s3VersionId: string | null;

  // =====================================================
  // CÁC TRƯỜNG TÍCH HỢP OPENAI
  // =====================================================

  /**
   * ID OpenAI Files API
   */
  @Column({
    name: 'openai_file_id',
    type: 'varchar',
    length: 100,
    nullable: true,
  })
  openaiFileId: string | null;

  /**
   * ID OpenAI Vector Store
   */
  @Column({
    name: 'openai_vector_store_id',
    type: 'varchar',
    length: 100,
    nullable: true,
  })
  openaiVectorStoreId: string | null;

  /**
   * Trạng thái xử lý OpenAI
   */
  @Column({
    name: 'processing_status',
    type: 'varchar',
    length: 50,
    default: ProcessingStatus.PENDING,
  })
  processingStatus: ProcessingStatus;

  /**
   * Thông báo lỗi xử lý
   */
  @Column({ name: 'processing_error', type: 'text', nullable: true })
  processingError: string | null;

  /**
   * Thời điểm thử xử lý cuối cùng
   */
  @Column({ name: 'last_processed_at', type: 'bigint', nullable: true })
  lastProcessedAt: number | null;

  /**
   * Số lần thử lại xử lý
   */
  @Column({ name: 'retry_count', type: 'integer', default: 0 })
  retryCount: number;

  // =====================================================
  // CÁC TRƯỜNG METADATA NỘI DUNG
  // =====================================================

  /**
   * Hash SHA-256 để phát hiện trùng lặp
   */
  @Column({ name: 'content_hash', type: 'varchar', length: 64, nullable: true })
  contentHash: string | null;

  /**
   * Số trang (cho tài liệu PDF)
   */
  @Column({ name: 'page_count', type: 'integer', nullable: true })
  pageCount: number | null;

  /**
   * Số từ
   */
  @Column({ name: 'word_count', type: 'integer', nullable: true })
  wordCount: number | null;

  /**
   * Ngôn ngữ tài liệu
   */
  @Column({ name: 'language', type: 'varchar', length: 10, default: 'vi' })
  language: string;

  /**
   * Nội dung văn bản đã trích xuất để tìm kiếm
   */
  @Column({ name: 'extracted_text', type: 'text', nullable: true })
  extractedText: string | null;

  // =====================================================
  // CÁC TRƯỜNG TRẠNG THÁI VÀ HIỂN THỊ
  // =====================================================

  /**
   * Tài liệu có đang hoạt động không
   */
  @Column({ name: 'is_active', type: 'boolean', default: true })
  isActive: boolean;

  /**
   * Tài liệu có công khai không (bỏ qua kiểm tra quyền)
   */
  @Column({ name: 'is_public', type: 'boolean', default: false })
  isPublic: boolean;

  /**
   * Phiên bản tài liệu
   */
  @Column({ name: 'version', type: 'integer', default: 1 })
  version: number;

  /**
   * Thẻ tag tài liệu
   */
  @Column({ name: 'tags', type: 'text', array: true, nullable: true })
  tags: string[] | null;

  /**
   * Metadata bổ sung của tài liệu
   */
  @Column({ name: 'metadata', type: 'jsonb', nullable: true })
  metadata: Record<string, any> | null;

  // =====================================================
  // CÁC TRƯỜNG KIỂM TOÁN
  // =====================================================

  /**
   * Thời điểm tạo (timestamp tính bằng milliseconds)
   */
  @Column({ name: 'created_at', type: 'bigint' })
  createdAt: number;

  /**
   * Thời điểm cập nhật cuối (timestamp tính bằng milliseconds)
   */
  @Column({ name: 'updated_at', type: 'bigint', nullable: true })
  updatedAt: number | null;

  /**
   * ID người dùng tạo tài liệu này
   */
  @Column({ name: 'created_by', type: 'integer' })
  createdBy: number;

  /**
   * ID người dùng cập nhật tài liệu này lần cuối
   */
  @Column({ name: 'updated_by', type: 'integer', nullable: true })
  updatedBy: number | null;

  /**
   * ID tenant để phân tách dữ liệu multi-tenant
   */
  @Column({ name: 'tenant_id', type: 'integer' })
  tenantId: number;

  // =====================================================
  // THUỘC TÍNH TÍNH TOÁN
  // =====================================================

  /**
   * Lấy kích thước file tính bằng MB
   */
  get fileSizeMB(): number {
    return Math.round((this.fileSize / (1024 * 1024)) * 100) / 100;
  }

  /**
   * Kiểm tra tài liệu đã được xử lý thành công chưa
   */
  get isProcessed(): boolean {
    return this.processingStatus === ProcessingStatus.COMPLETED;
  }

  /**
   * Kiểm tra xử lý tài liệu có thất bại không
   */
  get isProcessingFailed(): boolean {
    return this.processingStatus === ProcessingStatus.FAILED;
  }

  /**
   * Kiểm tra tài liệu có thể thử lại không
   */
  get canRetry(): boolean {
    return this.isProcessingFailed && this.retryCount < 3;
  }

  /**
   * Lấy phần mở rộng file từ tên file
   */
  get fileExtension(): string {
    return this.fileName.split('.').pop()?.toLowerCase() || '';
  }

  /**
   * Kiểm tra tài liệu có phải PDF không
   */
  get isPDF(): boolean {
    return this.mimeType === 'application/pdf' || this.fileExtension === 'pdf';
  }

  /**
   * Kiểm tra tài liệu có phải Word document không
   */
  get isWordDocument(): boolean {
    return (
      this.mimeType ===
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document' ||
      this.fileExtension === 'docx'
    );
  }

  /**
   * Kiểm tra tài liệu có phải text file không
   */
  get isTextFile(): boolean {
    return (
      this.mimeType.startsWith('text/') ||
      ['txt', 'md'].includes(this.fileExtension)
    );
  }
}
