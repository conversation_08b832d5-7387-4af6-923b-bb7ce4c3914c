import { Injectable, Logger } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { ConfigService, ConfigType, ServicesConfig } from '@config';
import { AppException, ErrorCode } from '@common/exceptions/app.exception';
import { firstValueFrom } from 'rxjs';

/**
 * Interface cho Meta AI Model
 */
interface MetaAIModel {
  id: string;
  name?: string;
  version?: string;
  description?: string;
  context_length?: number;
  capabilities?: string[];
  parameters?: number;
  created_at?: string;
}

/**
 * Interface cho Meta AI Models Response
 */
interface MetaAIModelsResponse {
  data: MetaAIModel[];
  object: string;
}

/**
 * Service tương tác với Meta AI API (Llama)
 */
@Injectable()
export class MetaAIService {
  private readonly logger = new Logger(MetaAIService.name);
  private readonly apiBaseUrl = 'https://api.meta.ai/v1';

  constructor(
    private readonly configService: ConfigService,
    private readonly httpService: HttpService,
  ) {}

  /**
   * Lấy API key từ options hoặc từ biến môi trường
   * @param options Tùy chọn chứa apiKey và useEnvKey
   * @returns API key hợp lệ hoặc ném ra lỗi nếu không tìm thấy
   * @throws Error nếu không tìm thấy API key hợp lệ
   */
  private getApiKey(): string {
    const servicesConfig = this.configService.getConfig<ServicesConfig>(
      ConfigType.Services,
    );
    return servicesConfig.metaAI?.apiKey || '';
  }

  /**
   * Lấy danh sách model từ Meta AI API
   * @param options Tùy chọn bổ sung (apiKey, useEnvKey)
   * @returns Danh sách model từ Meta AI
   * @throws AppException nếu có lỗi khi lấy danh sách model
   */
  async getModels(options?: { apiKey?: string }): Promise<MetaAIModel[]> {
    try {
      // Thiết lập timeout cho request
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 30000); // 30 seconds timeout

      // Gọi REST API để lấy danh sách model
      const response = await firstValueFrom(
        this.httpService.get<MetaAIModelsResponse>(
          `${this.apiBaseUrl}/models`,
          {
            headers: {
              'Content-Type': 'application/json',
              Authorization: `Bearer ${options?.apiKey || this.getApiKey()}`,
            },
            signal: controller.signal,
          },
        ),
      );

      clearTimeout(timeoutId);

      // Lấy danh sách model từ response
      const models = response.data.data || [];

      this.logger.log(`Retrieved ${models.length} models from Meta AI`);
      return models;
    } catch (error: any) {
      this.logger.error(
        `Error retrieving models from Meta AI: ${error.message}`,
        error.stack,
      );

      // Xử lý các lỗi khi kết nối Meta AI API
      if (error.response) {
        // Lỗi từ API response
        const status = error.response.status;
        const data = error.response.data;

        if (status === 429) {
          throw new AppException(
            ErrorCode.OPENAI_QUOTA_EXCEEDED,
            'Đã vượt quá giới hạn sử dụng Meta AI API',
          );
        }

        if (status === 401 || status === 403) {
          throw new AppException(
            ErrorCode.OPENAI_API_ERROR,
            'Lỗi xác thực API key: ' +
              (data?.error?.message || 'Không có quyền truy cập'),
          );
        }

        throw new AppException(
          ErrorCode.OPENAI_API_ERROR,
          `Lỗi API (${status}): ${data?.error?.message || 'Không xác định'}`,
        );
      }

      if (error.code === 'ECONNABORTED' || error.message.includes('timeout')) {
        throw new AppException(
          ErrorCode.OPENAI_TIMEOUT,
          'Kết nối đến Meta AI API bị gián đoạn hoặc quá thời gian chờ',
        );
      }

      if (error.code === 'ENOTFOUND' || error.message.includes('network')) {
        throw new AppException(
          ErrorCode.OPENAI_TIMEOUT,
          'Lỗi kết nối đến Meta AI API',
        );
      }

      // Các lỗi khác
      throw new AppException(
        ErrorCode.OPENAI_API_ERROR,
        'Lỗi khi lấy danh sách model: ' + error.message,
      );
    }
  }
}
