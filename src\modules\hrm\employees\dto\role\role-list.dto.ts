import { ApiProperty } from '@nestjs/swagger';
import { QueryDto } from '@/common/dto/query.dto';
import { IsOptional, IsString } from 'class-validator';
import { Exclude, Expose } from 'class-transformer';

/**
 * DTO cho truy vấn danh sách vai trò
 */
export class RoleQueryDto extends QueryDto {
  @ApiProperty({
    description: 'Tên vai trò',
    required: false,
    example: 'Admin',
  })
  @IsOptional()
  @IsString()
  name?: string;
}

/**
 * DTO đại diện cho thông tin vai trò trong danh sách
 */
@Exclude()
export class RoleListItemDto {
  /**
   * ID của vai trò
   */
  @Expose()
  @ApiProperty({
    description: 'ID của vai trò',
    example: 1,
  })
  id: number;

  /**
   * Tên vai trò
   */
  @Expose()
  @ApiProperty({
    description: 'Tên vai trò',
    example: 'Admin',
  })
  name: string;

  /**
   * <PERSON>ô tả vai trò
   */
  @Expose()
  @ApiProperty({
    description: 'Mô tả vai trò',
    example: 'Quản trị viên với tất cả quyền',
    nullable: true,
  })
  description: string | null;

  /**
   * Loại vai trò
   */
  @Expose()
  @ApiProperty({
    description: 'Loại vai trò',
    example: 'ADMIN',
    nullable: true,
  })
  type: string | null;

  /**
   * Thời gian tạo vai trò (timestamp)
   */
  @Expose()
  @ApiProperty({
    description: 'Thời gian tạo vai trò (timestamp)',
    example: 1625097600000,
    nullable: true,
  })
  createdAt: number | null;

  constructor(partial: Partial<RoleListItemDto>) {
    Object.assign(this, partial);
  }
}

/**
 * DTO đại diện cho thông tin chi tiết của vai trò bao gồm danh sách quyền
 */
@Exclude()
export class RoleDetailDto extends RoleListItemDto {
  /**
   * Danh sách ID của các quyền thuộc vai trò
   */
  @Expose()
  @ApiProperty({
    description: 'Danh sách ID của các quyền thuộc vai trò',
    example: [1, 2, 3],
    type: [Number],
  })
  permissionIds: number[];

  /**
   * Danh sách các quyền của vai trò theo định dạng 'module:action'
   */
  @Expose()
  @ApiProperty({
    description: 'Danh sách các quyền của vai trò theo định dạng module:action',
    example: ['user:view_list', 'user:view_detail'],
    type: [String],
  })
  permissions: string[];

  constructor(partial: Partial<RoleDetailDto>) {
    super(partial);
    Object.assign(this, partial);
  }
}
