import { ApiProperty } from '@nestjs/swagger';
import { IsEmail, IsString, IsOptional, IsArray } from 'class-validator';

/**
 * DTO cho request gửi email
 */
export class SendEmailDto {
  @ApiProperty({
    description: 'Email người nhận',
    example: '<EMAIL>',
  })
  @IsEmail()
  to: string;

  @ApiProperty({
    description: 'Danh sách email CC',
    example: ['<EMAIL>', '<EMAIL>'],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @IsEmail({}, { each: true })
  cc?: string[];

  @ApiProperty({
    description: 'Danh sách email BCC',
    example: ['<EMAIL>', '<EMAIL>'],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @IsEmail({}, { each: true })
  bcc?: string[];

  @ApiProperty({
    description: 'Tiêu đề email',
    example: 'Test Email',
  })
  @IsString()
  subject: string;

  @ApiProperty({
    description: 'Nội dung email',
    example: 'This is a test email',
  })
  @IsString()
  content: string;

  @ApiProperty({
    description: 'Nội dung email dạng HTML',
    example: '<h1>This is a test email</h1>',
    required: false,
  })
  @IsOptional()
  @IsString()
  html?: string;
}
