import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { AxiosRequestConfig } from 'axios';
import { BaseMarketingAiServiceImpl } from '../base-marketing-ai.service';
import {
  ContentGenerationOptions,
  ContentGenerationResult,
  ContentGenerationService,
  ContentType,
  ContentTone,
  ContentLanguage,
  MarketingAiResponse,
} from '../interfaces';

/**
 * Cohere service for content generation
 */
@Injectable()
export class CohereService
  extends BaseMarketingAiServiceImpl
  implements ContentGenerationService
{
  readonly serviceName = 'Cohere';
  protected readonly baseUrl = 'https://api.cohere.ai/v1';
  protected readonly apiKey: string | undefined;

  constructor(private readonly configService: ConfigService) {
    super(CohereService.name);
    this.apiKey = this.configService.get<string>('COHERE_API_KEY');

    if (!this.apiKey) {
      this.logger.warn(
        'COHERE_API_KEY is not defined in environment variables',
      );
    }
  }

  /**
   * Test the connection to the Cohere API
   * @returns A promise that resolves to a boolean indicating if the connection was successful
   */
  async testConnection(): Promise<boolean> {
    try {
      const url = `${this.baseUrl}/tokenize`;
      const config = this.createRequestConfig();

      const data = {
        text: 'Hello, world!',
      };

      const response = await this.axiosInstance.post(url, data, config);
      return response.status === 200;
    } catch (error) {
      this.logger.error(
        `Connection test failed: ${error.message}`,
        error.stack,
      );
      return false;
    }
  }

  /**
   * Create a request configuration with authentication
   * @param config Additional request configuration
   * @returns Request configuration with authentication
   */
  protected createRequestConfig(
    config?: AxiosRequestConfig,
  ): AxiosRequestConfig {
    if (!this.apiKey) {
      throw new Error('Cohere API key is not defined');
    }

    return {
      ...config,
      headers: {
        ...config?.headers,
        Authorization: `Bearer ${this.apiKey}`,
        'Content-Type': 'application/json',
        Accept: 'application/json',
      },
    };
  }

  /**
   * Create a prompt based on content options
   * @param prompt User prompt
   * @param contentType Content type
   * @param tone Content tone
   * @param language Content language
   * @param audience Target audience
   * @param keywords Keywords to include
   * @returns Enhanced prompt
   */
  private createEnhancedPrompt(
    prompt: string,
    contentType: ContentType = ContentType.BLOG_POST,
    tone: ContentTone = ContentTone.PROFESSIONAL,
    language: ContentLanguage = ContentLanguage.ENGLISH,
    audience?: string,
    keywords?: string[],
  ): string {
    let languageName = 'English';
    switch (language) {
      case ContentLanguage.VIETNAMESE:
        languageName = 'Vietnamese';
        break;
      case ContentLanguage.FRENCH:
        languageName = 'French';
        break;
      case ContentLanguage.GERMAN:
        languageName = 'German';
        break;
      case ContentLanguage.SPANISH:
        languageName = 'Spanish';
        break;
      case ContentLanguage.ITALIAN:
        languageName = 'Italian';
        break;
      case ContentLanguage.PORTUGUESE:
        languageName = 'Portuguese';
        break;
      case ContentLanguage.RUSSIAN:
        languageName = 'Russian';
        break;
      case ContentLanguage.JAPANESE:
        languageName = 'Japanese';
        break;
      case ContentLanguage.KOREAN:
        languageName = 'Korean';
        break;
      case ContentLanguage.CHINESE:
        languageName = 'Chinese';
        break;
    }

    let contentTypeDescription = '';
    let contentStructure = '';

    switch (contentType) {
      case ContentType.BLOG_POST:
        contentTypeDescription = 'a blog post';
        contentStructure =
          'Include a catchy title, introduction, main sections with subheadings, and a conclusion.';
        break;
      case ContentType.SOCIAL_MEDIA:
        contentTypeDescription = 'a social media post';
        contentStructure =
          'Keep it concise, engaging, and include relevant hashtags.';
        break;
      case ContentType.EMAIL:
        contentTypeDescription = 'an email';
        contentStructure =
          'Include a subject line, greeting, body, and call-to-action.';
        break;
      case ContentType.AD_COPY:
        contentTypeDescription = 'an advertisement';
        contentStructure =
          'Include a headline, main copy, and a compelling call-to-action.';
        break;
      case ContentType.PRODUCT_DESCRIPTION:
        contentTypeDescription = 'a product description';
        contentStructure =
          'Highlight features, benefits, and unique selling points.';
        break;
      case ContentType.HEADLINE:
        contentTypeDescription = 'a headline';
        contentStructure = 'Make it attention-grabbing, concise, and clear.';
        break;
      case ContentType.LANDING_PAGE:
        contentTypeDescription = 'landing page content';
        contentStructure =
          'Include a headline, subheadline, benefits, features, testimonials, and call-to-action sections.';
        break;
      case ContentType.PRESS_RELEASE:
        contentTypeDescription = 'a press release';
        contentStructure =
          'Include a headline, dateline, introduction, body, company information, and contact details.';
        break;
      case ContentType.SEO_CONTENT:
        contentTypeDescription = 'SEO-optimized content';
        contentStructure =
          'Include a title with the main keyword, meta description, headings with keywords, and naturally incorporate keywords throughout.';
        break;
      case ContentType.CUSTOM:
        contentTypeDescription = 'custom content';
        contentStructure = 'Follow the specific requirements in the prompt.';
        break;
    }

    let enhancedPrompt = `Create ${contentTypeDescription} in ${languageName} with a ${tone.toLowerCase()} tone.`;

    if (audience) {
      enhancedPrompt += ` The target audience is ${audience}.`;
    }

    if (keywords && keywords.length > 0) {
      enhancedPrompt += ` Incorporate the following keywords naturally: ${keywords.join(', ')}.`;
    }

    enhancedPrompt += ` ${contentStructure}`;

    if (
      contentType === ContentType.BLOG_POST ||
      contentType === ContentType.LANDING_PAGE ||
      contentType === ContentType.SEO_CONTENT
    ) {
      enhancedPrompt += ` Also provide a meta description for SEO purposes.`;
    }

    enhancedPrompt += `\n\nPrompt: ${prompt}`;

    return enhancedPrompt;
  }

  /**
   * Generate content from a text prompt using Cohere
   * @param prompt Text prompt to generate content from
   * @param options Options for content generation
   * @returns A promise that resolves to a response containing the generated content
   */
  async generateContent(
    prompt: string,
    options?: ContentGenerationOptions,
  ): Promise<MarketingAiResponse<ContentGenerationResult>> {
    try {
      const contentType = options?.contentType || ContentType.BLOG_POST;
      const tone = options?.tone || ContentTone.PROFESSIONAL;
      const language = options?.language || ContentLanguage.ENGLISH;

      const enhancedPrompt = this.createEnhancedPrompt(
        prompt,
        contentType,
        tone,
        language,
        options?.audience,
        options?.keywords,
      );

      const url = `${this.baseUrl}/generate`;

      const data = {
        model: 'command',
        prompt: enhancedPrompt,
        max_tokens: options?.maxLength
          ? Math.min(Math.floor(options.maxLength / 4), 4000)
          : 2000,
        temperature: options?.temperature || 0.7,
        k: 0,
        p: 0.75,
        frequency_penalty: 0,
        presence_penalty: 0,
        return_likelihoods: 'NONE',
      };

      const config = this.createRequestConfig({
        timeout: options?.timeout || 60000,
      });

      const response = await this.axiosInstance.post(url, data, config);

      const content = response.data.generations[0]?.text || '';

      // Extract title and meta description if available
      let title = '';
      let metaDescription = '';

      // Simple regex to extract title (first line that starts with # or is in quotes)
      const titleMatch = content.match(/^#\s*(.+)$|^"(.+)"$/m);
      if (titleMatch) {
        title = titleMatch[1] || titleMatch[2];
      }

      // Simple regex to extract meta description (line that starts with "Meta description:" or similar)
      const metaMatch = content.match(/Meta\s*description:?\s*(.+)$/im);
      if (metaMatch) {
        metaDescription = metaMatch[1];
      }

      // Create result
      const result: ContentGenerationResult = {
        content,
        title,
        metaDescription,
        keywords: options?.keywords,
        contentType,
      };

      return this.createSuccessResponse(result, response.data);
    } catch (error) {
      return this.handleApiError<ContentGenerationResult>(
        error,
        'Cohere content generation',
      );
    }
  }

  /**
   * Edit existing content using a text prompt
   * @param content Existing content to edit
   * @param prompt Text prompt to guide the editing
   * @param options Options for content editing
   * @returns A promise that resolves to a response containing the edited content
   */
  async editContent(
    content: string,
    prompt: string,
    options?: ContentGenerationOptions,
  ): Promise<MarketingAiResponse<ContentGenerationResult>> {
    try {
      const contentType = options?.contentType || ContentType.BLOG_POST;
      const tone = options?.tone || ContentTone.PROFESSIONAL;
      const language = options?.language || ContentLanguage.ENGLISH;

      const enhancedPrompt = `Edit the following content. Maintain the ${tone.toLowerCase()} tone and ensure it's in ${language}. Return the edited content in full.\n\nContent to edit:\n\n${content}\n\nEdit instructions: ${prompt}`;

      const url = `${this.baseUrl}/generate`;

      const data = {
        model: 'command',
        prompt: enhancedPrompt,
        max_tokens: options?.maxLength
          ? Math.min(Math.floor(options.maxLength / 4), 4000)
          : 2000,
        temperature: options?.temperature || 0.7,
        k: 0,
        p: 0.75,
        frequency_penalty: 0,
        presence_penalty: 0,
        return_likelihoods: 'NONE',
      };

      const config = this.createRequestConfig({
        timeout: options?.timeout || 60000,
      });

      const response = await this.axiosInstance.post(url, data, config);

      const editedContent = response.data.generations[0]?.text || '';

      // Extract title and meta description if available
      let title = '';
      let metaDescription = '';

      // Simple regex to extract title (first line that starts with # or is in quotes)
      const titleMatch = editedContent.match(/^#\s*(.+)$|^"(.+)"$/m);
      if (titleMatch) {
        title = titleMatch[1] || titleMatch[2];
      }

      // Simple regex to extract meta description (line that starts with "Meta description:" or similar)
      const metaMatch = editedContent.match(/Meta\s*description:?\s*(.+)$/im);
      if (metaMatch) {
        metaDescription = metaMatch[1];
      }

      // Create result
      const result: ContentGenerationResult = {
        content: editedContent,
        title,
        metaDescription,
        keywords: options?.keywords,
        contentType,
      };

      return this.createSuccessResponse(result, response.data);
    } catch (error) {
      return this.handleApiError<ContentGenerationResult>(
        error,
        'Cohere content editing',
      );
    }
  }

  /**
   * Generate content variations from existing content
   * @param content Existing content to create variations from
   * @param options Options for content variation generation
   * @returns A promise that resolves to a response containing the content variations
   */
  async generateContentVariations(
    content: string,
    options?: ContentGenerationOptions,
  ): Promise<MarketingAiResponse<ContentGenerationResult>> {
    try {
      const contentType = options?.contentType || ContentType.BLOG_POST;
      const tone = options?.tone || ContentTone.PROFESSIONAL;
      const language = options?.language || ContentLanguage.ENGLISH;

      const enhancedPrompt = `Create a variation of the following content. Maintain the same overall message but use different wording and structure. Use a ${tone.toLowerCase()} tone and ensure it's in ${language}.\n\nContent to create a variation from:\n\n${content}`;

      const url = `${this.baseUrl}/generate`;

      const data = {
        model: 'command',
        prompt: enhancedPrompt,
        max_tokens: options?.maxLength
          ? Math.min(Math.floor(options.maxLength / 4), 4000)
          : 2000,
        temperature: options?.temperature || 0.7,
        k: 0,
        p: 0.75,
        frequency_penalty: 0,
        presence_penalty: 0,
        return_likelihoods: 'NONE',
      };

      const config = this.createRequestConfig({
        timeout: options?.timeout || 60000,
      });

      const response = await this.axiosInstance.post(url, data, config);

      const variationContent = response.data.generations[0]?.text || '';

      // Extract title and meta description if available
      let title = '';
      let metaDescription = '';

      // Simple regex to extract title (first line that starts with # or is in quotes)
      const titleMatch = variationContent.match(/^#\s*(.+)$|^"(.+)"$/m);
      if (titleMatch) {
        title = titleMatch[1] || titleMatch[2];
      }

      // Simple regex to extract meta description (line that starts with "Meta description:" or similar)
      const metaMatch = variationContent.match(
        /Meta\s*description:?\s*(.+)$/im,
      );
      if (metaMatch) {
        metaDescription = metaMatch[1];
      }

      // Create result
      const result: ContentGenerationResult = {
        content: variationContent,
        title,
        metaDescription,
        keywords: options?.keywords,
        contentType,
      };

      return this.createSuccessResponse(result, response.data);
    } catch (error) {
      return this.handleApiError<ContentGenerationResult>(
        error,
        'Cohere content variation',
      );
    }
  }
}
