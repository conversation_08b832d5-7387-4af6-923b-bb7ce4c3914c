import { Entity, PrimaryGeneratedColumn, Column, Index } from 'typeorm';

@Entity('document_folders')
@Index(['name', 'parentId', 'tenantId'], { unique: true })
@Index(['tenantId'])
@Index(['parentId'])
@Index(['path'])
@Index(['level'])
@Index(['isActive', 'tenantId'])
export class DocumentFolder {
  /**
   * Khóa chính
   */
  @PrimaryGeneratedColumn()
  id: number;

  /**
   * Tên thư mục
   */
  @Column({ name: 'name', type: 'varchar', length: 255 })
  name: string;

  /**
   * <PERSON><PERSON> tả thư mục
   */
  @Column({ name: 'description', type: 'text', nullable: true })
  description: string | null;

  /**
   * ID thư mục cha cho cấu trúc phân cấp
   */
  @Column({ name: 'parent_id', type: 'integer', nullable: true })
  parentId: number | null;

  /**
   * Đường dẫn phân cấp được tính tự động
   */
  @Column({ name: 'path', type: 'varchar', length: 1000 })
  path: string;

  /**
   * Cấp độ sâu trong phân cấp (0 = cấp gốc)
   */
  @Column({ name: 'level', type: 'integer', default: 0 })
  level: number;

  /**
   * Thứ tự sắp xếp trong cùng cấp độ
   */
  @Column({ name: 'sort_order', type: 'integer', default: 0 })
  sortOrder: number;

  /**
   * Thư mục có đang hoạt động không
   */
  @Column({ name: 'is_active', type: 'boolean', default: true })
  isActive: boolean;

  /**
   * Metadata bổ sung của thư mục
   */
  @Column({ name: 'metadata', type: 'jsonb', nullable: true })
  metadata: Record<string, any> | null;

  /**
   * Thời điểm tạo (timestamp tính bằng milliseconds)
   */
  @Column({ name: 'created_at', type: 'bigint' })
  createdAt: number;

  /**
   * Thời điểm cập nhật cuối (timestamp tính bằng milliseconds)
   */
  @Column({ name: 'updated_at', type: 'bigint', nullable: true })
  updatedAt: number | null;

  /**
   * ID người dùng tạo thư mục này
   */
  @Column({ name: 'created_by', type: 'integer' })
  createdBy: number;

  /**
   * ID người dùng cập nhật thư mục này lần cuối
   */
  @Column({ name: 'updated_by', type: 'integer', nullable: true })
  updatedBy: number | null;

  /**
   * ID tenant để phân tách dữ liệu multi-tenant
   */
  @Column({ name: 'tenant_id', type: 'integer' })
  tenantId: number;

  // =====================================================
  // THUỘC TÍNH TÍNH TOÁN
  // =====================================================

  /**
   * Lấy đường dẫn thư mục đầy đủ dưới dạng mảng
   */
  get pathArray(): string[] {
    return this.path.split('/').filter(Boolean);
  }

  /**
   * Kiểm tra có phải thư mục gốc không
   */
  get isRoot(): boolean {
    return this.level === 0 && this.parentId === null;
  }
}
