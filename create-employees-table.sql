-- T<PERSON><PERSON> c<PERSON>c enum types trước khi tạo bảng
DO $$
BEGIN
    -- Tạo enum cho employee_status
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'employee_status_enum') THEN
        CREATE TYPE employee_status_enum AS ENUM (
            'active', 'inactive', 'on_leave', 'terminated', 'probation', 'suspended'
        );
        COMMENT ON TYPE employee_status_enum IS 'Trạng thái của nhân viên: đang làm việ<PERSON>, không hoạt động, nghỉ phép, đã nghỉ việc, thử việc, tạm ngưng';
    END IF;

    -- Tạo enum cho employment_type
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'employment_type_enum') THEN
        CREATE TYPE employment_type_enum AS ENUM (
            'full_time', 'part_time', 'contract', 'temporary', 'intern', 'freelance'
        );
        COMMENT ON TYPE employment_type_enum IS 'Loạ<PERSON> hình làm việc: to<PERSON><PERSON> thờ<PERSON> gian, b<PERSON>ờ<PERSON> gian, h<PERSON><PERSON> đ<PERSON>, t<PERSON><PERSON> thời, thực tậ<PERSON> sinh, tự do';
    END IF;

    -- Tạo enum cho marital_status
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'marital_status_enum') THEN
        CREATE TYPE marital_status_enum AS ENUM (
            'single', 'married', 'divorced', 'widowed', 'separated'
        );
        COMMENT ON TYPE marital_status_enum IS 'Tình trạng hôn nhân: độc thân, đã kết hôn, đã ly hôn, góa, ly thân';
    END IF;
END$$;

-- Tạo bảng employees
CREATE TABLE IF NOT EXISTS employees (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL,
    employee_code VARCHAR(50) NOT NULL,
    department_id INTEGER,
    job_title VARCHAR(255),
    job_level VARCHAR(50),
    manager_id INTEGER,
    employment_type employment_type_enum,
    status employee_status_enum NOT NULL DEFAULT 'active',
    hire_date DATE,
    termination_date DATE,
    termination_reason VARCHAR(500),
    probation_end_date DATE,
    marital_status marital_status_enum,
    number_of_dependents INTEGER,
    emergency_contact_name VARCHAR(255),
    emergency_contact_phone VARCHAR(20),
    emergency_contact_relationship VARCHAR(100),
    notes TEXT,
    created_at BIGINT,
    updated_at BIGINT,
    created_by INTEGER,
    updated_by INTEGER,
    tenant_id INTEGER
);

-- Thêm các ràng buộc
ALTER TABLE employees ADD CONSTRAINT employees_employee_code_unique UNIQUE (employee_code, tenant_id);
ALTER TABLE employees ADD CONSTRAINT employees_user_id_fk FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE;
ALTER TABLE employees ADD CONSTRAINT employees_department_id_fk FOREIGN KEY (department_id) REFERENCES departments(id) ON DELETE SET NULL;
ALTER TABLE employees ADD CONSTRAINT employees_manager_id_fk FOREIGN KEY (manager_id) REFERENCES employees(id) ON DELETE SET NULL;
ALTER TABLE employees ADD CONSTRAINT employees_created_by_fk FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL;
ALTER TABLE employees ADD CONSTRAINT employees_updated_by_fk FOREIGN KEY (updated_by) REFERENCES users(id) ON DELETE SET NULL;
ALTER TABLE employees ADD CONSTRAINT employees_tenant_id_fk FOREIGN KEY (tenant_id) REFERENCES companies(id) ON DELETE CASCADE;

-- Thêm comment cho bảng
COMMENT ON TABLE employees IS 'Bảng lưu trữ thông tin nhân viên trong hệ thống';

-- Thêm comment cho các cột
COMMENT ON COLUMN employees.id IS 'Định danh duy nhất cho nhân viên';
COMMENT ON COLUMN employees.user_id IS 'ID của người dùng liên kết với nhân viên này. Mỗi nhân viên phải có một tài khoản người dùng tương ứng';
COMMENT ON COLUMN employees.employee_code IS 'Mã nhân viên (định danh duy nhất trong ngữ cảnh HR). Thường được sử dụng trong các tài liệu chính thức và giao tiếp nội bộ';
COMMENT ON COLUMN employees.department_id IS 'ID của phòng ban mà nhân viên thuộc về. Có thể null nếu nhân viên chưa được phân công vào phòng ban cụ thể';
COMMENT ON COLUMN employees.job_title IS 'Chức danh công việc của nhân viên. Ví dụ: Kỹ sư phần mềm, Quản lý dự án, Giám đốc điều hành, v.v.';
COMMENT ON COLUMN employees.job_level IS 'Cấp bậc công việc của nhân viên. Ví dụ: Junior, Middle, Senior, Lead, v.v.';
COMMENT ON COLUMN employees.manager_id IS 'ID của người quản lý trực tiếp của nhân viên. Tham chiếu đến một nhân viên khác trong hệ thống';
COMMENT ON COLUMN employees.employment_type IS 'Loại hình làm việc của nhân viên: toàn thời gian, bán thời gian, hợp đồng, tạm thời, thực tập sinh, tự do';
COMMENT ON COLUMN employees.status IS 'Trạng thái của nhân viên: đang làm việc, không hoạt động, nghỉ phép, đã nghỉ việc, thử việc, tạm ngưng';
COMMENT ON COLUMN employees.hire_date IS 'Ngày nhân viên bắt đầu làm việc. Được sử dụng để tính thâm niên và các quyền lợi dựa trên thời gian làm việc';
COMMENT ON COLUMN employees.termination_date IS 'Ngày nhân viên nghỉ việc (nếu có). Chỉ được điền khi nhân viên đã nghỉ việc (status = terminated)';
COMMENT ON COLUMN employees.termination_reason IS 'Lý do nghỉ việc (nếu có). Ghi chú về lý do nhân viên rời khỏi công ty';
COMMENT ON COLUMN employees.probation_end_date IS 'Ngày kết thúc thời gian thử việc của nhân viên. Được sử dụng để theo dõi quá trình thử việc và chuyển đổi sang nhân viên chính thức';
COMMENT ON COLUMN employees.marital_status IS 'Tình trạng hôn nhân của nhân viên: độc thân, đã kết hôn, đã ly hôn, góa, ly thân';
COMMENT ON COLUMN employees.number_of_dependents IS 'Số người phụ thuộc. Được sử dụng cho mục đích tính thuế và phúc lợi';
COMMENT ON COLUMN employees.emergency_contact_name IS 'Tên người liên hệ khẩn cấp. Người được liên hệ trong trường hợp khẩn cấp liên quan đến nhân viên';
COMMENT ON COLUMN employees.emergency_contact_phone IS 'Số điện thoại của người liên hệ khẩn cấp. Dùng để liên lạc trong trường hợp khẩn cấp';
COMMENT ON COLUMN employees.emergency_contact_relationship IS 'Mối quan hệ với người liên hệ khẩn cấp. Ví dụ: vợ/chồng, cha/mẹ, anh/chị/em, v.v.';
COMMENT ON COLUMN employees.notes IS 'Ghi chú về nhân viên. Thông tin bổ sung không phù hợp với các trường khác';
COMMENT ON COLUMN employees.created_at IS 'Thời điểm tạo bản ghi (tính bằng mili giây). Lưu trữ timestamp Unix khi bản ghi được tạo';
COMMENT ON COLUMN employees.updated_at IS 'Thời điểm cập nhật bản ghi lần cuối (tính bằng mili giây). Lưu trữ timestamp Unix khi bản ghi được cập nhật';
COMMENT ON COLUMN employees.created_by IS 'ID của người dùng đã tạo bản ghi này. Dùng để theo dõi người thực hiện thao tác tạo';
COMMENT ON COLUMN employees.updated_by IS 'ID của người dùng đã cập nhật bản ghi này lần cuối. Dùng để theo dõi người thực hiện thao tác cập nhật';
COMMENT ON COLUMN employees.tenant_id IS 'ID của công ty/tổ chức sở hữu bản ghi này. Dùng cho tính năng multi-tenant, phân tách dữ liệu giữa các công ty';

-- Tạo index để tối ưu truy vấn
CREATE INDEX idx_employees_user_id ON employees(user_id);
CREATE INDEX idx_employees_department_id ON employees(department_id);
CREATE INDEX idx_employees_manager_id ON employees(manager_id);
CREATE INDEX idx_employees_status ON employees(status);
CREATE INDEX idx_employees_tenant_id ON employees(tenant_id);
CREATE INDEX idx_employees_employee_code ON employees(employee_code);
