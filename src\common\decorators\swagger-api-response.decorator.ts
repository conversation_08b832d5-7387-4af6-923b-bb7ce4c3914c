import { applyDecorators, Type } from '@nestjs/common';
import { ApiResponse, getSchemaPath } from '@nestjs/swagger';
import { ApiResponseDto as ApiResponseClass } from '../response/api-response-dto';

interface SwaggerApiResponseOptions {
  type?: Type<any>;
  description?: string;
  isArray?: boolean;
}

/**
 * Custom decorator for Swagger API responses
 * Automatically wraps the response in the standard ApiResponse format
 */
export const SwaggerApiResponse = (options: SwaggerApiResponseOptions = {}) => {
  const {
    type,
    description = 'Successful operation',
    isArray = false,
  } = options;

  if (!type) {
    return applyDecorators(
      ApiResponse({
        status: 200,
        description,
        schema: {
          allOf: [
            { $ref: getSchemaPath(ApiResponseClass) },
            {
              properties: {
                result: {
                  type: 'object',
                  nullable: true,
                },
              },
            },
          ],
        },
      }),
    );
  }

  return applyDecorators(
    ApiResponse({
      status: 200,
      description,
      schema: {
        allOf: [
          { $ref: getSchemaPath(ApiResponseClass) },
          {
            properties: {
              result: isArray
                ? {
                    type: 'array',
                    items: { $ref: getSchemaPath(type) },
                  }
                : {
                    $ref: getSchemaPath(type),
                  },
            },
          },
        ],
      },
    }),
  );
};
