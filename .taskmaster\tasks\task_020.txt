# Task ID: 20
# Title: Update Email Module - Template and Log Repositories
# Status: done
# Dependencies: 1
# Priority: low
# Description: Add tenantId filtering to Email module repositories
# Details:
Update repository methods:
- EmailTemplateRepository: Add tenantId to all queries
- EmailLogRepository: Add tenantId filtering
- Handle tenant-specific email templates
- Update email history queries with tenantId
- Ensure email configurations are tenant-isolated

# Test Strategy:
Unit tests for Email repositories
