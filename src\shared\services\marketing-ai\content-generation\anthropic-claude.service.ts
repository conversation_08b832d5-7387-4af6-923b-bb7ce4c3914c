import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { AxiosRequestConfig } from 'axios';
import { BaseMarketingAiServiceImpl } from '../base-marketing-ai.service';
import {
  ContentGenerationOptions,
  ContentGenerationResult,
  ContentGenerationService,
  ContentType,
  ContentTone,
  ContentLanguage,
  MarketingAiResponse,
} from '../interfaces';

/**
 * Anthropic Claude service for content generation
 */
@Injectable()
export class AnthropicClaudeService
  extends BaseMarketingAiServiceImpl
  implements ContentGenerationService
{
  readonly serviceName = 'Anthropic Claude';
  protected readonly baseUrl = 'https://api.anthropic.com/v1';
  protected readonly apiKey: string | undefined;
  private readonly apiVersion = '2023-06-01';

  constructor(private readonly configService: ConfigService) {
    super(AnthropicClaudeService.name);
    this.apiKey = this.configService.get<string>('ANTHROPIC_API_KEY');

    if (!this.apiKey) {
      this.logger.warn(
        'ANTHROPIC_API_KEY is not defined in environment variables',
      );
    }
  }

  /**
   * Test the connection to the Anthropic API
   * @returns A promise that resolves to a boolean indicating if the connection was successful
   */
  async testConnection(): Promise<boolean> {
    try {
      // Simple test to check if the API key is valid
      // Anthropic doesn't have a dedicated endpoint for this, so we'll make a minimal request
      const url = `${this.baseUrl}/messages`;
      const config = this.createRequestConfig();

      const data = {
        model: 'claude-3-opus-20240229',
        max_tokens: 10,
        messages: [{ role: 'user', content: 'Hello' }],
      };

      const response = await this.axiosInstance.post(url, data, config);
      return response.status === 200;
    } catch (error) {
      this.logger.error(
        `Connection test failed: ${error.message}`,
        error.stack,
      );
      return false;
    }
  }

  /**
   * Create a request configuration with authentication
   * @param config Additional request configuration
   * @returns Request configuration with authentication
   */
  protected createRequestConfig(
    config?: AxiosRequestConfig,
  ): AxiosRequestConfig {
    if (!this.apiKey) {
      throw new Error('Anthropic API key is not defined');
    }

    return {
      ...config,
      headers: {
        ...config?.headers,
        'x-api-key': this.apiKey,
        'anthropic-version': this.apiVersion,
        'Content-Type': 'application/json',
      },
    };
  }

  /**
   * Create a system prompt based on content options
   * @param contentType Content type
   * @param tone Content tone
   * @param language Content language
   * @param audience Target audience
   * @param keywords Keywords to include
   * @returns System prompt
   */
  private createSystemPrompt(
    contentType: ContentType = ContentType.BLOG_POST,
    tone: ContentTone = ContentTone.PROFESSIONAL,
    language: ContentLanguage = ContentLanguage.ENGLISH,
    audience?: string,
    keywords?: string[],
  ): string {
    let languageName = 'English';
    switch (language) {
      case ContentLanguage.VIETNAMESE:
        languageName = 'Vietnamese';
        break;
      case ContentLanguage.FRENCH:
        languageName = 'French';
        break;
      case ContentLanguage.GERMAN:
        languageName = 'German';
        break;
      case ContentLanguage.SPANISH:
        languageName = 'Spanish';
        break;
      case ContentLanguage.ITALIAN:
        languageName = 'Italian';
        break;
      case ContentLanguage.PORTUGUESE:
        languageName = 'Portuguese';
        break;
      case ContentLanguage.RUSSIAN:
        languageName = 'Russian';
        break;
      case ContentLanguage.JAPANESE:
        languageName = 'Japanese';
        break;
      case ContentLanguage.KOREAN:
        languageName = 'Korean';
        break;
      case ContentLanguage.CHINESE:
        languageName = 'Chinese';
        break;
    }

    let contentTypeDescription = '';
    let contentStructure = '';

    switch (contentType) {
      case ContentType.BLOG_POST:
        contentTypeDescription = 'a blog post';
        contentStructure =
          'Include a catchy title, introduction, main sections with subheadings, and a conclusion.';
        break;
      case ContentType.SOCIAL_MEDIA:
        contentTypeDescription = 'a social media post';
        contentStructure =
          'Keep it concise, engaging, and include relevant hashtags.';
        break;
      case ContentType.EMAIL:
        contentTypeDescription = 'an email';
        contentStructure =
          'Include a subject line, greeting, body, and call-to-action.';
        break;
      case ContentType.AD_COPY:
        contentTypeDescription = 'an advertisement';
        contentStructure =
          'Include a headline, main copy, and a compelling call-to-action.';
        break;
      case ContentType.PRODUCT_DESCRIPTION:
        contentTypeDescription = 'a product description';
        contentStructure =
          'Highlight features, benefits, and unique selling points.';
        break;
      case ContentType.HEADLINE:
        contentTypeDescription = 'a headline';
        contentStructure = 'Make it attention-grabbing, concise, and clear.';
        break;
      case ContentType.LANDING_PAGE:
        contentTypeDescription = 'landing page content';
        contentStructure =
          'Include a headline, subheadline, benefits, features, testimonials, and call-to-action sections.';
        break;
      case ContentType.PRESS_RELEASE:
        contentTypeDescription = 'a press release';
        contentStructure =
          'Include a headline, dateline, introduction, body, company information, and contact details.';
        break;
      case ContentType.SEO_CONTENT:
        contentTypeDescription = 'SEO-optimized content';
        contentStructure =
          'Include a title with the main keyword, meta description, headings with keywords, and naturally incorporate keywords throughout.';
        break;
      case ContentType.CUSTOM:
        contentTypeDescription = 'custom content';
        contentStructure = 'Follow the specific requirements in the prompt.';
        break;
    }

    let systemPrompt = `You are a professional marketing content creator specializing in creating ${contentTypeDescription} in ${languageName}.`;

    systemPrompt += ` Write in a ${tone.toLowerCase()} tone.`;

    if (audience) {
      systemPrompt += ` The target audience is ${audience}.`;
    }

    if (keywords && keywords.length > 0) {
      systemPrompt += ` Incorporate the following keywords naturally: ${keywords.join(', ')}.`;
    }

    systemPrompt += ` ${contentStructure}`;

    systemPrompt += ` Provide the content in a well-structured format appropriate for ${contentTypeDescription}.`;

    if (
      contentType === ContentType.BLOG_POST ||
      contentType === ContentType.LANDING_PAGE ||
      contentType === ContentType.SEO_CONTENT
    ) {
      systemPrompt += ` Also provide a meta description for SEO purposes.`;
    }

    return systemPrompt;
  }

  /**
   * Generate content from a text prompt using Anthropic Claude
   * @param prompt Text prompt to generate content from
   * @param options Options for content generation
   * @returns A promise that resolves to a response containing the generated content
   */
  async generateContent(
    prompt: string,
    options?: ContentGenerationOptions,
  ): Promise<MarketingAiResponse<ContentGenerationResult>> {
    try {
      const contentType = options?.contentType || ContentType.BLOG_POST;
      const tone = options?.tone || ContentTone.PROFESSIONAL;
      const language = options?.language || ContentLanguage.ENGLISH;

      const systemPrompt = this.createSystemPrompt(
        contentType,
        tone,
        language,
        options?.audience,
        options?.keywords,
      );

      const url = `${this.baseUrl}/messages`;

      const data = {
        model: 'claude-3-opus-20240229',
        max_tokens: options?.maxLength
          ? Math.min(Math.floor(options.maxLength / 4), 4000)
          : 2000,
        temperature: options?.temperature || 0.7,
        system: systemPrompt,
        messages: [{ role: 'user', content: prompt }],
      };

      const config = this.createRequestConfig({
        timeout: options?.timeout || 60000,
      });

      const response = await this.axiosInstance.post(url, data, config);

      const content = response.data.content[0]?.text || '';

      // Extract title and meta description if available
      let title = '';
      let metaDescription = '';

      // Simple regex to extract title (first line that starts with # or is in quotes)
      const titleMatch = content.match(/^#\s*(.+)$|^"(.+)"$/m);
      if (titleMatch) {
        title = titleMatch[1] || titleMatch[2];
      }

      // Simple regex to extract meta description (line that starts with "Meta description:" or similar)
      const metaMatch = content.match(/Meta\s*description:?\s*(.+)$/im);
      if (metaMatch) {
        metaDescription = metaMatch[1];
      }

      // Create result
      const result: ContentGenerationResult = {
        content,
        title,
        metaDescription,
        keywords: options?.keywords,
        contentType,
      };

      return this.createSuccessResponse(result, response.data);
    } catch (error) {
      return this.handleApiError<ContentGenerationResult>(
        error,
        'Anthropic Claude content generation',
      );
    }
  }

  /**
   * Edit existing content using a text prompt
   * @param content Existing content to edit
   * @param prompt Text prompt to guide the editing
   * @param options Options for content editing
   * @returns A promise that resolves to a response containing the edited content
   */
  async editContent(
    content: string,
    prompt: string,
    options?: ContentGenerationOptions,
  ): Promise<MarketingAiResponse<ContentGenerationResult>> {
    try {
      const contentType = options?.contentType || ContentType.BLOG_POST;
      const tone = options?.tone || ContentTone.PROFESSIONAL;
      const language = options?.language || ContentLanguage.ENGLISH;

      const systemPrompt = `You are a professional editor. Edit the provided content according to the instructions. Maintain the ${tone.toLowerCase()} tone and ensure it's in ${language}. Return the edited content in full.`;

      const url = `${this.baseUrl}/messages`;

      const data = {
        model: 'claude-3-opus-20240229',
        max_tokens: options?.maxLength
          ? Math.min(Math.floor(options.maxLength / 4), 4000)
          : 2000,
        temperature: options?.temperature || 0.7,
        system: systemPrompt,
        messages: [
          {
            role: 'user',
            content: `Here is the content to edit:\n\n${content}\n\nEdit instructions: ${prompt}`,
          },
        ],
      };

      const config = this.createRequestConfig({
        timeout: options?.timeout || 60000,
      });

      const response = await this.axiosInstance.post(url, data, config);

      const editedContent = response.data.content[0]?.text || '';

      // Extract title and meta description if available
      let title = '';
      let metaDescription = '';

      // Simple regex to extract title (first line that starts with # or is in quotes)
      const titleMatch = editedContent.match(/^#\s*(.+)$|^"(.+)"$/m);
      if (titleMatch) {
        title = titleMatch[1] || titleMatch[2];
      }

      // Simple regex to extract meta description (line that starts with "Meta description:" or similar)
      const metaMatch = editedContent.match(/Meta\s*description:?\s*(.+)$/im);
      if (metaMatch) {
        metaDescription = metaMatch[1];
      }

      // Create result
      const result: ContentGenerationResult = {
        content: editedContent,
        title,
        metaDescription,
        keywords: options?.keywords,
        contentType,
      };

      return this.createSuccessResponse(result, response.data);
    } catch (error) {
      return this.handleApiError<ContentGenerationResult>(
        error,
        'Anthropic Claude content editing',
      );
    }
  }

  /**
   * Generate content variations from existing content
   * @param content Existing content to create variations from
   * @param options Options for content variation generation
   * @returns A promise that resolves to a response containing the content variations
   */
  async generateContentVariations(
    content: string,
    options?: ContentGenerationOptions,
  ): Promise<MarketingAiResponse<ContentGenerationResult>> {
    try {
      const contentType = options?.contentType || ContentType.BLOG_POST;
      const tone = options?.tone || ContentTone.PROFESSIONAL;
      const language = options?.language || ContentLanguage.ENGLISH;

      const systemPrompt = `You are a professional content creator. Create a variation of the provided content. Maintain the same overall message but use different wording and structure. Use a ${tone.toLowerCase()} tone and ensure it's in ${language}.`;

      const url = `${this.baseUrl}/messages`;

      const data = {
        model: 'claude-3-opus-20240229',
        max_tokens: options?.maxLength
          ? Math.min(Math.floor(options.maxLength / 4), 4000)
          : 2000,
        temperature: options?.temperature || 0.7,
        system: systemPrompt,
        messages: [
          {
            role: 'user',
            content: `Here is the content to create a variation from:\n\n${content}`,
          },
        ],
      };

      const config = this.createRequestConfig({
        timeout: options?.timeout || 60000,
      });

      const response = await this.axiosInstance.post(url, data, config);

      const variationContent = response.data.content[0]?.text || '';

      // Extract title and meta description if available
      let title = '';
      let metaDescription = '';

      // Simple regex to extract title (first line that starts with # or is in quotes)
      const titleMatch = variationContent.match(/^#\s*(.+)$|^"(.+)"$/m);
      if (titleMatch) {
        title = titleMatch[1] || titleMatch[2];
      }

      // Simple regex to extract meta description (line that starts with "Meta description:" or similar)
      const metaMatch = variationContent.match(
        /Meta\s*description:?\s*(.+)$/im,
      );
      if (metaMatch) {
        metaDescription = metaMatch[1];
      }

      // Create result
      const result: ContentGenerationResult = {
        content: variationContent,
        title,
        metaDescription,
        keywords: options?.keywords,
        contentType,
      };

      return this.createSuccessResponse(result, response.data);
    } catch (error) {
      return this.handleApiError<ContentGenerationResult>(
        error,
        'Anthropic Claude content variation',
      );
    }
  }
}
