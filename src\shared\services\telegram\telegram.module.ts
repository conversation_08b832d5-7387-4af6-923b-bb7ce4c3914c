import { Module, Global } from '@nestjs/common';
import { HttpModule } from '@nestjs/axios';
import { ConfigModule } from '@nestjs/config';
import { TelegramService } from './telegram.service';
import { TelegramBotService } from './telegram-bot.service';
import { TelegramWebhookService } from './telegram-webhook.service';
import { TelegramAgentService } from './telegram-agent.service';

/**
 * Module tích hợp Telegram API
 */
@Global()
@Module({
  imports: [
    HttpModule.register({
      timeout: 30000, // 30 seconds
      maxRedirects: 5,
    }),
    ConfigModule,
  ],
  providers: [
    TelegramService,
    TelegramBotService,
    TelegramWebhookService,
    TelegramAgentService,
  ],
  exports: [
    TelegramService,
    TelegramBotService,
    TelegramWebhookService,
    TelegramAgentService,
  ],
})
export class TelegramModule {}
