## 2.2.2.16 <PERSON><PERSON> tả use case Tạo công việc mới

**M<PERSON> tả ngắn:** Cho phép người dùng tạo một công việc mới trong hệ thống, có thể là công việc độc lập hoặc thuộc về dự án.

### Luồng các sự kiện:

#### Luồng cơ bản:
1) Khi người dùng chọn "Tạo công việc mới", hệ thống hiển thị form tạo công việc với các trường thông tin.
2) Người dùng nhập các thông tin công việc cần thiết: ti<PERSON><PERSON> đề, mô tả, người được giao, dự án (nếu có), đ<PERSON> ưu tiên, số sao kỳ vọng.
3) Người dùng có thể liên kết công việc với các key result (nế<PERSON> có) bằng cách chọn từ danh sách.
4) Ng<PERSON>ờ<PERSON> dùng nhấn "Tạo công việc" và hệ thống kiểm tra dữ liệu.
5) Hệ thống tạo công việc mới với trạng thái mặc định là "Chờ xử lý" (PENDING), lưu thời điểm tạo và hiển thị thông báo "Công việc đã được tạo thành công".
6) Use case kết thúc.

#### Luồng rẽ nhánh:
1) Nếu người dùng không nhập tiêu đề công việc, hệ thống hiển thị cảnh báo "Vui lòng nhập tiêu đề công việc".
2) Nếu người dùng chọn dự án mà họ không phải là thành viên, hệ thống hiển thị thông báo "Bạn không phải là thành viên của dự án này" và không cho phép tạo công việc.
3) Nếu người dùng nhấn "Hủy", hệ thống trở về màn hình danh sách công việc mà không tạo công việc mới.
4) Khi có lỗi hệ thống/DB, hiển thị thông báo "Lỗi hệ thống, vui lòng thử lại sau" và kết thúc use case.
5) Nếu người dùng chọn liên kết với key result không tồn tại, hệ thống hiển thị thông báo lỗi và yêu cầu chọn lại.

### Các yêu cầu đặc biệt:
- Cho phép người dùng thiết lập độ ưu tiên và mức đánh giá kỳ vọng (1-5 sao) cho công việc.
- Cho phép liên kết công việc với các key result để theo dõi tiến độ đạt mục tiêu.
- Hệ thống tự động gán người tạo và thời gian tạo công việc.

### Tiền điều kiện:
- Người dùng đã đăng nhập.
- Nếu công việc thuộc về dự án, người dùng phải là thành viên của dự án.

### Hậu điều kiện:
- Công việc mới được tạo với trạng thái "Chờ xử lý" (PENDING).
- Công việc xuất hiện trong danh sách công việc của người tạo và người được giao (nếu có).
- Nếu có liên kết với key result, công việc xuất hiện trong danh sách công việc liên quan của key result.

### Điểm mở rộng:
- Tích hợp với hệ thống thông báo để gửi email thông báo cho người được giao công việc.
- Cho phép thiết lập lặp lại cho công việc định kỳ (hàng ngày, hàng tuần, hàng tháng).
- Tích hợp công cụ ước tính thời gian hoàn thành dựa trên mức độ phức tạp và data AI. 