// Test script để kiểm tra validation
const axios = require('axios');

async function testValidation() {
  try {
    console.log('Testing validation with invalid data...');
    
    // Test với data không hợp lệ
    const response = await axios.post('http://localhost:3001/v1/api/hrm/employees', {
      // Thiếu employeeCode (required)
      // Thiếu employeeName (required)
      departmentId: "invalid_number", // Sai kiểu dữ liệu
      jobTitle: "A".repeat(300), // Qu<PERSON> dài (max 255)
    }, {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer test-token' // Fake token để test
      }
    });
    
    console.log('Response:', response.data);
  } catch (error) {
    console.log('Error response:', error.response?.data || error.message);
  }
}

testValidation();
