# Task ID: 6
# Title: Update OKRs Module - Controllers Layer
# Status: done
# Dependencies: 5
# Priority: high
# Description: Update OKRs controllers to use @CurrentTenant() and pass tenantId to services
# Details:
Update controller classes:
- ObjectiveController: Add @CurrentTenant() to all endpoints
- OkrCycleController: Update to extract and pass tenantId
- KeyResultController: Add tenantId handling
- Update API documentation to reflect tenantId requirements
- Ensure all endpoints properly validate tenant access

# Test Strategy:
API tests to verify tenant isolation at controller level
