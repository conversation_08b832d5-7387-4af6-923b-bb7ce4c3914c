import { ApiProperty } from '@nestjs/swagger';
import {
  IsNotEmpty,
  IsEnum,
  IsOptional,
  IsString,
  MaxLength,
} from 'class-validator';
import { ContractStatus } from '../enum/contract-status.enum';

/**
 * DTO for updating contract status
 */
export class UpdateContractStatusDto {
  /**
   * New contract status
   * @example "active"
   */
  @ApiProperty({
    description: 'New contract status',
    enum: ContractStatus,
    example: ContractStatus.ACTIVE,
  })
  @IsNotEmpty()
  @IsEnum(ContractStatus)
  status: ContractStatus;

  /**
   * Reason for status change
   * @example "Contract approved by HR department"
   */
  @ApiProperty({
    required: false,
    description: 'Reason for status change',
    example: 'Contract approved by HR department',
  })
  @IsOptional()
  @IsString()
  @MaxLength(500)
  reason?: string;
}
