/**
 * Enum định nghĩa các sự kiện Socket.IO chuẩn
 */
export enum SocketEvents {
  // Sự kiện kết nối
  CONNECTION = 'connection',
  DISCONNECT = 'disconnect',

  // Sự kiện phòng
  JOIN_ROOM = 'join_room',
  LEAVE_ROOM = 'leave_room',
  ROOM_JOINED = 'room_joined',
  ROOM_LEFT = 'room_left',

  // Sự kiện tin nhắn
  MESSAGE = 'message',
  MESSAGE_RECEIVED = 'message_received',

  // Sự kiện thông báo
  NOTIFICATION = 'notification',

  // Sự kiện lỗi
  ERROR = 'error',

  // Sự kiện trạng thái
  USER_CONNECTED = 'user_connected',
  USER_DISCONNECTED = 'user_disconnected',
  USER_STATUS_CHANGED = 'user_status_changed',

  // Sự kiện gõ phím
  TYPING = 'typing',
  STOP_TYPING = 'stop_typing',

  // Sự kiện đọc tin nhắn
  READ_MESSAGE = 'read_message',
  MESSAGE_READ = 'message_read',
}
