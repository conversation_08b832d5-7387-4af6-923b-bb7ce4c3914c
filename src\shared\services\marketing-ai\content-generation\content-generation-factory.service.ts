import { Injectable } from '@nestjs/common';
import { ContentGenerationService } from '../interfaces';
import { OpenAiGptService } from './openai-gpt.service';
import { AnthropicClaudeService } from './anthropic-claude.service';
import { CohereService } from './cohere.service';

/**
 * Enum for content generation provider types
 */
export enum ContentGenerationProviderType {
  OPENAI_GPT = 'OPENAI_GPT',
  ANTHROPIC_CLAUDE = 'ANTHROPIC_CLAUDE',
  COHERE = 'COHERE',
}

/**
 * Factory service for creating content generation services
 */
@Injectable()
export class ContentGenerationFactoryService {
  constructor(
    private readonly openAiGptService: OpenAiGptService,
    private readonly anthropicClaudeService: AnthropicClaudeService,
    private readonly cohereService: CohereService,
  ) {}

  /**
   * Get a content generation service by provider type
   * @param providerType Provider type
   * @returns Content generation service
   * @throws Error if provider type is not supported
   */
  getProvider(
    providerType: ContentGenerationProviderType,
  ): ContentGenerationService {
    switch (providerType) {
      case ContentGenerationProviderType.OPENAI_GPT:
        return this.openAiGptService;
      case ContentGenerationProviderType.ANTHROPIC_CLAUDE:
        return this.anthropicClaudeService;
      case ContentGenerationProviderType.COHERE:
        return this.cohereService;
      default:
        throw new Error(
          `Unsupported content generation provider type: ${providerType}`,
        );
    }
  }

  /**
   * Get all available content generation services
   * @returns Array of content generation services
   */
  getAllProviders(): ContentGenerationService[] {
    return [
      this.openAiGptService,
      this.anthropicClaudeService,
      this.cohereService,
    ];
  }

  /**
   * Get all available provider types
   * @returns Array of provider types
   */
  getAllProviderTypes(): ContentGenerationProviderType[] {
    return [
      ContentGenerationProviderType.OPENAI_GPT,
      ContentGenerationProviderType.ANTHROPIC_CLAUDE,
      ContentGenerationProviderType.COHERE,
    ];
  }
}
