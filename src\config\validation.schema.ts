import * as Jo<PERSON> from 'joi';
import { Environment } from './constants';

// <PERSON><PERSON>m tra xem có đang chạy trong môi trường test không
const isTestEnvironment = process.env.NODE_ENV === Environment.Test;

/**
 * Schema validation cho các biến môi trường
 */
export const validationSchema = Joi.object({
  // App
  NODE_ENV: Joi.string()
    .valid(
      Environment.Development,
      Environment.Production,
      Environment.Test,
      Environment.Staging,
    )
    .default(Environment.Development),
  PORT: Joi.number().default(3000),
  API_PREFIX: Joi.string().default('api/v1'),

  // Database
  DB_HOST: isTestEnvironment
    ? Joi.string().default('localhost')
    : Joi.string().required(),
  DB_PORT: Joi.number().default(5432),
  DB_USERNAME: isTestEnvironment
    ? Joi.string().default('test')
    : Joi.string().required(),
  DB_PASSWORD: isTestEnvironment
    ? Joi.string().default('test')
    : Joi.string().required(),
  DB_DATABASE: isTestEnvironment
    ? Joi.string().default('test_db')
    : Joi.string().required(),
  DB_SSL: Joi.boolean().default(false),

  // CloudFlare R2
  CF_R2_ACCESS_KEY: isTestEnvironment
    ? Joi.string().default('mock_access_key')
    : Joi.string().required(),
  CF_R2_SECRET_KEY: isTestEnvironment
    ? Joi.string().default('mock_secret_key')
    : Joi.string().required(),
  CF_R2_ENDPOINT: isTestEnvironment
    ? Joi.string().default('https://mock-endpoint.com')
    : Joi.string().required(),
  CF_BUCKET_NAME: isTestEnvironment
    ? Joi.string().default('mock-bucket')
    : Joi.string().required(),

  // OpenAI
  OPENAI_API_KEY: isTestEnvironment
    ? Joi.string().default('mock_openai_key')
    : Joi.string().required(),
  OPENAI_ORGANIZATION_ID: Joi.string().optional(),

  // Anthropic
  ANTHROPIC_API_KEY: Joi.string().optional(),

  // Google AI
  GOOGLE_AI_API_KEY: Joi.string().optional(),

  // DeepSeek
  DEEPSEEK_API_KEY: Joi.string().optional(),

  // Meta AI
  META_AI_API_KEY: Joi.string().optional(),

  // CDN
  CDN_URL: isTestEnvironment
    ? Joi.string().default('https://mock-cdn.com')
    : Joi.string().required(),
  CDN_SECRET_KEY: isTestEnvironment
    ? Joi.string().default('mock_cdn_key')
    : Joi.string().required(),

  // JWT Authentication
  JWT_SECRET: isTestEnvironment
    ? Joi.string().default('mock_jwt_secret')
    : Joi.string().required(),
  JWT_EXPIRATION_TIME: Joi.string().default('1d'),
  JWT_REFRESH_SECRET: Joi.string().optional(),
  JWT_REFRESH_EXPIRATION_TIME: Joi.string().optional().default('7d'),

  // Redis
  REDIS_URL: Joi.string().optional(),
  REDIS_PASSWORD: Joi.string().optional(),

  // Email
  EXTERNAL_EMAIL_API_URL: Joi.string().optional(),
  SMTP_HOST: Joi.string().optional(),
  SMTP_PORT: Joi.number().optional(),
  SMTP_USER: Joi.string().optional(),
  SMTP_PASS: Joi.string().optional(),
});
