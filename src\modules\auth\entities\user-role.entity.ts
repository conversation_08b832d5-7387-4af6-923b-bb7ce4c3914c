import { Entity, PrimaryGeneratedColumn, Column } from 'typeorm';

/**
 * Entity representing the link between users and roles
 */
@Entity('user_roles')
export class UserRole {
  /**
   * Unique identifier for the link
   */
  @PrimaryGeneratedColumn()
  id: number;

  /**
   * ID of the user
   */
  @Column({ name: 'user_id', type: 'integer', nullable: true })
  userId: number | null;

  /**
   * ID of the role
   */
  @Column({ name: 'role_id', type: 'integer', nullable: true })
  roleId: number | null;

  /**
   * Creation timestamp (in milliseconds)
   */
  @Column({ name: 'created_at', type: 'bigint', nullable: true })
  createdAt: number | null;

  /**
   * ID of the company/organization that owns this record
   */
  @Column({ name: 'tenant_id', type: 'integer', nullable: true })
  tenantId: number | null;
}
