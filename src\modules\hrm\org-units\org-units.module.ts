import { Module, Global } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AuthModule } from '@/modules/auth/auth.module';
import { Department } from './entities/department.entity';
import { DepartmentController } from './controllers/department.controller';
import { DepartmentService } from './services/department.service';
import { DepartmentRepository } from './repositories/department.repository';
import { DepartmentMembersService } from './services/department-members.service';
import { DepartmentMembersController } from './controllers/department-members.controller';
import { DepartmentTreeService } from './services/department-tree.service';
import { DepartmentTreeController } from './controllers/department-tree.controller';

/**
 * Module quản lý đơn vị tổ chức
 */
@Global()
@Module({
  imports: [AuthModule, TypeOrmModule.forFeature([Department])],
  controllers: [
    DepartmentController,
    DepartmentMembersController,
    DepartmentTreeController,
  ],
  providers: [
    DepartmentService,
    DepartmentRepository,
    DepartmentMembersService,
    DepartmentTreeService,
  ],
  exports: [DepartmentService, DepartmentMembersService, DepartmentTreeService],
})
export class OrgUnitsModule {}
