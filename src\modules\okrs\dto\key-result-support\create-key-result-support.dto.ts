import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsNotEmpty, IsNumber } from 'class-validator';

/**
 * DTO for creating key result support relationships
 */
export class CreateKeyResultSupportDto {
  /**
   * IDs of the key results that support the parent key result
   * @example [2, 3, 4]
   */
  @ApiProperty({
    description: 'Danh sách ID của các kết quả chính hỗ trợ',
    example: [2, 3, 4],
    type: [Number],
  })
  @IsArray()
  @IsNumber({}, { each: true })
  @IsNotEmpty()
  childIds: number[];
}
