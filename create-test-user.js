// Script để tạo user test cho việc test API
const http = require('http');

// Cấu hình test
const BASE_URL = 'http://localhost:3001';

// Function để gọi API
function makeRequest(path, method = 'GET', data = null, token = null) {
  return new Promise((resolve, reject) => {
    const url = new URL(BASE_URL + path);
    const options = {
      hostname: url.hostname,
      port: url.port,
      path: url.pathname + url.search,
      method: method,
      headers: {
        'Content-Type': 'application/json',
      }
    };

    if (token) {
      options.headers['Authorization'] = `Bearer ${token}`;
    }

    if (data) {
      const postData = JSON.stringify(data);
      options.headers['Content-Length'] = Buffer.byteLength(postData);
    }

    const req = http.request(options, (res) => {
      let responseData = '';
      
      res.on('data', (chunk) => {
        responseData += chunk;
      });
      
      res.on('end', () => {
        try {
          const parsedData = JSON.parse(responseData);
          resolve({
            status: res.statusCode,
            headers: res.headers,
            data: parsedData
          });
        } catch (e) {
          resolve({
            status: res.statusCode,
            headers: res.headers,
            data: responseData
          });
        }
      });
    });

    req.on('error', (err) => {
      reject(err);
    });

    if (data) {
      req.write(JSON.stringify(data));
    }
    
    req.end();
  });
}

async function createTestUser() {
  console.log('🚀 Tạo user test cho việc test API...\n');

  // Test 1: Tạo user admin
  console.log('👤 Test 1: Tạo user admin');
  try {
    const userData = {
      username: 'admin',
      email: '<EMAIL>',
      password: 'password123',
      fullName: 'Admin Test User',
      tenantId: 1
    };
    
    const response = await makeRequest('/v1/auth/user/register', 'POST', userData);
    console.log(`Status: ${response.status}`);
    
    if (response.status === 201) {
      console.log('✅ User admin created successfully!');
      console.log(`User ID: ${response.data.data?.id}`);
      console.log(`Username: ${response.data.data?.username}`);
      console.log(`Email: ${response.data.data?.email}`);
    } else {
      console.log('❌ Failed to create user:', response.data);
      
      // Nếu user đã tồn tại, thử login
      if (response.status === 400 && response.data.message?.includes('đã tồn tại')) {
        console.log('💡 User đã tồn tại, thử login...');
        
        const loginData = {
          username: 'admin',
          password: 'password123'
        };
        
        const loginResponse = await makeRequest('/v1/auth/user/login', 'POST', loginData);
        console.log(`Login Status: ${loginResponse.status}`);
        
        if (loginResponse.status === 200) {
          console.log('✅ Login thành công với user hiện có!');
          console.log(`Token preview: ${loginResponse.data.data?.accessToken?.substring(0, 50)}...`);
        } else {
          console.log('❌ Login failed:', loginResponse.data);
        }
      }
    }
  } catch (error) {
    console.log('❌ Error:', error.message);
  }
  console.log('');

  // Test 2: Tạo một số nhân viên test
  console.log('👥 Test 2: Tạo nhân viên test');
  
  // Trước tiên login để lấy token
  const loginData = {
    username: 'admin',
    password: 'password123'
  };
  
  try {
    const loginResponse = await makeRequest('/v1/auth/user/login', 'POST', loginData);
    
    if (loginResponse.status === 200) {
      const token = loginResponse.data.data.accessToken;
      console.log('✅ Login thành công, bắt đầu tạo nhân viên test...');
      
      // Tạo một số nhân viên với các status khác nhau
      const employees = [
        {
          employeeName: 'Nguyễn Văn A',
          status: 'active',
          hireDate: '2024-01-15'
        },
        {
          employeeName: 'Trần Thị B',
          status: 'active',
          hireDate: '2024-02-20'
        },
        {
          employeeName: 'Lê Văn C',
          status: 'probation',
          hireDate: new Date().toISOString().split('T')[0] // Hôm nay
        },
        {
          employeeName: 'Phạm Thị D',
          status: 'inactive',
          hireDate: '2024-03-10'
        },
        {
          employeeName: 'Hoàng Văn E',
          status: 'probation',
          hireDate: new Date().toISOString().split('T')[0] // Hôm nay
        }
      ];
      
      for (let i = 0; i < employees.length; i++) {
        const emp = employees[i];
        try {
          const response = await makeRequest('/v1/api/hrm/employees', 'POST', emp, token);
          
          if (response.status === 201) {
            console.log(`✅ Created: ${response.data.data.employeeCode} - ${emp.employeeName} (${emp.status})`);
          } else {
            console.log(`❌ Failed to create ${emp.employeeName}:`, response.data);
          }
        } catch (error) {
          console.log(`❌ Error creating ${emp.employeeName}:`, error.message);
        }
      }
      
    } else {
      console.log('❌ Không thể login để tạo nhân viên test');
    }
  } catch (error) {
    console.log('❌ Error during employee creation:', error.message);
  }

  console.log('\n🏁 Hoàn thành tạo test data!');
  console.log('💡 Bây giờ bạn có thể chạy test-employee-overview-api.js');
}

// Chạy script
createTestUser().catch(console.error);
