import { ApiProperty } from '@nestjs/swagger';
import {
  IsNotEmpty,
  IsN<PERSON>ber,
  IsOptional,
  IsString,
  IsBoolean,
  IsDateString,
  IsEnum,
} from 'class-validator';
import { AttendanceStatus } from '../enum/attendance-status.enum';

/**
 * DTO for creating attendance record
 */
export class CreateAttendanceDto {
  /**
   * ID of the employee
   */
  @ApiProperty({
    description: 'ID of the employee',
    example: 1,
  })
  @IsNotEmpty()
  @IsNumber()
  employeeId: number;

  /**
   * Work date (YYYY-MM-DD format)
   */
  @ApiProperty({
    description: 'Work date in YYYY-MM-DD format',
    example: '2023-12-01',
  })
  @IsNotEmpty()
  @IsDateString()
  workDate: string;

  /**
   * Check-in time (timestamp in milliseconds)
   */
  @ApiProperty({
    description: 'Check-in time as timestamp in milliseconds',
    example: 1701417600000,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  checkInTime?: number;

  /**
   * Check-out time (timestamp in milliseconds)
   */
  @ApiProperty({
    description: 'Check-out time as timestamp in milliseconds',
    example: 1701446400000,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  checkOutTime?: number;

  /**
   * Total work hours for the day (in minutes)
   */
  @ApiProperty({
    description: 'Total work hours in minutes',
    example: 480,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  workHours?: number;

  /**
   * Break time duration (in minutes)
   */
  @ApiProperty({
    description: 'Break time duration in minutes',
    example: 60,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  breakTime?: number;

  /**
   * Overtime hours (in minutes)
   */
  @ApiProperty({
    description: 'Overtime hours in minutes',
    example: 120,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  overtimeHours?: number;

  /**
   * Attendance status
   */
  @ApiProperty({
    description: 'Attendance status',
    enum: AttendanceStatus,
    example: AttendanceStatus.PRESENT,
    required: false,
  })
  @IsOptional()
  @IsEnum(AttendanceStatus)
  status?: AttendanceStatus;

  /**
   * Check-in location
   */
  @ApiProperty({
    description: 'Check-in location (GPS coordinates or office location)',
    example: '21.0285,105.8542',
    required: false,
  })
  @IsOptional()
  @IsString()
  checkInLocation?: string;

  /**
   * Check-out location
   */
  @ApiProperty({
    description: 'Check-out location (GPS coordinates or office location)',
    example: '21.0285,105.8542',
    required: false,
  })
  @IsOptional()
  @IsString()
  checkOutLocation?: string;

  /**
   * Check-in IP address
   */
  @ApiProperty({
    description: 'Check-in IP address',
    example: '*************',
    required: false,
  })
  @IsOptional()
  @IsString()
  checkInIp?: string;

  /**
   * Check-out IP address
   */
  @ApiProperty({
    description: 'Check-out IP address',
    example: '*************',
    required: false,
  })
  @IsOptional()
  @IsString()
  checkOutIp?: string;

  /**
   * Notes or comments about the attendance
   */
  @ApiProperty({
    description: 'Notes or comments about the attendance',
    example: 'Worked from home due to weather conditions',
    required: false,
  })
  @IsOptional()
  @IsString()
  notes?: string;

  /**
   * Whether the attendance was manually created
   */
  @ApiProperty({
    description: 'Whether the attendance was manually created by admin',
    example: false,
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  isManual?: boolean;
}
