import { ApiProperty } from '@nestjs/swagger';
import { AttendanceStatus } from '../enum/attendance-status.enum';

/**
 * DTO for attendance response
 */
export class AttendanceResponseDto {
  /**
   * Unique identifier for the attendance record
   */
  @ApiProperty({
    description: 'Unique identifier for the attendance record',
    example: 1,
  })
  id: number;

  /**
   * ID of the employee
   */
  @ApiProperty({
    description: 'ID of the employee',
    example: 1,
  })
  employeeId: number;

  /**
   * Work date (YYYY-MM-DD format)
   */
  @ApiProperty({
    description: 'Work date in YYYY-MM-DD format',
    example: '2023-12-01',
  })
  workDate: string;

  /**
   * Check-in time (timestamp in milliseconds)
   */
  @ApiProperty({
    description: 'Check-in time as timestamp in milliseconds',
    example: 1701417600000,
    nullable: true,
  })
  checkInTime: number | null;

  /**
   * Check-out time (timestamp in milliseconds)
   */
  @ApiProperty({
    description: 'Check-out time as timestamp in milliseconds',
    example: 1701446400000,
    nullable: true,
  })
  checkOutTime: number | null;

  /**
   * Total work hours for the day (in minutes)
   */
  @ApiProperty({
    description: 'Total work hours in minutes',
    example: 480,
    nullable: true,
  })
  workHours: number | null;

  /**
   * Break time duration (in minutes)
   */
  @ApiProperty({
    description: 'Break time duration in minutes',
    example: 60,
    nullable: true,
  })
  breakTime: number | null;

  /**
   * Overtime hours (in minutes)
   */
  @ApiProperty({
    description: 'Overtime hours in minutes',
    example: 120,
    nullable: true,
  })
  overtimeHours: number | null;

  /**
   * Attendance status
   */
  @ApiProperty({
    description: 'Attendance status',
    enum: AttendanceStatus,
    example: AttendanceStatus.PRESENT,
  })
  status: AttendanceStatus;

  /**
   * Check-in location
   */
  @ApiProperty({
    description: 'Check-in location (GPS coordinates or office location)',
    example: '21.0285,105.8542',
    nullable: true,
  })
  checkInLocation: string | null;

  /**
   * Check-out location
   */
  @ApiProperty({
    description: 'Check-out location (GPS coordinates or office location)',
    example: '21.0285,105.8542',
    nullable: true,
  })
  checkOutLocation: string | null;

  /**
   * Check-in IP address
   */
  @ApiProperty({
    description: 'Check-in IP address',
    example: '*************',
    nullable: true,
  })
  checkInIp: string | null;

  /**
   * Check-out IP address
   */
  @ApiProperty({
    description: 'Check-out IP address',
    example: '*************',
    nullable: true,
  })
  checkOutIp: string | null;

  /**
   * Notes or comments about the attendance
   */
  @ApiProperty({
    description: 'Notes or comments about the attendance',
    example: 'Worked from home due to weather conditions',
    nullable: true,
  })
  notes: string | null;

  /**
   * Whether the attendance was manually adjusted
   */
  @ApiProperty({
    description: 'Whether the attendance was manually adjusted by admin',
    example: false,
  })
  isManual: boolean;

  /**
   * ID of the admin who made manual adjustments
   */
  @ApiProperty({
    description: 'ID of the admin who made manual adjustments',
    example: 2,
    nullable: true,
  })
  adjustedBy: number | null;

  /**
   * Timestamp when manual adjustment was made
   */
  @ApiProperty({
    description: 'Timestamp when manual adjustment was made',
    example: 1701450000000,
    nullable: true,
  })
  adjustedAt: number | null;

  /**
   * Creation timestamp (in milliseconds)
   */
  @ApiProperty({
    description: 'Creation timestamp in milliseconds',
    example: 1701417600000,
  })
  createdAt: number;

  /**
   * Last update timestamp (in milliseconds)
   */
  @ApiProperty({
    description: 'Last update timestamp in milliseconds',
    example: 1701417600000,
  })
  updatedAt: number;

  /**
   * ID of the user who created this record
   */
  @ApiProperty({
    description: 'ID of the user who created this record',
    example: 1,
  })
  createdBy: number;

  /**
   * ID of the user who last updated this record
   */
  @ApiProperty({
    description: 'ID of the user who last updated this record',
    example: 1,
  })
  updatedBy: number;

  /**
   * Tenant ID for multi-tenancy support
   */
  @ApiProperty({
    description: 'Tenant ID for multi-tenancy support',
    example: 1,
  })
  tenantId: number;
}
