/**
 * Enum cho các tag API trong Swagger
 */
export enum SWAGGER_API_TAG {
  AUTHENTICATION = 'Authentication',
  USER = 'User',
  COMPANY = 'Company',
  EMPLOYEE = 'Employee',
  PERMISSION = 'Permission',
  ROLE = 'Role',
  PRODUCT = 'Product',
  ORDER = 'Order',
  PAYMENT = 'Payment',
  NOTIFICATION = 'Notification',
  SETTING = 'Setting',
  DASHBOARD = 'Dashboard',
  REPORT = 'Report',
  UPLOAD = 'Upload',
  COMMON = 'Common',
  SYSTEM = 'System',
  USER_AUTHENTICATION = 'User Authentication',
  OKRS = 'OKRs',
  ADMIN = 'Admin',
  TODOLISTS = 'TodoLists',
  HRM = 'Human Resource Management',
  CALENDAR = 'Calendar',
  CHAT = 'Chat',
}

/**
 * Mô tả chi tiết cho các tag API
 */
export const SWAGGER_API_TAGS = {
  [SWAGGER_API_TAG.AUTHENTICATION]: {
    name: SWAGGER_API_TAG.AUTHENTICATION,
    description: 'Các API liên quan đến xác thực và phân quyền',
  },
  [SWAGGER_API_TAG.USER]: {
    name: SWAGGER_API_TAG.USER,
    description: 'Các API quản lý người dùng',
  },
  [SWAGGER_API_TAG.COMPANY]: {
    name: SWAGGER_API_TAG.COMPANY,
    description: 'Các API quản lý công ty',
  },
  [SWAGGER_API_TAG.EMPLOYEE]: {
    name: SWAGGER_API_TAG.EMPLOYEE,
    description: 'Các API quản lý nhân viên',
  },
  [SWAGGER_API_TAG.PERMISSION]: {
    name: SWAGGER_API_TAG.PERMISSION,
    description: 'Các API quản lý quyền',
  },
  [SWAGGER_API_TAG.ROLE]: {
    name: SWAGGER_API_TAG.ROLE,
    description: 'Các API quản lý vai trò',
  },
  [SWAGGER_API_TAG.PRODUCT]: {
    name: SWAGGER_API_TAG.PRODUCT,
    description: 'Các API quản lý sản phẩm',
  },
  [SWAGGER_API_TAG.ORDER]: {
    name: SWAGGER_API_TAG.ORDER,
    description: 'Các API quản lý đơn hàng',
  },
  [SWAGGER_API_TAG.PAYMENT]: {
    name: SWAGGER_API_TAG.PAYMENT,
    description: 'Các API quản lý thanh toán',
  },
  [SWAGGER_API_TAG.NOTIFICATION]: {
    name: SWAGGER_API_TAG.NOTIFICATION,
    description: 'Các API quản lý thông báo',
  },
  [SWAGGER_API_TAG.SETTING]: {
    name: SWAGGER_API_TAG.SETTING,
    description: 'Các API quản lý cài đặt',
  },
  [SWAGGER_API_TAG.DASHBOARD]: {
    name: SWAGGER_API_TAG.DASHBOARD,
    description: 'Các API cho bảng điều khiển',
  },
  [SWAGGER_API_TAG.REPORT]: {
    name: SWAGGER_API_TAG.REPORT,
    description: 'Các API báo cáo và thống kê',
  },
  [SWAGGER_API_TAG.UPLOAD]: {
    name: SWAGGER_API_TAG.UPLOAD,
    description: 'Các API tải lên tệp',
  },
  [SWAGGER_API_TAG.COMMON]: {
    name: SWAGGER_API_TAG.COMMON,
    description: 'Các API chung',
  },
  [SWAGGER_API_TAG.SYSTEM]: {
    name: SWAGGER_API_TAG.SYSTEM,
    description: 'Các API hệ thống',
  },
  [SWAGGER_API_TAG.USER_AUTHENTICATION]: {
    name: SWAGGER_API_TAG.USER_AUTHENTICATION,
    description: 'Các API xác thực người dùng',
  },
  [SWAGGER_API_TAG.OKRS]: {
    name: SWAGGER_API_TAG.OKRS,
    description: 'Các API quản lý OKRs',
  },
  [SWAGGER_API_TAG.ADMIN]: {
    name: SWAGGER_API_TAG.ADMIN,
    description: 'Các API quản lý dành cho Admin',
  },
  [SWAGGER_API_TAG.TODOLISTS]: {
    name: SWAGGER_API_TAG.TODOLISTS,
    description: 'Các API quản lý danh sách công việc',
  },
  [SWAGGER_API_TAG.HRM]: {
    name: SWAGGER_API_TAG.HRM,
    description: 'Các API quản lý nhân sự',
  },
  [SWAGGER_API_TAG.CALENDAR]: {
    name: SWAGGER_API_TAG.CALENDAR,
    description: 'Các API quản lý lịch và sự kiện',
  },
  [SWAGGER_API_TAG.CHAT]: {
    name: SWAGGER_API_TAG.CHAT,
    description: 'Các API quản lý chat và tích hợp Facebook Messenger',
  },
};
