import { Module, Global } from '@nestjs/common';
import { HttpModule } from '@nestjs/axios';
import { ConfigModule } from '@nestjs/config';
import { ZaloService } from './zalo.service';
import { ZaloOaService } from './zalo-oa.service';
import { ZaloZnsService } from './zalo-zns.service';
import { ZaloWebhookService } from './zalo-webhook.service';
import { ZaloAgentService } from './zalo-agent.service';

/**
 * Module tích hợp Zalo API
 */
@Global()
@Module({
  imports: [
    HttpModule.register({
      timeout: 30000, // 30 seconds
      maxRedirects: 5,
    }),
    ConfigModule,
  ],
  providers: [
    ZaloService,
    ZaloOaService,
    ZaloZnsService,
    ZaloWebhookService,
    ZaloAgentService,
  ],
  exports: [
    ZaloService,
    ZaloOaService,
    ZaloZnsService,
    ZaloWebhookService,
    ZaloAgentService,
  ],
})
export class ZaloModule {}
