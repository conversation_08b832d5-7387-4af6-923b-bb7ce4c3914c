const axios = require('axios');

// Test API objectives với parent title
async function testObjectivesAPI() {
  try {
    console.log('Testing Objectives API with parent title...');

    // Test 1: Gọi API GET objectives mà không có token để xem lỗi
    console.log('\n=== Test 1: Without token ===');
    try {
      const response = await axios.get('http://localhost:3001/v1/api/okrs/objectives?page=1&limit=10');
      console.log('Response status:', response.status);
    } catch (error) {
      console.log('Expected error (no token):', error.response?.status, error.response?.data?.message);
    }

    // Test 2: Kiểm tra Swagger docs để xem schema
    console.log('\n=== Test 2: Check Swagger docs ===');
    try {
      const swaggerResponse = await axios.get('http://localhost:3001/api/docs-json');
      const objectivesSchema = swaggerResponse.data.paths['/v1/api/okrs/objectives']?.get?.responses?.['200']?.content?.['application/json']?.schema;
      console.log('Objectives API schema found:', !!objectivesSchema);

      // Tìm ObjectiveResponseDto schema
      const components = swaggerResponse.data.components?.schemas;
      if (components && components.ObjectiveResponseDto) {
        console.log('✅ ObjectiveResponseDto schema found!');
        console.log('ObjectiveResponseDto properties:');
        Object.keys(components.ObjectiveResponseDto.properties || {}).forEach(prop => {
          const propInfo = components.ObjectiveResponseDto.properties[prop];
          console.log(`  - ${prop}: ${propInfo.type || 'object'} ${propInfo.nullable ? '(nullable)' : ''}`);
        });

        // Kiểm tra xem có parentTitle không
        if (components.ObjectiveResponseDto.properties.parentTitle) {
          console.log('\n✅ parentTitle field found in schema!');
          console.log('parentTitle config:', JSON.stringify(components.ObjectiveResponseDto.properties.parentTitle, null, 2));
        } else {
          console.log('\n❌ parentTitle field NOT found in schema');
        }
      } else {
        console.log('❌ ObjectiveResponseDto schema NOT found');
        console.log('Available schemas:', Object.keys(components || {}));
      }
    } catch (error) {
      console.log('Error checking swagger:', error.message);
    }

  } catch (error) {
    console.error('Error testing API:', error.response?.data || error.message);
  }
}

testObjectivesAPI();
