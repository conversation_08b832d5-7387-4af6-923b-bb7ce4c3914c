# Task ID: 19
# Title: Update System Module - Services and Controllers
# Status: done
# Dependencies: 17, 18
# Priority: medium
# Description: Update System services and controllers for tenantId handling
# Details:
Update service and controller layers:
- NotificationService: Add tenantId parameter to all methods
- SettingsService: Update to pass tenantId to repository
- AuditService: Add tenantId handling
- System Controllers: Add @CurrentTenant() where appropriate
- Update system reporting with tenantId filtering

# Test Strategy:
Integration and API tests for System module
