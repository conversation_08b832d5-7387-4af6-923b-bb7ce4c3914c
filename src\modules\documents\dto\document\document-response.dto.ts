import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { DocumentType, ProcessingStatus } from '../../enums';

/**
 * DTO response cho tài liệu
 */
export class DocumentResponseDto {
  /**
   * ID tài liệu
   */
  @ApiProperty({
    description: 'ID tài liệu',
    example: 1,
  })
  id: number;

  /**
   * Tiêu đề tài liệu
   */
  @ApiProperty({
    description: 'Tiêu đề tài liệu',
    example: 'Chính sách làm việc từ xa',
  })
  title: string;

  /**
   * Mô tả tài liệu
   */
  @ApiPropertyOptional({
    description: 'Mô tả tài liệu',
    example: 'Quy định về làm việc từ xa cho nhân viên công ty',
  })
  description: string | null;

  /**
   * Loại tài liệu
   */
  @ApiProperty({
    description: 'Loại tài liệu',
    enum: DocumentType,
    example: DocumentType.POLICY,
  })
  documentType: DocumentType;

  /**
   * Tên file gốc
   */
  @ApiProperty({
    description: 'Tên file gốc',
    example: 'chinh-sach-lam-viec-tu-xa.pdf',
  })
  fileName: string;

  /**
   * Kích thước file
   */
  @ApiProperty({
    description: 'Kích thước file (bytes)',
    example: 1048576,
  })
  fileSize: number;

  /**
   * Loại MIME
   */
  @ApiProperty({
    description: 'Loại MIME',
    example: 'application/pdf',
  })
  mimeType: string;

  /**
   * ID thư mục
   */
  @ApiPropertyOptional({
    description: 'ID thư mục chứa tài liệu',
    example: 1,
  })
  folderId: number | null;

  /**
   * Khóa S3
   */
  @ApiProperty({
    description: 'Khóa object S3',
    example: 'documents/tenant-1/2024/01/chinh-sach-lam-viec-tu-xa.pdf',
  })
  s3Key: string;

  /**
   * Bucket S3
   */
  @ApiPropertyOptional({
    description: 'Tên bucket S3',
    example: 'company-documents',
  })
  s3Bucket: string | null;

  /**
   * ID file OpenAI
   */
  @ApiPropertyOptional({
    description: 'ID file OpenAI',
    example: 'file-abc123',
  })
  openaiFileId: string | null;

  /**
   * ID Vector Store OpenAI
   */
  @ApiPropertyOptional({
    description: 'ID Vector Store OpenAI',
    example: 'vs-abc123',
  })
  openaiVectorStoreId: string | null;

  /**
   * Trạng thái xử lý
   */
  @ApiProperty({
    description: 'Trạng thái xử lý OpenAI',
    enum: ProcessingStatus,
    example: ProcessingStatus.COMPLETED,
  })
  processingStatus: ProcessingStatus;

  /**
   * Lỗi xử lý
   */
  @ApiPropertyOptional({
    description: 'Thông báo lỗi xử lý (nếu có)',
    example: null,
  })
  processingError: string | null;

  /**
   * Thời điểm xử lý cuối
   */
  @ApiPropertyOptional({
    description: 'Thời điểm xử lý cuối (timestamp)',
    example: 1640995200000,
  })
  lastProcessedAt: number | null;

  /**
   * Số lần thử lại
   */
  @ApiProperty({
    description: 'Số lần thử lại xử lý',
    example: 0,
  })
  retryCount: number;

  /**
   * Hash nội dung
   */
  @ApiPropertyOptional({
    description: 'Hash SHA-256 của nội dung',
    example: 'e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855',
  })
  contentHash: string | null;

  /**
   * Số trang
   */
  @ApiPropertyOptional({
    description: 'Số trang (cho PDF)',
    example: 10,
  })
  pageCount: number | null;

  /**
   * Số từ
   */
  @ApiPropertyOptional({
    description: 'Số từ trong tài liệu',
    example: 1500,
  })
  wordCount: number | null;

  /**
   * Ngôn ngữ
   */
  @ApiProperty({
    description: 'Ngôn ngữ tài liệu',
    example: 'vi',
  })
  language: string;

  /**
   * Trạng thái hoạt động
   */
  @ApiProperty({
    description: 'Trạng thái hoạt động',
    example: true,
  })
  isActive: boolean;

  /**
   * Tài liệu công khai
   */
  @ApiProperty({
    description: 'Tài liệu có công khai không',
    example: false,
  })
  isPublic: boolean;

  /**
   * Phiên bản
   */
  @ApiProperty({
    description: 'Phiên bản tài liệu',
    example: 1,
  })
  version: number;

  /**
   * Thẻ tags
   */
  @ApiPropertyOptional({
    description: 'Danh sách thẻ tags',
    example: ['hr', 'policy', 'remote-work'],
  })
  tags: string[] | null;

  /**
   * Metadata
   */
  @ApiPropertyOptional({
    description: 'Metadata bổ sung',
    example: { author: 'HR Department', version: '1.0' },
  })
  metadata: Record<string, any> | null;

  /**
   * Thời điểm tạo
   */
  @ApiProperty({
    description: 'Thời điểm tạo (timestamp)',
    example: 1640995200000,
  })
  createdAt: number;

  /**
   * Thời điểm cập nhật cuối
   */
  @ApiPropertyOptional({
    description: 'Thời điểm cập nhật cuối (timestamp)',
    example: 1640995200000,
  })
  updatedAt: number | null;

  /**
   * ID người tạo
   */
  @ApiProperty({
    description: 'ID người tạo tài liệu',
    example: 1,
  })
  createdBy: number;

  /**
   * ID người cập nhật cuối
   */
  @ApiPropertyOptional({
    description: 'ID người cập nhật cuối',
    example: 1,
  })
  updatedBy: number | null;

  /**
   * ID tenant
   */
  @ApiProperty({
    description: 'ID tenant',
    example: 1,
  })
  tenantId: number;

  // Computed properties

  /**
   * Kích thước file (MB)
   */
  @ApiProperty({
    description: 'Kích thước file (MB)',
    example: 1.0,
  })
  fileSizeMB: number;

  /**
   * Đã xử lý thành công
   */
  @ApiProperty({
    description: 'Tài liệu đã được xử lý thành công',
    example: true,
  })
  isProcessed: boolean;

  /**
   * Xử lý thất bại
   */
  @ApiProperty({
    description: 'Xử lý tài liệu thất bại',
    example: false,
  })
  isProcessingFailed: boolean;

  /**
   * Có thể thử lại
   */
  @ApiProperty({
    description: 'Có thể thử lại xử lý',
    example: false,
  })
  canRetry: boolean;

  /**
   * Phần mở rộng file
   */
  @ApiProperty({
    description: 'Phần mở rộng file',
    example: 'pdf',
  })
  fileExtension: string;

  /**
   * Là file PDF
   */
  @ApiProperty({
    description: 'Có phải file PDF không',
    example: true,
  })
  isPDF: boolean;

  /**
   * Là Word document
   */
  @ApiProperty({
    description: 'Có phải Word document không',
    example: false,
  })
  isWordDocument: boolean;

  /**
   * Là text file
   */
  @ApiProperty({
    description: 'Có phải text file không',
    example: false,
  })
  isTextFile: boolean;
}

/**
 * DTO response cho tài liệu với URL download
 */
export class DocumentWithDownloadUrlResponseDto extends DocumentResponseDto {
  /**
   * URL tạm thời để download
   */
  @ApiProperty({
    description: 'URL tạm thời để download file (có thời hạn)',
    example: 'https://s3.amazonaws.com/bucket/file.pdf?signature=...',
  })
  downloadUrl: string;

  /**
   * Thời gian hết hạn URL (timestamp)
   */
  @ApiProperty({
    description: 'Thời gian hết hạn URL download (timestamp)',
    example: 1640995200000,
  })
  downloadUrlExpiresAt: number;
}
