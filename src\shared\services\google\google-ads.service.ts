import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { GoogleAdsApi, Customer, enums } from 'google-ads-api';
import {
  GoogleAdsConfig,
  GoogleAdsCredentials,
  CampaignSearchResult,
  AdGroupSearchResult,
  KeywordSearchResult,
  PerformanceReport,
} from './interfaces/google-ads.interface';

@Injectable()
export class GoogleAdsService {
  private client: GoogleAdsApi;
  private readonly logger = new Logger(GoogleAdsService.name);

  constructor(private readonly configService: ConfigService) {
    try {
      const clientId = this.configService.get<string>('GOOGLE_ADS_CLIENT_ID');
      const clientSecret = this.configService.get<string>(
        'GOOGLE_ADS_CLIENT_SECRET',
      );
      const developerToken = this.configService.get<string>(
        'GOOGLE_ADS_DEVELOPER_TOKEN',
      );

      if (!clientId || !clientSecret || !developerToken) {
        this.logger.warn('Missing Google Ads API configuration');
        return;
      }

      // Khởi tạo Google Ads API client
      this.client = new GoogleAdsApi({
        client_id: clientId,
        client_secret: clientSecret,
        developer_token: developerToken,
      });

      this.logger.log('Google Ads API client initialized');
    } catch (error) {
      this.logger.error(
        `Failed to initialize Google Ads API client: ${error.message}`,
      );
    }
  }

  /**
   * Lấy customer từ customer ID
   * @param customerId ID của customer
   * @param refreshToken Refresh token (nếu không có sẽ sử dụng từ cấu hình)
   * @returns Customer instance
   */
  getCustomer(customerId: string, refreshToken?: string): Customer {
    if (!this.client) {
      throw new Error('Google Ads API client not initialized');
    }

    const configRefreshToken = this.configService.get<string>(
      'GOOGLE_ADS_REFRESH_TOKEN',
    );
    const token = refreshToken || configRefreshToken;

    if (!token) {
      throw new Error('Refresh token is required');
    }

    return this.client.Customer({
      customer_id: customerId,
      refresh_token: token,
    });
  }

  /**
   * Lấy danh sách chiến dịch
   * @param customerId ID của customer
   * @param refreshToken Refresh token (tùy chọn)
   * @returns Danh sách chiến dịch
   */
  async listCampaigns(
    customerId: string,
    refreshToken?: string,
  ): Promise<CampaignSearchResult[]> {
    try {
      const customer = this.getCustomer(customerId, refreshToken);

      const query = `
        SELECT
          campaign.id,
          campaign.name,
          campaign.status,
          campaign_budget.amount_micros,
          campaign.advertising_channel_type,
          campaign.start_date,
          campaign.end_date
        FROM campaign
        ORDER BY campaign.name
      `;

      const response = await customer.query(query);

      return response.map((row) => ({
        id: String(row.campaign?.id || ''),
        name: String(row.campaign?.name || ''),
        status: String(row.campaign?.status || ''),
        budget: Number(row.campaign_budget?.amount_micros || 0),
        type: String(row.campaign?.advertising_channel_type || ''),
        startDate: String(row.campaign?.start_date || ''),
        endDate: String(row.campaign?.end_date || ''),
      }));
    } catch (error) {
      this.logger.error(`Failed to list campaigns: ${error.message}`);
      throw error;
    }
  }

  /**
   * Lấy thông tin chi tiết của chiến dịch
   * @param customerId ID của customer
   * @param campaignId ID của chiến dịch
   * @param refreshToken Refresh token (tùy chọn)
   * @returns Thông tin chi tiết của chiến dịch
   */
  async getCampaign(
    customerId: string,
    campaignId: string,
    refreshToken?: string,
  ): Promise<CampaignSearchResult> {
    try {
      const customer = this.getCustomer(customerId, refreshToken);

      const query = `
        SELECT
          campaign.id,
          campaign.name,
          campaign.status,
          campaign_budget.amount_micros,
          campaign.advertising_channel_type,
          campaign.start_date,
          campaign.end_date
        FROM campaign
        WHERE campaign.id = ${campaignId}
      `;

      const response = await customer.query(query);

      if (response.length === 0) {
        throw new Error(`Campaign with ID ${campaignId} not found`);
      }

      const row = response[0];
      return {
        id: String(row.campaign?.id || ''),
        name: String(row.campaign?.name || ''),
        status: String(row.campaign?.status || ''),
        budget: Number(row.campaign_budget?.amount_micros || 0),
        type: String(row.campaign?.advertising_channel_type || ''),
        startDate: String(row.campaign?.start_date || ''),
        endDate: String(row.campaign?.end_date || ''),
      };
    } catch (error) {
      this.logger.error(`Failed to get campaign: ${error.message}`);
      throw error;
    }
  }

  /**
   * Tạo chiến dịch mới
   * @param customerId ID của customer
   * @param campaignData Dữ liệu chiến dịch
   * @param refreshToken Refresh token (tùy chọn)
   * @returns ID của chiến dịch mới
   */
  async createCampaign(
    customerId: string,
    campaignData: {
      name: string;
      budgetAmount: number; // Micro amount (1000000 = 1 USD)
      type?: string; // SEARCH, DISPLAY, VIDEO, ...
      startDate?: string; // YYYYMMDD format
      endDate?: string; // YYYYMMDD format
    },
    refreshToken?: string,
  ): Promise<string> {
    try {
      const customer = this.getCustomer(customerId, refreshToken);

      // Tạo ngân sách trước
      const budgetResponse = await customer.campaignBudgets.create([
        {
          name: `Budget for ${campaignData.name}`,
          amount_micros: campaignData.budgetAmount,
          delivery_method: enums.BudgetDeliveryMethod.STANDARD,
        },
      ]);
      const budgetResourceName = budgetResponse.results[0].resource_name;

      // Tạo chiến dịch
      const campaignResponse = await customer.campaigns.create([
        {
          name: campaignData.name,
          campaign_budget: budgetResourceName,
          status: enums.CampaignStatus.PAUSED, // Bắt đầu ở trạng thái tạm dừng
          advertising_channel_type: campaignData.type
            ? (campaignData.type as unknown as enums.AdvertisingChannelType)
            : enums.AdvertisingChannelType.SEARCH,
          start_date:
            campaignData.startDate || this.formatDateForGoogleAds(new Date()),
          end_date: campaignData.endDate || undefined,
        },
      ]);
      const resourceName = campaignResponse.results[0]?.resource_name || '';
      const campaignId = resourceName.split('/').pop() || '';

      return campaignId;
    } catch (error) {
      this.logger.error(`Failed to create campaign: ${error.message}`);
      throw error;
    }
  }

  /**
   * Cập nhật chiến dịch
   * @param customerId ID của customer
   * @param campaignId ID của chiến dịch
   * @param campaignData Dữ liệu cập nhật
   * @param refreshToken Refresh token (tùy chọn)
   * @returns true nếu cập nhật thành công
   */
  async updateCampaign(
    customerId: string,
    campaignId: string,
    campaignData: {
      name?: string;
      status?: string; // ENABLED, PAUSED, REMOVED
      budgetAmount?: number; // Micro amount
      startDate?: string; // YYYYMMDD format
      endDate?: string; // YYYYMMDD format
    },
    refreshToken?: string,
  ): Promise<boolean> {
    try {
      const customer = this.getCustomer(customerId, refreshToken);

      // Lấy thông tin chiến dịch hiện tại (không cần thiết cho việc cập nhật)
      // await this.getCampaign(customerId, campaignId, refreshToken);

      // Chuẩn bị dữ liệu cập nhật
      const updateData: any = {
        resource_name: `customers/${customerId}/campaigns/${campaignId}`,
      };

      if (campaignData.name) {
        updateData.name = campaignData.name;
      }

      if (campaignData.status) {
        updateData.status = campaignData.status;
      }

      if (campaignData.startDate) {
        updateData.start_date = campaignData.startDate;
      }

      if (campaignData.endDate) {
        updateData.end_date = campaignData.endDate;
      }

      // Cập nhật chiến dịch
      await customer.campaigns.update([updateData]);

      // Cập nhật ngân sách nếu cần
      if (campaignData.budgetAmount) {
        // Lấy resource name của ngân sách
        const query = `
          SELECT campaign_budget.resource_name
          FROM campaign
          WHERE campaign.id = ${campaignId}
        `;
        const response = await customer.query(query);

        if (response.length > 0 && response[0].campaign_budget?.resource_name) {
          const budgetResourceName = response[0].campaign_budget.resource_name;

          await customer.campaignBudgets.update([
            {
              resource_name: budgetResourceName,
              amount_micros: campaignData.budgetAmount,
            },
          ]);
        }
      }

      return true;
    } catch (error) {
      this.logger.error(`Failed to update campaign: ${error.message}`);
      throw error;
    }
  }

  /**
   * Lấy danh sách nhóm quảng cáo trong chiến dịch
   * @param customerId ID của customer
   * @param campaignId ID của chiến dịch
   * @param refreshToken Refresh token (tùy chọn)
   * @returns Danh sách nhóm quảng cáo
   */
  async listAdGroups(
    customerId: string,
    campaignId: string,
    refreshToken?: string,
  ): Promise<AdGroupSearchResult[]> {
    try {
      const customer = this.getCustomer(customerId, refreshToken);

      const query = `
        SELECT
          ad_group.id,
          ad_group.name,
          ad_group.campaign,
          ad_group.status,
          ad_group.type,
          ad_group.cpc_bid_micros
        FROM ad_group
        WHERE ad_group.campaign = 'customers/${customerId}/campaigns/${campaignId}'
        ORDER BY ad_group.name
      `;

      const response = await customer.query(query);

      return response.map((row) => ({
        id: String(row.ad_group?.id || ''),
        name: String(row.ad_group?.name || ''),
        campaignId: campaignId,
        status: String(row.ad_group?.status || ''),
        type: String(row.ad_group?.type || ''),
        cpcBidMicros: Number(row.ad_group?.cpc_bid_micros || 0),
      }));
    } catch (error) {
      this.logger.error(`Failed to list ad groups: ${error.message}`);
      throw error;
    }
  }

  /**
   * Tạo nhóm quảng cáo mới
   * @param customerId ID của customer
   * @param adGroupData Dữ liệu nhóm quảng cáo
   * @param refreshToken Refresh token (tùy chọn)
   * @returns ID của nhóm quảng cáo mới
   */
  async createAdGroup(
    customerId: string,
    adGroupData: {
      name: string;
      campaignId: string;
      cpcBidMicros?: number; // Micro amount
    },
    refreshToken?: string,
  ): Promise<string> {
    try {
      const customer = this.getCustomer(customerId, refreshToken);

      const adGroupResponse = await customer.adGroups.create([
        {
          name: adGroupData.name,
          campaign: `customers/${customerId}/campaigns/${adGroupData.campaignId}`,
          status: enums.AdGroupStatus.ENABLED,
          type: enums.AdGroupType.SEARCH_STANDARD,
          cpc_bid_micros: adGroupData.cpcBidMicros,
        },
      ]);
      const resourceName = adGroupResponse.results[0]?.resource_name || '';
      const adGroupId = resourceName.split('/').pop() || '';

      return adGroupId;
    } catch (error) {
      this.logger.error(`Failed to create ad group: ${error.message}`);
      throw error;
    }
  }

  /**
   * Lấy danh sách từ khóa trong nhóm quảng cáo
   * @param customerId ID của customer
   * @param adGroupId ID của nhóm quảng cáo
   * @param refreshToken Refresh token (tùy chọn)
   * @returns Danh sách từ khóa
   */
  async listKeywords(
    customerId: string,
    adGroupId: string,
    refreshToken?: string,
  ): Promise<KeywordSearchResult[]> {
    try {
      const customer = this.getCustomer(customerId, refreshToken);

      const query = `
        SELECT
          ad_group_criterion.criterion_id,
          ad_group_criterion.keyword.text,
          ad_group_criterion.ad_group,
          ad_group_criterion.keyword.match_type,
          ad_group_criterion.status,
          ad_group_criterion.keyword.cpc_bid_micros
        FROM ad_group_criterion
        WHERE ad_group_criterion.ad_group = 'customers/${customerId}/adGroups/${adGroupId}'
        AND ad_group_criterion.type = 'KEYWORD'
        ORDER BY ad_group_criterion.keyword.text
      `;

      const response = await customer.query(query);

      return response.map((row) => {
        const criterion = row.ad_group_criterion || {};
        const keyword = criterion.keyword || {};

        return {
          id: String(criterion.criterion_id || ''),
          text: String(keyword.text || ''),
          adGroupId: adGroupId,
          matchType: String(keyword.match_type || ''),
          status: String(criterion.status || ''),
          cpcBidMicros: Number((keyword as any).cpc_bid_micros || 0),
        };
      });
    } catch (error) {
      this.logger.error(`Failed to list keywords: ${error.message}`);
      throw error;
    }
  }

  /**
   * Thêm từ khóa vào nhóm quảng cáo
   * @param customerId ID của customer
   * @param keywordData Dữ liệu từ khóa
   * @param refreshToken Refresh token (tùy chọn)
   * @returns ID của từ khóa mới
   */
  async addKeyword(
    customerId: string,
    keywordData: {
      text: string;
      adGroupId: string;
      matchType?: string; // EXACT, PHRASE, BROAD
      cpcBidMicros?: number; // Micro amount
    },
    refreshToken?: string,
  ): Promise<string> {
    try {
      const customer = this.getCustomer(customerId, refreshToken);

      const keywordResponse = await customer.adGroupCriteria.create([
        {
          ad_group: `customers/${customerId}/adGroups/${keywordData.adGroupId}`,
          status: enums.AdGroupCriterionStatus.ENABLED,
          keyword: {
            text: keywordData.text,
            match_type: keywordData.matchType
              ? (keywordData.matchType as unknown as enums.KeywordMatchType)
              : enums.KeywordMatchType.EXACT,
          },
          cpc_bid_micros: keywordData.cpcBidMicros,
        },
      ]);
      const resourceName = keywordResponse.results[0]?.resource_name || '';
      const keywordId = resourceName.split('/').pop() || '';

      return keywordId;
    } catch (error) {
      this.logger.error(`Failed to add keyword: ${error.message}`);
      throw error;
    }
  }

  /**
   * Lấy báo cáo hiệu suất của chiến dịch
   * @param customerId ID của customer
   * @param campaignId ID của chiến dịch
   * @param dateRange Khoảng thời gian (YYYYMMDD-YYYYMMDD)
   * @param refreshToken Refresh token (tùy chọn)
   * @returns Báo cáo hiệu suất
   */
  async getCampaignPerformance(
    customerId: string,
    campaignId: string,
    dateRange: { startDate: string; endDate: string },
    refreshToken?: string,
  ): Promise<PerformanceReport[]> {
    try {
      const customer = this.getCustomer(customerId, refreshToken);

      const query = `
        SELECT
          segments.date,
          metrics.impressions,
          metrics.clicks,
          metrics.cost_micros,
          metrics.ctr,
          metrics.average_cpc,
          metrics.conversions,
          metrics.conversions_value
        FROM campaign
        WHERE campaign.id = ${campaignId}
        AND segments.date BETWEEN '${dateRange.startDate}' AND '${dateRange.endDate}'
        ORDER BY segments.date
      `;

      const response = await customer.query(query);

      return response.map((row) => ({
        date: String(row.segments?.date || ''),
        impressions: Number(row.metrics?.impressions || 0),
        clicks: Number(row.metrics?.clicks || 0),
        cost: Number(row.metrics?.cost_micros || 0),
        ctr: Number(row.metrics?.ctr || 0),
        averageCpc: Number(row.metrics?.average_cpc || 0),
        conversions: Number(row.metrics?.conversions || 0),
        conversionValue: Number(row.metrics?.conversions_value || 0),
      }));
    } catch (error) {
      this.logger.error(`Failed to get campaign performance: ${error.message}`);
      throw error;
    }
  }

  /**
   * Định dạng ngày cho Google Ads (YYYYMMDD)
   * @param date Đối tượng Date
   * @returns Chuỗi ngày định dạng YYYYMMDD
   */
  private formatDateForGoogleAds(date: Date): string {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}${month}${day}`;
  }
}
