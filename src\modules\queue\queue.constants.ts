/**
 * T<PERSON><PERSON> các queue trong hệ thống
 */
export enum QueueName {
  EMAIL = 'email',
  SMS = 'sms',
  NOTIFICATION = 'notification',
  DATA_PROCESS = 'data-process',
  TODO = 'todo',
  PROJECT = 'project',
  REPORT = 'report',
  EXPORT = 'export',
  IMPORT = 'import',
  SYNC = 'sync',
}

/**
 * Cấu hình mặc định cho các job
 */
export const DEFAULT_JOB_OPTIONS = {
  attempts: 3, // Số lần thử lại nếu thất bại
  backoff: {
    type: 'exponential', // Kiểu backoff
    delay: 1000, // Delay ban đầu 1s
  },
  removeOnComplete: true, // Xóa job khi hoàn thành
  removeOnFail: false, // Giữ lại job khi thất bại để debug
  timeout: 30000, // Timeout 30s
};

/**
 * Các event của queue
 */
export enum QueueEvent {
  COMPLETED = 'completed',
  FAILED = 'failed',
  STALLED = 'stalled',
  PROGRESS = 'progress',
  ACTIVE = 'active',
  WAITING = 'waiting',
  DELAYED = 'delayed',
  PAUSED = 'paused',
  RESUMED = 'resumed',
  CLEANED = 'cleaned',
  DRAINED = 'drained',
  ERROR = 'error',
}
