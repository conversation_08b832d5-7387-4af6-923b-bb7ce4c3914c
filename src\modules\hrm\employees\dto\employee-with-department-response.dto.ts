import { ApiProperty } from '@nestjs/swagger';
import { EmployeeStatus } from '../enum/employee-status.enum';
import { Gender } from '../enum/gender.enum';
import { EmploymentType } from '../enum/employment-type.enum';

/**
 * DTO cho thông tin phòng ban trong response
 */
export class DepartmentInfoDto {
  /**
   * ID phòng ban
   * @example 1
   */
  @ApiProperty({ description: 'ID phòng ban', example: 1 })
  id: number;

  /**
   * Tên phòng ban
   * @example "Phòng Kỹ thuật"
   */
  @ApiProperty({ description: 'Tên phòng ban', example: 'Phòng Kỹ thuật' })
  name: string;

  /**
   * <PERSON>ô tả phòng ban
   * @example "Phòng ban phụ trách các vấn đề kỹ thuật của công ty"
   */
  @ApiProperty({
    description: 'Mô tả phòng ban',
    example: 'Phòng ban phụ trách các vấn đề kỹ thuật của công ty',
    nullable: true,
  })
  description: string | null;

  /**
   * ID người quản lý phòng ban
   * @example 1
   */
  @ApiProperty({
    description: 'ID người quản lý phòng ban',
    example: 1,
    nullable: true,
  })
  managerId: number | null;

  /**
   * ID phòng ban cấp trên
   * @example 1
   */
  @ApiProperty({
    description: 'ID phòng ban cấp trên',
    example: 1,
    nullable: true,
  })
  parentId: number | null;
}

/**
 * DTO for employee response with department information
 */
export class EmployeeWithDepartmentResponseDto {
  /**
   * Employee ID
   * @example 1
   */
  @ApiProperty({ description: 'Employee ID', example: 1 })
  id: number;

  /**
   * Employee code
   * @example "EMP001"
   */
  @ApiProperty({ description: 'Employee code', example: 'EMP001' })
  employeeCode: string;

  /**
   * Employee name
   * @example "Nguyễn Văn A"
   */
  @ApiProperty({ description: 'Employee name', example: 'Nguyễn Văn A' })
  employeeName: string;

  /**
   * Date of birth
   * @example "1990-01-15"
   */
  @ApiProperty({
    description: 'Date of birth',
    example: '1990-01-15',
    nullable: true,
  })
  dateOfBirth: Date | null;

  /**
   * Gender
   * @example "male"
   */
  @ApiProperty({
    description: 'Gender',
    enum: Gender,
    example: Gender.MALE,
    nullable: true,
  })
  gender: Gender | null;

  /**
   * Department ID
   * @example 1
   */
  @ApiProperty({
    description: 'Department ID',
    example: 1,
    nullable: true,
  })
  departmentId: number | null;

  /**
   * Department information
   */
  @ApiProperty({
    description: 'Thông tin phòng ban',
    type: DepartmentInfoDto,
    nullable: true,
  })
  department: DepartmentInfoDto | null;

  /**
   * Job title
   * @example "Software Engineer"
   */
  @ApiProperty({
    description: 'Job title',
    example: 'Software Engineer',
    nullable: true,
  })
  jobTitle: string | null;

  /**
   * Job level
   * @example "Senior"
   */
  @ApiProperty({
    description: 'Job level',
    example: 'Senior',
    nullable: true,
  })
  jobLevel: string | null;

  /**
   * Manager ID
   * @example 2
   */
  @ApiProperty({
    description: 'Manager ID',
    example: 2,
    nullable: true,
  })
  managerId: number | null;

  /**
   * Employment type
   * @example "full_time"
   */
  @ApiProperty({
    description: 'Employment type',
    enum: EmploymentType,
    example: EmploymentType.FULL_TIME,
    nullable: true,
  })
  employmentType: EmploymentType | null;

  /**
   * Employee status
   * @example "active"
   */
  @ApiProperty({
    description: 'Employee status',
    enum: EmployeeStatus,
    example: EmployeeStatus.ACTIVE,
  })
  status: EmployeeStatus;

  /**
   * Hire date
   * @example "2023-01-15"
   */
  @ApiProperty({
    description: 'Hire date',
    example: '2023-01-15',
    nullable: true,
  })
  hireDate: Date | null;

  /**
   * Email
   * @example "<EMAIL>"
   */
  @ApiProperty({
    description: 'Email',
    example: '<EMAIL>',
    nullable: true,
  })
  email: string | null;

  /**
   * Phone number
   * @example "+84901234567"
   */
  @ApiProperty({
    description: 'Phone number',
    example: '+84901234567',
    nullable: true,
  })
  phoneNumber: string | null;

  /**
   * Address
   * @example "123 Main St, Ho Chi Minh City"
   */
  @ApiProperty({
    description: 'Address',
    example: '123 Main St, Ho Chi Minh City',
    nullable: true,
  })
  address: string | null;

  /**
   * Created at timestamp
   * @example 1672531200000
   */
  @ApiProperty({
    description: 'Created at timestamp',
    example: 1672531200000,
    nullable: true,
  })
  createdAt: number | null;

  /**
   * Updated at timestamp
   * @example 1672531200000
   */
  @ApiProperty({
    description: 'Updated at timestamp',
    example: 1672531200000,
    nullable: true,
  })
  updatedAt: number | null;
}
