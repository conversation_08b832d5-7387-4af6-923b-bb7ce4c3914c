import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Between } from 'typeorm';
import { CalendarRepository } from '../repositories/calendar.repository';
import { ReferenceType } from '../enum/reference-type.enum';
import { CalendarEventDto } from '../dto/calendar-event.dto';
import { Calendar } from '../entities/calendar.entity';

/**
 * Service tích hợp Calendar với các module khác
 */
@Injectable()
export class CalendarIntegrationService {
  private readonly logger = new Logger(CalendarIntegrationService.name);

  constructor(
    private readonly calendarRepository: CalendarRepository,
    @InjectRepository(Calendar)
    private readonly repository: Repository<Calendar>,
  ) {}

  /**
   * Tạo hoặc cập nhật sự kiện lịch từ Todo
   * @param todoId ID của Todo
   * @param title Tiêu đề
   * @param description Mô tả
   * @param dueDate <PERSON><PERSON><PERSON> hết hạn
   * @param status Trạng thái
   * @param priority Độ ưu tiên
   * @param userId ID của người dùng
   * @returns Sự kiện lịch đã tạo hoặc cập nhật
   */
  async syncTodoToCalendar(
    tenantId: number,
    todoId: number,
    title: string,
    description: string | null,
    dueDate: number,
    status: string,
    priority: string,
    userId: number,
  ): Promise<Calendar> {
    try {
      // Tìm sự kiện lịch hiện có cho Todo này
      let calendarEvent = await this.calendarRepository.findByReference(
        tenantId,
        ReferenceType.TODO,
        todoId,
      );

      const now = Date.now();

      // Nếu không tìm thấy, tạo mới
      if (!calendarEvent) {
        calendarEvent = await this.calendarRepository.create(tenantId, {
          startTime: dueDate,
          endTime: dueDate + 3600000, // Mặc định kéo dài 1 giờ
          description,
          info: {
            title,
            status,
            priority,
            color: this.getTodoColor(priority),
            icon: 'task',
          },
          referenceType: ReferenceType.TODO,
          referenceId: todoId,
          createdAt: now,
          updatedAt: now,
          updatedBy: userId,
        });
      } else {
        // Nếu tìm thấy, cập nhật
        calendarEvent = await this.calendarRepository.update(
          tenantId,
          calendarEvent.id,
          {
            startTime: dueDate,
            endTime: dueDate + 3600000, // Mặc định kéo dài 1 giờ
            description,
            info: {
              ...calendarEvent.info,
              title,
              status,
              priority,
              color: this.getTodoColor(priority),
            },
            updatedAt: now,
            updatedBy: userId,
          },
        );
      }

      return calendarEvent;
    } catch (error) {
      this.logger.error(
        `Error syncing todo to calendar: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Xóa sự kiện lịch khi Todo bị xóa
   * @param todoId ID của Todo
   * @returns true nếu xóa thành công, false nếu không
   */
  async removeTodoFromCalendar(
    tenantId: number,
    todoId: number,
  ): Promise<boolean> {
    try {
      return await this.calendarRepository.deleteByReference(
        tenantId,
        ReferenceType.TODO,
        todoId,
      );
    } catch (error) {
      this.logger.error(
        `Error removing todo from calendar: ${error.message}`,
        error.stack,
      );
      return false;
    }
  }

  /**
   * Tạo hoặc cập nhật sự kiện lịch từ đơn xin nghỉ
   * @param leaveRequestId ID của đơn xin nghỉ
   * @param title Tiêu đề
   * @param reason Lý do
   * @param startDate Ngày bắt đầu
   * @param endDate Ngày kết thúc
   * @param status Trạng thái
   * @param userId ID của người dùng
   * @returns Sự kiện lịch đã tạo hoặc cập nhật
   */
  async syncLeaveRequestToCalendar(
    tenantId: number,
    leaveRequestId: number,
    title: string,
    reason: string | null,
    startDate: number,
    endDate: number,
    status: string,
    userId: number,
  ): Promise<Calendar> {
    try {
      // Tìm sự kiện lịch hiện có cho đơn xin nghỉ này
      let calendarEvent = await this.calendarRepository.findByReference(
        tenantId,
        ReferenceType.LEAVE_REQUEST,
        leaveRequestId,
      );

      const now = Date.now();

      // Nếu không tìm thấy, tạo mới
      if (!calendarEvent) {
        calendarEvent = await this.calendarRepository.create(tenantId, {
          startTime: startDate,
          endTime: endDate,
          description: reason,
          info: {
            title,
            status,
            color: this.getLeaveRequestColor(status),
            icon: 'event_busy',
          },
          referenceType: ReferenceType.LEAVE_REQUEST,
          referenceId: leaveRequestId,
          createdAt: now,
          updatedAt: now,
          updatedBy: userId,
        });
      } else {
        // Nếu tìm thấy, cập nhật
        calendarEvent = await this.calendarRepository.update(
          tenantId,
          calendarEvent.id,
          {
            startTime: startDate,
            endTime: endDate,
            description: reason,
            info: {
              ...calendarEvent.info,
              title,
              status,
              color: this.getLeaveRequestColor(status),
            },
            updatedAt: now,
            updatedBy: userId,
          },
        );
      }

      return calendarEvent;
    } catch (error) {
      this.logger.error(
        `Error syncing leave request to calendar: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Xóa sự kiện lịch khi đơn xin nghỉ bị xóa
   * @param leaveRequestId ID của đơn xin nghỉ
   * @returns true nếu xóa thành công, false nếu không
   */
  async removeLeaveRequestFromCalendar(
    tenantId: number,
    leaveRequestId: number,
  ): Promise<boolean> {
    try {
      return await this.calendarRepository.deleteByReference(
        tenantId,
        ReferenceType.LEAVE_REQUEST,
        leaveRequestId,
      );
    } catch (error) {
      this.logger.error(
        `Error removing leave request from calendar: ${error.message}`,
        error.stack,
      );
      return false;
    }
  }

  /**
   * Lấy màu cho Todo dựa trên độ ưu tiên
   * @param priority Độ ưu tiên
   * @returns Mã màu
   */
  private getTodoColor(priority: string): string {
    switch (priority?.toLowerCase()) {
      case 'high':
        return '#FF5733';
      case 'medium':
        return '#FFC300';
      case 'low':
        return '#33FF57';
      default:
        return '#3788d8';
    }
  }

  /**
   * Lấy màu cho đơn xin nghỉ dựa trên trạng thái
   * @param status Trạng thái
   * @returns Mã màu
   */
  private getLeaveRequestColor(status: string): string {
    switch (status?.toLowerCase()) {
      case 'approved':
        return '#33FF57';
      case 'pending':
        return '#FFC300';
      case 'rejected':
        return '#FF5733';
      default:
        return '#3788d8';
    }
  }
}
