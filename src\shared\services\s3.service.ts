import { Injectable, Logger } from '@nestjs/common';
import { AppException, ErrorCode } from '@common/exceptions/app.exception';
import { FileSizeEnum, getTimeInterval, TimeIntervalEnum } from '../utils';
import { MediaType, MediaTypeUtil } from '@utils/file';
import {
  CopyObjectCommand,
  CopyObjectCommandOutput,
  DeleteObjectCommand,
  DeleteObjectCommandOutput,
  DeleteObjectsCommand,
  DeleteObjectsCommandOutput,
  GetObjectCommand,
  ObjectIdentifier,
  PutObjectCommand,
  S3Client,
} from '@aws-sdk/client-s3';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';
import { ConfigService, ConfigType, S3Config } from '@config';

@Injectable()
export class S3Service {
  private readonly logger = new Logger(S3Service.name);
  private readonly s3Client: S3Client;
  private readonly bucketName: string;
  private readonly cdnUrl: string;

  constructor(private readonly configService: ConfigService) {
    const s3Config = this.configService.getConfig<S3Config>(ConfigType.S3);

    // Kiểm tra và gán giá trị cho bucketName và cdnUrl
    this.bucketName = s3Config.s3?.bucketName || '';
    this.cdnUrl = s3Config.cdn?.url || '';

    // Kiểm tra các giá trị bắt buộc
    if (!this.bucketName) {
      this.logger.error('Missing required S3 configuration: bucketName');
    }

    if (!s3Config.s3?.endpoint) {
      this.logger.error('Missing required S3 configuration: endpoint');
    }

    if (!s3Config.s3?.region) {
      this.logger.error('Missing required S3 configuration: region');
    }

    if (!s3Config.s3?.accessKey || !s3Config.s3?.secretAccessKey) {
      this.logger.error('Missing required S3 configuration: credentials');
    }

    // Khởi tạo S3 client
    this.s3Client = new S3Client({
      region: s3Config.s3?.region,
      endpoint: s3Config.s3?.endpoint,
      credentials: {
        accessKeyId: s3Config.s3?.accessKey || '',
        secretAccessKey: s3Config.s3?.secretAccessKey || '',
      },
    });
  }

  /**
   * Tạo presigned URL để upload file
   * @param key S3 key
   * @param expirationTimeInMillis Thời gian hết hạn
   * @param type Loại file (hỗ trợ ImageTypeEnum, FileTypeEnum, VideoTypeEnum)
   * @param maxSize Kích thước tối đa
   * @returns Thông tin về presigned URL và các thông số liên quan
   */
  async createPresignedWithID(
    key: string,
    expirationTimeInMillis: TimeIntervalEnum,
    type: MediaType,
    maxSize: FileSizeEnum,
  ): Promise<string> {
    try {
      // Kiểm tra key
      if (!key || typeof key !== 'string') {
        throw new AppException(
          ErrorCode.CLOUD_FLARE_ERROR_UPLOAD,
          'Invalid S3 key provided',
          { key },
        );
      }

      // Chuẩn hóa key (loại bỏ dấu / ở đầu nếu có)
      const normalizedKey = key.startsWith('/') ? key.substring(1) : key;

      // Lấy content type từ MediaTypeUtil
      const contentType = MediaTypeUtil.getValue(type);

      // Chuyển đổi thời gian hết hạn sang giây
      const expiresIn = getTimeInterval(expirationTimeInMillis);

      // Tạo command để tạo presigned URL
      const command = new PutObjectCommand({
        Bucket: this.bucketName,
        Key: normalizedKey,
        ContentType: contentType,
        Metadata: {
          'x-amz-meta-max-file-size': maxSize.toString(),
          'x-amz-meta-media-type': type,
        },
      });

      // Tạo presigned URL với thời gian sống tùy chỉnh
      const url = await getSignedUrl(this.s3Client, command, { expiresIn });

      // Tạo URL download
      const downloadUrl = this.getDownloadUrl(normalizedKey);

      // Ghi log thành công
      this.logger.log(
        `Created presigned URL for ${normalizedKey} (${contentType}), expires in ${expiresIn}s`,
      );

      // Trả về thông tin đầy đủ để giúp client sử dụng dễ dàng hơn
      return url;
    } catch (error) {
      this.logger.error(
        `Cloudflare R2 Error creating presigned URL: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        ErrorCode.CLOUD_FLARE_ERROR_UPLOAD,
        `Failed to create upload URL: ${error.message}`,
        process.env.NODE_ENV === 'production'
          ? undefined
          : { error: error.message, stack: error.stack },
      );
    }
  }

  /**
   * Xóa file trên S3/Cloudflare R2
   * @param key Đường dẫn đến file cần xóa (S3 key)
   * @returns true nếu xóa thành công, false nếu có lỗi
   * @throws AppException nếu có lỗi từ Cloudflare R2
   *
   * Hàm này thực hiện việc xóa file từ bucket S3/Cloudflare R2 dựa trên key được cung cấp.
   * Key thường là đường dẫn đầy đủ đến file, ví dụ: 'uploads/images/avatar-123.jpg'
   *
   * Sử dụng DeleteObjectCommand từ AWS SDK để thực hiện thao tác xóa.
   * Nếu xóa thành công, hàm trả về true.
   * Nếu có lỗi, hàm sẽ ghi log lỗi và ném ra một AppException với mã lỗi phù hợp.
   */
  async deleteFile(key: string): Promise<boolean> {
    // Kiểm tra key
    if (!key || typeof key !== 'string') {
      throw new AppException(
        ErrorCode.CLOUD_FLARE_ERROR_DELETE,
        'Invalid S3 key provided',
        { key },
      );
    }
    try {
      // Chuẩn hóa key (loại bỏ dấu / ở đầu nếu có)
      const normalizedKey = key.startsWith('/') ? key.substring(1) : key;

      // Tạo command để xóa file
      const command = new DeleteObjectCommand({
        Bucket: this.bucketName,
        Key: normalizedKey,
      });

      // Thực hiện lệnh xóa file
      const response: DeleteObjectCommandOutput =
        await this.s3Client.send(command);

      // Kiểm tra kết quả (DeleteObjectCommand không trả về lỗi nếu file không tồn tại)
      const isSuccess =
        response.$metadata.httpStatusCode === 204 ||
        response.$metadata.httpStatusCode === 200;

      // Ghi log thành công
      if (isSuccess) {
        this.logger.log(`File deleted successfully: ${normalizedKey}`);
      } else {
        this.logger.warn(
          `Unexpected response when deleting file ${normalizedKey}: ${JSON.stringify(response)}`,
        );
      }

      return isSuccess;
    } catch (error) {
      // Ghi log lỗi chi tiết để dễ dàng debug
      this.logger.error(
        `Failed to delete file ${key} from S3/R2: ${error.message}`,
        error.stack,
      );

      // Ném ra exception để caller có thể xử lý
      throw new AppException(
        ErrorCode.CLOUD_FLARE_ERROR_DELETE,
        `Failed to delete file: ${error.message}`,
        process.env.NODE_ENV === 'production'
          ? undefined
          : { error: error.message, stack: error.stack },
      );
    }
  }

  /**
   * Xóa nhiều file cùng lúc trên S3/Cloudflare R2
   * @param keys Danh sách các đường dẫn đến file cần xóa (S3 keys)
   * @returns Kết quả xóa bao gồm danh sách các file đã xóa thành công và các file xóa thất bại
   * @throws AppException nếu có lỗi từ Cloudflare R2
   */
  async deleteFiles(keys: string[]): Promise<{
    deleted: string[];
    errors: { key: string; message: string }[];
  }> {
    // Kiểm tra nếu danh sách keys rỗng
    if (!keys || !Array.isArray(keys) || keys.length === 0) {
      this.logger.warn('Empty or invalid keys array provided for deletion');
      return { deleted: [], errors: [] };
    }

    // Kết quả tổng hợp
    const deleted: string[] = [];
    const errors: { key: string; message: string }[] = [];

    try {
      // Chuẩn hóa các key
      const normalizedKeys = keys.map((key) =>
        key.startsWith('/') ? key.substring(1) : key,
      );

      // Xử lý theo batch để tránh vượt quá giới hạn của S3/R2
      const BATCH_SIZE = 1000; // AWS S3 cho phép tối đa 1000 key trong một lần gọi DeleteObjects

      // Chia thành các batch nhỏ hơn
      for (let i = 0; i < normalizedKeys.length; i += BATCH_SIZE) {
        const batch = normalizedKeys.slice(i, i + BATCH_SIZE);

        // Chuẩn bị danh sách các đối tượng cần xóa
        const objects: ObjectIdentifier[] = batch.map((key) => ({ Key: key }));

        try {
          // Tạo command để xóa nhiều file
          const command = new DeleteObjectsCommand({
            Bucket: this.bucketName,
            Delete: {
              Objects: objects,
              Quiet: false, // Đặt Quiet = false để nhận danh sách đầy đủ các file đã xóa
            },
          });

          // Thực hiện lệnh xóa nhiều file
          const response: DeleteObjectsCommandOutput =
            await this.s3Client.send(command);

          // Xử lý kết quả
          if (response.Deleted) {
            const batchDeleted = response.Deleted.map(
              (item) => item.Key,
            ).filter((key) => key !== undefined);
            deleted.push(...batchDeleted);
            this.logger.log(
              `Batch delete successful: ${batchDeleted.length} files deleted`,
            );
          }

          if (response.Errors && response.Errors.length > 0) {
            const batchErrors = response.Errors.map((error) => ({
              key: error.Key || 'unknown',
              message: error.Message || 'Unknown error',
            }));
            errors.push(...batchErrors);
            this.logger.warn(
              `Batch delete had errors: ${batchErrors.length} files failed`,
              batchErrors,
            );
          }
        } catch (batchError) {
          // Nếu batch gặp lỗi, thêm tất cả các key trong batch vào danh sách lỗi
          const batchErrors = batch.map((key) => ({
            key,
            message: batchError.message || 'Batch processing error',
          }));
          errors.push(...batchErrors);
          this.logger.error(
            `Error processing batch: ${batchError.message}`,
            batchError.stack,
          );
        }
      }

      // Ghi log tổng kết
      this.logger.log(
        `Delete operation completed: ${deleted.length} files deleted, ${errors.length} errors`,
      );

      return { deleted, errors };
    } catch (error) {
      // Ghi log lỗi chi tiết để dễ dàng debug
      this.logger.error(
        `Failed to delete multiple files from S3/R2: ${error.message}`,
        error.stack,
      );

      // Ném ra exception để caller có thể xử lý
      throw new AppException(
        ErrorCode.CLOUD_FLARE_ERROR_DELETE,
        `Failed to delete multiple files: ${error.message}`,
        process.env.NODE_ENV === 'production'
          ? undefined
          : { error: error.message, stack: error.stack },
      );
    }
  }

  /**
   * Lấy URL download trực tiếp từ S3 cho file (sử dụng presigned URL)
   * @param key S3 key của file
   * @param expirationTimeInMillis Thời gian hết hạn (milliseconds)
   * @returns URL download trực tiếp từ S3
   * @throws AppException nếu key không hợp lệ hoặc có lỗi từ S3
   */
  async getDownloadUrl(
    key: string,
    expirationTimeInMillis: TimeIntervalEnum = TimeIntervalEnum.ONE_DAY,
  ): Promise<string> {
    // Kiểm tra key
    if (!key || typeof key !== 'string') {
      this.logger.warn(`Invalid key provided for download URL: ${key}`);
      throw new AppException(
        ErrorCode.CLOUD_FLARE_ERROR_DOWNLOAD,
        'Invalid key provided for download URL',
        { key },
      );
    }

    // Chuẩn hóa key (loại bỏ dấu / ở đầu nếu có)
    const normalizedKey = key.startsWith('/') ? key.substring(1) : key;

    // Kiểm tra xem key có rỗng không
    if (!normalizedKey.trim()) {
      this.logger.warn('Empty key provided for download URL');
      throw new AppException(
        ErrorCode.CLOUD_FLARE_ERROR_DOWNLOAD,
        'Empty key provided for download URL',
      );
    }

    try {
      // Chuyển đổi thời gian hết hạn sang giây
      const expiresIn = getTimeInterval(expirationTimeInMillis);

      // Tạo command để lấy file
      const command = new GetObjectCommand({
        Bucket: this.bucketName,
        Key: normalizedKey,
      });

      // Tạo presigned URL với thời gian sống tùy chỉnh
      const url = await getSignedUrl(this.s3Client, command, { expiresIn });

      // Ghi log thành công
      this.logger.debug(
        `Created S3 presigned download URL for ${normalizedKey}, expires in ${expiresIn}s`,
      );

      return url;
    } catch (error) {
      this.logger.error(
        `Failed to create S3 presigned download URL for ${normalizedKey}: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        ErrorCode.CLOUD_FLARE_ERROR_DOWNLOAD,
        `Failed to create download URL: ${error.message}`,
        process.env.NODE_ENV === 'production'
          ? undefined
          : { error: error.message, stack: error.stack },
      );
    }
  }

  /**
   * Lấy nhiều URL download trực tiếp từ S3 cho nhiều file (sử dụng presigned URL)
   * @param keys Danh sách các S3 key
   * @param expirationTimeInMillis Thời gian hết hạn (milliseconds)
   * @returns Kết quả bao gồm các URL download và các lỗi nếu có
   */
  async getDownloadUrls(
    keys: string[],
    expirationTimeInMillis: TimeIntervalEnum = TimeIntervalEnum.ONE_DAY,
  ): Promise<{
    results: { [key: string]: string };
    errors: { key: string; message: string }[];
  }> {
    // Kiểm tra nếu danh sách keys rỗng
    if (!keys || !Array.isArray(keys) || keys.length === 0) {
      this.logger.warn('Empty or invalid keys array provided');
      return { results: {}, errors: [] };
    }

    const results: { [key: string]: string } = {};
    const errors: { key: string; message: string }[] = [];

    // Tạo URL download cho từng key
    await Promise.all(
      keys.map(async (key) => {
        try {
          // Chuẩn hóa key (loại bỏ dấu / ở đầu nếu có)
          const normalizedKey = key.startsWith('/') ? key.substring(1) : key;

          // Lấy URL download
          results[key] = await this.getDownloadUrl(
            normalizedKey,
            expirationTimeInMillis,
          );
        } catch (error) {
          errors.push({
            key,
            message: error.message || 'Unknown error',
          });
          this.logger.error(
            `Failed to create download URL for ${key}: ${error.message}`,
            error.stack,
          );
        }
      }),
    );

    // Ghi log kết quả
    const successCount = Object.keys(results).length;
    this.logger.log(
      `Created ${successCount} download URLs, ${errors.length} errors`,
    );

    if (errors.length > 0) {
      this.logger.warn(
        `Failed to create download URLs for ${errors.length} keys`,
        errors,
      );
    }

    return { results, errors };
  }

  /**
   * Upload buffer trực tiếp lên S3/Cloudflare R2
   * @param key S3 key của file
   * @param buffer Buffer dữ liệu cần upload
   * @param contentType Content type của file
   * @returns true nếu upload thành công
   * @throws AppException nếu có lỗi khi upload
   */
  async uploadBuffer(
    key: string,
    buffer: Buffer,
    contentType: MediaType,
  ): Promise<boolean> {
    try {
      // Kiểm tra key
      if (!key || typeof key !== 'string') {
        throw new AppException(
          ErrorCode.CLOUD_FLARE_ERROR_UPLOAD,
          'Invalid S3 key provided',
          { key },
        );
      }

      // Chuẩn hóa key (loại bỏ dấu / ở đầu nếu có)
      const normalizedKey = key.startsWith('/') ? key.substring(1) : key;

      // Tạo command để upload file
      const command = new PutObjectCommand({
        Bucket: this.bucketName,
        Key: normalizedKey,
        Body: buffer,
        ContentType: contentType,
      });

      // Thực hiện lệnh upload file
      const response = await this.s3Client.send(command);

      // Kiểm tra kết quả
      const isSuccess =
        response.$metadata.httpStatusCode === 200 ||
        response.$metadata.httpStatusCode === 204;

      // Ghi log thành công
      if (isSuccess) {
        this.logger.log(`Buffer uploaded successfully to ${normalizedKey}`);
      } else {
        this.logger.warn(
          `Unexpected response when uploading buffer to ${normalizedKey}: ${JSON.stringify(response)}`,
        );
      }

      return isSuccess;
    } catch (error) {
      this.logger.error(
        `Failed to upload buffer to S3/R2: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        ErrorCode.CLOUD_FLARE_ERROR_UPLOAD,
        `Failed to upload buffer: ${error.message}`,
        process.env.NODE_ENV === 'production'
          ? undefined
          : { error: error.message, stack: error.stack },
      );
    }
  }

  /**
   * Tải file từ S3/R2 dưới dạng byte array
   * @param key S3 key của file cần tải
   * @returns Byte array của file
   * @throws AppException nếu có lỗi khi tải file
   */
  async downloadFileAsBytes(key: string): Promise<Uint8Array> {
    try {
      // Kiểm tra nếu key rỗng hoặc không hợp lệ
      if (!key || typeof key !== 'string') {
        this.logger.warn(`Invalid key provided for download: ${key}`);
        throw new AppException(
          ErrorCode.CLOUD_FLARE_ERROR_DOWNLOAD,
          'Invalid key provided for download',
          { key },
        );
      }

      // Chuẩn hóa key (loại bỏ dấu / ở đầu nếu có)
      const normalizedKey = key.startsWith('/') ? key.substring(1) : key;

      // Tạo command để lấy file
      const command = new GetObjectCommand({
        Bucket: this.bucketName,
        Key: normalizedKey,
      });

      // Gọi S3 API để lấy file
      const response = await this.s3Client.send(command);

      // Kiểm tra response
      if (!response.Body) {
        throw new Error('Empty response body');
      }

      // Đọc dữ liệu từ stream
      const chunks: Uint8Array[] = [];
      const stream = response.Body as any;

      // Sử dụng for await để đọc từ stream
      for await (const chunk of stream) {
        chunks.push(chunk);
      }

      // Nối các chunk lại với nhau
      const fileSize = chunks.reduce((acc, chunk) => acc + chunk.length, 0);
      const fileData = new Uint8Array(fileSize);
      let position = 0;

      for (const chunk of chunks) {
        fileData.set(chunk, position);
        position += chunk.length;
      }

      // Ghi log thành công
      this.logger.log(
        `Downloaded file as bytes successfully: ${normalizedKey}, size: ${fileSize} bytes`,
      );

      return fileData;
    } catch (error) {
      this.logger.error(
        `Failed to download file as bytes from S3/R2: ${key}: ${error.message}`,
        error.stack,
      );

      // Ném ra exception để caller có thể xử lý
      throw new AppException(
        ErrorCode.CLOUD_FLARE_ERROR_DOWNLOAD,
        `Failed to download file as bytes: ${error.message}`,
        process.env.NODE_ENV === 'production'
          ? undefined
          : { error: error.message, stack: error.stack },
      );
    }
  }

  /**
   * Sao chép file từ key nguồn sang key đích
   * @param sourceKey S3 key của file nguồn
   * @param destinationKey S3 key của file đích
   * @param destinationBucket Tên bucket đích (nếu khác với bucket nguồn)
   * @returns Thông tin về file đã sao chép
   * @throws AppException nếu có lỗi từ Cloudflare R2
   */
  async copyFile(
    sourceKey: string,
    destinationKey: string,
    destinationBucket?: string,
  ): Promise<{ etag: string; lastModified: Date }> {
    try {
      // Kiểm tra nếu key nguồn hoặc key đích rỗng hoặc không hợp lệ
      if (
        !sourceKey ||
        !destinationKey ||
        typeof sourceKey !== 'string' ||
        typeof destinationKey !== 'string'
      ) {
        this.logger.warn(
          `Invalid source or destination key: ${sourceKey} -> ${destinationKey}`,
        );
        throw new AppException(
          ErrorCode.CLOUD_FLARE_ERROR_COPY,
          'Invalid source or destination key',
        );
      }

      // Chuẩn hóa key (loại bỏ dấu / ở đầu nếu có)
      const normalizedSourceKey = sourceKey.startsWith('/')
        ? sourceKey.substring(1)
        : sourceKey;
      const normalizedDestinationKey = destinationKey.startsWith('/')
        ? destinationKey.substring(1)
        : destinationKey;

      // Xác định bucket đích (nếu không cung cấp, sử dụng bucket nguồn)
      const targetBucket = destinationBucket || this.bucketName;

      // Tạo command để sao chép file
      const command = new CopyObjectCommand({
        Bucket: targetBucket,
        Key: normalizedDestinationKey,
        CopySource: `${this.bucketName}/${normalizedSourceKey}`,
      });

      // Thực hiện lệnh sao chép file
      const response: CopyObjectCommandOutput =
        await this.s3Client.send(command);

      // Ghi log thành công
      this.logger.log(
        `File copied successfully: ${sourceKey} -> ${destinationKey}`,
      );

      return {
        etag: response.CopyObjectResult?.ETag?.replace(/\"/g, '') || '',
        lastModified: response.CopyObjectResult?.LastModified || new Date(),
      };
    } catch (error) {
      // Ghi log lỗi chi tiết để dễ dàng debug
      this.logger.error(
        `Failed to copy file from ${sourceKey} to ${destinationKey}: ${error.message}`,
        error.stack,
      );

      // Ném ra exception để caller có thể xử lý
      throw new AppException(
        ErrorCode.CLOUD_FLARE_ERROR_COPY,
        `Failed to copy file: ${error.message}`,
      );
    }
  }

  /**
   * Sao chép nhiều file từ key nguồn sang key đích
   * @param copyOperations Danh sách các thao tác sao chép
   * @returns Kết quả sao chép bao gồm các file đã sao chép thành công và các lỗi
   */
  async copyFiles(
    copyOperations: {
      sourceKey: string;
      destinationKey: string;
      destinationBucket?: string;
    }[],
  ): Promise<{
    copied: { sourceKey: string; destinationKey: string; etag: string }[];
    errors: { sourceKey: string; destinationKey: string; message: string }[];
  }> {
    // Kiểm tra nếu danh sách thao tác rỗng
    if (
      !copyOperations ||
      !Array.isArray(copyOperations) ||
      copyOperations.length === 0
    ) {
      this.logger.warn('Empty or invalid copy operations array provided');
      return { copied: [], errors: [] };
    }

    const copied: {
      sourceKey: string;
      destinationKey: string;
      etag: string;
    }[] = [];
    const errors: {
      sourceKey: string;
      destinationKey: string;
      message: string;
    }[] = [];

    // Thực hiện sao chép từng file
    await Promise.all(
      copyOperations.map(
        async ({ sourceKey, destinationKey, destinationBucket }) => {
          try {
            const result = await this.copyFile(
              sourceKey,
              destinationKey,
              destinationBucket,
            );
            copied.push({
              sourceKey,
              destinationKey,
              etag: result.etag,
            });
          } catch (error) {
            errors.push({
              sourceKey,
              destinationKey,
              message: error.message || 'Unknown error',
            });
            this.logger.error(
              `Failed to copy file from ${sourceKey} to ${destinationKey}: ${error.message}`,
            );
          }
        },
      ),
    );

    // Ghi log kết quả
    this.logger.log(
      `Copy operations completed: ${copied.length} files copied, ${errors.length} errors`,
    );

    if (errors.length > 0) {
      this.logger.warn('Errors during copy operations:', errors);
    }

    return { copied, errors };
  }
}
