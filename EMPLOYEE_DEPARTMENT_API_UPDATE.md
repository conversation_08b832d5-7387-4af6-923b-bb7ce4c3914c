# Cập Nhật API Employee với Thông Tin Department

## Tổng Quan

Đã cập nhật API `GET /api/hrm/employees` để bao gồm thông tin chi tiết về phòng ban (department) của từng nhân viên.

## Thay Đổi Thực Hiện

### 1. Tạo DTO Mới

**File:** `src/modules/hrm/employees/dto/employee-with-department-response.dto.ts`

- `DepartmentInfoDto`: DTO chứa thông tin cơ bản của phòng ban
- `EmployeeWithDepartmentResponseDto`: DTO mở rộng từ employee response với thêm thông tin department

### 2. Cập Nhật Employee Service

**File:** `src/modules/hrm/employees/services/employee.service.ts`

- Thêm dependency injection cho `DepartmentService`
- Tạo method mới `findAllWithDepartment()` để lấy employees kèm thông tin department
- Logic:
  1. <PERSON><PERSON><PERSON> danh sách employees từ repository
  2. <PERSON><PERSON> thập các department IDs unique
  3. <PERSON><PERSON>y thông tin departments từ DepartmentService
  4. <PERSON> <PERSON><PERSON> li<PERSON> sang DTO mới

### 3. Cập Nhật Employee Controller

**File:** `src/modules/hrm/employees/controllers/employee.controller.ts`

- Import DTO mới
- Cập nhật method `findAll()` để sử dụng `findAllWithDepartment()`
- Cập nhật Swagger documentation

### 4. Cập Nhật Module Dependencies

**File:** `src/modules/hrm/employees/employees.module.ts`

- Import `OrgUnitsModule` để có thể sử dụng `DepartmentService`

### 5. Cập Nhật Export

**File:** `src/modules/hrm/employees/dto/index.ts`

- Export DTO mới để có thể sử dụng ở nơi khác

## API Response Mới

### Trước (chỉ có departmentId):
```json
{
  "success": true,
  "data": {
    "items": [
      {
        "id": 1,
        "employeeCode": "EMP001",
        "employeeName": "Nguyễn Văn A",
        "departmentId": 1,
        "jobTitle": "Software Engineer",
        ...
      }
    ],
    "meta": { ... }
  }
}
```

### Sau (có thông tin department đầy đủ):
```json
{
  "success": true,
  "data": {
    "items": [
      {
        "id": 1,
        "employeeCode": "EMP001",
        "employeeName": "Nguyễn Văn A",
        "departmentId": 1,
        "department": {
          "id": 1,
          "name": "Phòng Kỹ thuật",
          "description": "Phòng ban phụ trách các vấn đề kỹ thuật của công ty",
          "managerId": 5,
          "parentId": null
        },
        "jobTitle": "Software Engineer",
        ...
      }
    ],
    "meta": { ... }
  }
}
```

## Endpoint

**URL:** `GET http://localhost:3001/api/hrm/employees`

**Query Parameters:**
- `page`: Số trang (mặc định: 1)
- `limit`: Số lượng items per page (mặc định: 10)
- `search`: Tìm kiếm theo tên nhân viên
- `departmentId`: Filter theo phòng ban
- `status`: Filter theo trạng thái nhân viên
- `employmentType`: Filter theo loại hình làm việc
- `sortBy`: Sắp xếp theo trường (mặc định: employeeCode)
- `sortDirection`: Hướng sắp xếp (ASC/DESC, mặc định: ASC)

**Headers:**
- `Authorization: Bearer <JWT_TOKEN>`
- `Content-Type: application/json`

## Lưu Ý Kỹ Thuật

1. **Performance**: API sử dụng cách tiếp cận lấy department riêng biệt thay vì JOIN để tránh phức tạp và phù hợp với nguyên tắc không dùng relationship mapping của user.

2. **Error Handling**: Nếu không tìm thấy department, sẽ log warning và trả về `null` cho trường `department`.

3. **Backward Compatibility**: Vẫn giữ nguyên trường `departmentId` để đảm bảo tương thích ngược.

4. **Tenant Isolation**: Đảm bảo tenant isolation cho cả employee và department queries.

## Test

Sử dụng file `test-employee-api.http` để test các endpoint:

```http
GET http://localhost:3001/api/hrm/employees?page=1&limit=10
Authorization: Bearer YOUR_JWT_TOKEN_HERE
```

## Swagger Documentation

Truy cập http://localhost:3001/api/docs để xem documentation chi tiết của API.
