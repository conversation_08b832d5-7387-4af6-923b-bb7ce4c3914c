import { applyDecorators, SetMetadata, UseGuards } from '@nestjs/common';
import { JwtUserGuard } from '@/modules/auth/guards/jwt-user.guard';
import { ApiBearerAuth } from '@nestjs/swagger';

/**
 * Key metadata để đánh dấu controller hoặc route cần áp dụng bảo mật tenant
 */
export const TENANT_SECURITY_KEY = 'tenantSecurity';

/**
 * Decorator để áp dụng bảo mật tenant cho controller hoặc route
 * Decorator này sẽ:
 * 1. <PERSON><PERSON><PERSON> dấu controller/route cần áp dụng bảo mật tenant
 * 2. Áp dụng JwtUserGuard để đảm bảo người dùng đã đăng nhập
 * 3. Cấu hình Swagger để hiển thị yêu cầu xác thực JWT
 *
 * @returns Decorator tổng hợp
 */
export function TenantSecurity() {
  return applyDecorators(
    SetMetadata(TENANT_SECURITY_KEY, true),
    UseGuards(JwtUserGuard),
    ApiBearerAuth('JWT-auth'),
  );
}
