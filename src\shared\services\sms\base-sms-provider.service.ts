import { Logger } from '@nestjs/common';
import {
  ISmsProvider,
  SmsResponse,
  BulkSmsResponse,
  MessageStatusResponse,
  ConnectionTestResponse,
  MessageStatus,
} from './sms-provider.interface';

/**
 * Lớp cơ sở trừu tượng cho các nhà cung cấp SMS
 * Triển khai các chức năng chung cho tất cả các nhà cung cấp
 */
export abstract class BaseSmsProvider implements ISmsProvider {
  protected readonly logger: Logger;

  /**
   * Tên nhà cung cấp SMS
   */
  abstract readonly providerName: string;

  constructor(loggerName: string) {
    this.logger = new Logger(loggerName);
  }

  /**
   * G<PERSON>i tin nhắn SMS đến một số điện thoại
   * @param phoneNumber Số điện thoại của người nhận
   * @param message Nội dung tin nhắn
   * @param options Cá<PERSON> tùy chọn bổ sung dành riêng cho nhà cung cấp
   * @returns Promise chứa ID tin nhắn hoặc lỗi
   */
  abstract sendSms(
    phoneNumber: string,
    message: string,
    options?: any,
  ): Promise<SmsResponse>;

  /**
   * Gửi tin nhắn SMS đến nhiều số điện thoại
   * @param phoneNumbers Danh sách số điện thoại của người nhận
   * @param message Nội dung tin nhắn
   * @param options Các tùy chọn bổ sung dành riêng cho nhà cung cấp
   * @returns Promise chứa kết quả cho từng người nhận
   */
  async sendBulkSms(
    phoneNumbers: string[],
    message: string,
    options?: any,
  ): Promise<BulkSmsResponse> {
    this.logger.debug(
      `Gửi hàng loạt SMS đến ${phoneNumbers.length} người nhận`,
    ); // Gửi hàng loạt SMS

    const results: {
      phoneNumber: string;
      success: boolean;
      messageId?: string;
      errorCode?: string;
      errorMessage?: string;
    }[] = [];
    let successCount = 0;
    let failureCount = 0;

    // Triển khai mặc định: gửi SMS đến từng người nhận riêng lẻ
    for (const phoneNumber of phoneNumbers) {
      try {
        const response = await this.sendSms(phoneNumber, message, options);

        if (response.success) {
          successCount++;
          results.push({
            phoneNumber,
            success: true,
            messageId: response.messageId,
          });
        } else {
          failureCount++;
          results.push({
            phoneNumber,
            success: false,
            errorCode: response.errorCode,
            errorMessage: response.errorMessage,
          });
        }
      } catch (error) {
        failureCount++;
        results.push({
          phoneNumber,
          success: false,
          errorMessage: error.message || 'Lỗi không xác định', // Lỗi không xác định
        });
      }
    }

    return {
      successCount,
      failureCount,
      results,
    };
  }

  /**
   * Kiểm tra trạng thái của tin nhắn đã gửi
   * @param messageId ID của tin nhắn cần kiểm tra
   * @returns Promise chứa trạng thái của tin nhắn
   */
  abstract checkMessageStatus(
    messageId: string,
  ): Promise<MessageStatusResponse>;

  /**
   * Gửi tin nhắn SMS với brandname
   * @param phoneNumber Số điện thoại của người nhận
   * @param message Nội dung tin nhắn
   * @param brandname Tên thương hiệu sử dụng làm người gửi
   * @param options Các tùy chọn bổ sung dành riêng cho nhà cung cấp
   * @returns Promise chứa ID tin nhắn hoặc lỗi
   */
  abstract sendBrandnameSms(
    phoneNumber: string,
    message: string,
    brandname: string,
    options?: any,
  ): Promise<SmsResponse>;

  /**
   * Gửi tin nhắn SMS OTP (One-Time Password)
   * @param phoneNumber Số điện thoại của người nhận
   * @param otpCode Mã OTP cần gửi
   * @param options Các tùy chọn bổ sung dành riêng cho nhà cung cấp
   * @returns Promise chứa ID tin nhắn hoặc lỗi
   */
  async sendOtp(
    phoneNumber: string,
    otpCode: string,
    options?: any,
  ): Promise<SmsResponse> {
    this.logger.debug(`Gửi mã OTP ${otpCode} đến số điện thoại ${phoneNumber}`); // Gửi mã OTP

    // Mặc định, sử dụng một mẫu chuẩn cho tin nhắn OTP
    const message = options?.template
      ? options.template.replace('{code}', otpCode)
      : `Mã xác thực của bạn là: ${otpCode}`; // Mã xác thực

    return this.sendSms(phoneNumber, message, options);
  }

  /**
   * Kiểm tra kết nối với nhà cung cấp SMS
   * @param config Cấu hình của nhà cung cấp
   * @returns Promise chỉ ra liệu kết nối có thành công hay không
   */
  abstract testConnection(config: any): Promise<ConnectionTestResponse>;

  /**
   * Định dạng số điện thoại theo chuẩn quốc tế
   * @param phoneNumber Số điện thoại cần định dạng
   * @returns Số điện thoại đã được định dạng
   */
  protected formatPhoneNumber(phoneNumber: string): string {
    // Loại bỏ tất cả các ký tự không phải số
    let cleaned = phoneNumber.replace(/\D/g, '');

    // Nếu số điện thoại bắt đầu bằng số 0, thay thế bằng mã quốc gia của Việt Nam (+84)
    if (cleaned.startsWith('0')) {
      cleaned = '84' + cleaned.substring(1);
    }

    // Thêm dấu + nếu cần thiết
    if (!cleaned.startsWith('+')) {
      cleaned = '+' + cleaned;
    }

    return cleaned;
  }

  /**
   * Chuyển đổi trạng thái cụ thể của nhà cung cấp thành trạng thái chuẩn
   * @param providerStatus Trạng thái cụ thể của nhà cung cấp
   * @returns Trạng thái chuẩn
   */
  protected mapToStandardStatus(_providerStatus: string): MessageStatus {
    // Phương thức này phải được ghi đè bởi các lớp con
    return MessageStatus.UNKNOWN;
  }
}
