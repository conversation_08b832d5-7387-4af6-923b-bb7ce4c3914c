import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { AxiosRequestConfig } from 'axios';
import { BaseMarketingAiServiceImpl } from '../base-marketing-ai.service';
import {
  VideoGenerationOptions,
  VideoGenerationResult,
  VideoGenerationService,
  VideoResolution,
  // VideoFormat, // Unused import
  // VideoStyle, // Unused import
  MarketingAiResponse,
} from '../interfaces';

/**
 * Runway ML service for video generation
 */
@Injectable()
export class RunwayMlService
  extends BaseMarketingAiServiceImpl
  implements VideoGenerationService
{
  readonly serviceName = 'Runway ML';
  protected readonly baseUrl = 'https://api.runwayml.com/v1';
  protected readonly apiKey: string | undefined;

  constructor(private readonly configService: ConfigService) {
    super(RunwayMlService.name);
    this.apiKey = this.configService.get<string>('RUNWAY_API_KEY');

    if (!this.apiKey) {
      this.logger.warn(
        'RUNWAY_API_KEY is not defined in environment variables',
      );
    }
  }

  /**
   * Test the connection to the Runway ML API
   * @returns A promise that resolves to a boolean indicating if the connection was successful
   */
  async testConnection(): Promise<boolean> {
    try {
      const url = `${this.baseUrl}/user`;
      const config = this.createRequestConfig();

      const response = await this.axiosInstance.get(url, config);
      return response.status === 200;
    } catch (error) {
      this.logger.error(
        `Connection test failed: ${error.message}`,
        error.stack,
      );
      return false;
    }
  }

  /**
   * Create a request configuration with authentication
   * @param config Additional request configuration
   * @returns Request configuration with authentication
   */
  protected createRequestConfig(
    config?: AxiosRequestConfig,
  ): AxiosRequestConfig {
    if (!this.apiKey) {
      throw new Error('Runway ML API key is not defined');
    }

    return {
      ...config,
      headers: {
        ...config?.headers,
        Authorization: `Bearer ${this.apiKey}`,
        'Content-Type': 'application/json',
      },
    };
  }

  /**
   * Map resolution option to Runway ML dimensions
   * @param resolution Resolution option
   * @returns Object with width and height
   */
  private mapResolutionToRunwayDimensions(
    resolution: VideoResolution = VideoResolution.HD,
  ): { width: number; height: number } {
    switch (resolution) {
      case VideoResolution.SD:
        return { width: 640, height: 360 };
      case VideoResolution.HD:
        return { width: 1280, height: 720 };
      case VideoResolution.FULL_HD:
        return { width: 1920, height: 1080 };
      case VideoResolution.ULTRA_HD:
        return { width: 3840, height: 2160 };
      default:
        return { width: 1280, height: 720 };
    }
  }

  /**
   * Generate a video from a text prompt using Runway ML
   * @param prompt Text prompt to generate video from
   * @param options Options for video generation
   * @returns A promise that resolves to a response containing the generated video
   */
  async generateVideo(
    prompt: string,
    options?: VideoGenerationOptions,
  ): Promise<MarketingAiResponse<VideoGenerationResult>> {
    try {
      const { width, height } = this.mapResolutionToRunwayDimensions(
        options?.resolution,
      );

      const duration = options?.duration || 15;

      // Runway ML Gen-2 API endpoint
      const url = `${this.baseUrl}/text-to-video`;

      const data = {
        prompt,
        negative_prompt: '',
        num_frames: duration * 30, // Assuming 30 fps
        width,
        height,
        guidance_scale: 7.5,
        mode: 'standard',
      };

      const config = this.createRequestConfig({
        timeout: options?.timeout || 300000, // 5 minutes timeout for video generation
      });

      // Start the generation
      const response = await this.axiosInstance.post(url, data, config);

      const jobId = response.data.id;

      // Return the job ID for status checking
      const result: VideoGenerationResult = {
        videoUrl: '',
        duration,
        prompt,
        status: 'processing',
        jobId,
      };

      return this.createSuccessResponse(result, response.data);
    } catch (error) {
      return this.handleApiError<VideoGenerationResult>(
        error,
        'Runway ML video generation',
      );
    }
  }

  /**
   * Check the status of a video generation job
   * @param jobId ID of the video generation job
   * @returns A promise that resolves to a response containing the status of the job
   */
  async checkVideoGenerationStatus(
    jobId: string,
  ): Promise<MarketingAiResponse<VideoGenerationResult>> {
    try {
      const url = `${this.baseUrl}/text-to-video/${jobId}`;
      const config = this.createRequestConfig();

      const response = await this.axiosInstance.get(url, config);

      const status = response.data.status;
      let videoStatus: 'completed' | 'processing' | 'failed' = 'processing';

      if (status === 'COMPLETED') {
        videoStatus = 'completed';
      } else if (status === 'FAILED') {
        videoStatus = 'failed';
      }

      const result: VideoGenerationResult = {
        videoUrl: response.data.output?.url || '',
        thumbnailUrl: response.data.output?.thumbnail_url || '',
        duration: response.data.output?.duration || 0,
        prompt: response.data.input?.prompt || '',
        status: videoStatus,
        jobId,
      };

      return this.createSuccessResponse(result, response.data);
    } catch (error) {
      return this.handleApiError<VideoGenerationResult>(
        error,
        'Runway ML video status check',
      );
    }
  }

  /**
   * Generate a video from an image and text prompt
   * @param imageUrl URL of the image to use as a reference
   * @param prompt Text prompt to guide the video generation
   * @param options Options for video generation
   * @returns A promise that resolves to a response containing the generated video
   */
  async generateVideoFromImage(
    imageUrl: string,
    prompt: string,
    options?: VideoGenerationOptions,
  ): Promise<MarketingAiResponse<VideoGenerationResult>> {
    try {
      const { width, height } = this.mapResolutionToRunwayDimensions(
        options?.resolution,
      );

      const duration = options?.duration || 15;

      // Runway ML Gen-2 API endpoint for image-to-video
      const url = `${this.baseUrl}/image-to-video`;

      const data = {
        image_url: imageUrl,
        prompt,
        negative_prompt: '',
        num_frames: duration * 30, // Assuming 30 fps
        width,
        height,
        guidance_scale: 7.5,
        motion_bucket_id: 40,
      };

      const config = this.createRequestConfig({
        timeout: options?.timeout || 300000, // 5 minutes timeout for video generation
      });

      // Start the generation
      const response = await this.axiosInstance.post(url, data, config);

      const jobId = response.data.id;

      // Return the job ID for status checking
      const result: VideoGenerationResult = {
        videoUrl: '',
        duration,
        prompt,
        status: 'processing',
        jobId,
      };

      return this.createSuccessResponse(result, response.data);
    } catch (error) {
      return this.handleApiError<VideoGenerationResult>(
        error,
        'Runway ML image-to-video generation',
      );
    }
  }

  /**
   * Edit an existing video using a text prompt
   * @param videoUrl URL of the video to edit
   * @param prompt Text prompt to guide the editing
   * @param options Options for video editing
   * @returns A promise that resolves to a response containing the edited video
   */
  async editVideo(
    videoUrl: string,
    prompt: string,
    options?: VideoGenerationOptions,
  ): Promise<MarketingAiResponse<VideoGenerationResult>> {
    try {
      // Runway ML doesn't have a direct video editing API
      // This is a simplified implementation

      this.logger.warn(
        'Runway ML API does not directly support video editing. Generating a new video based on the prompt.',
      );

      // Generate a new video with the prompt
      return this.generateVideo(`Edit this video: ${prompt}`, options);
    } catch (error) {
      return this.handleApiError<VideoGenerationResult>(
        error,
        'Runway ML video editing',
      );
    }
  }
}
