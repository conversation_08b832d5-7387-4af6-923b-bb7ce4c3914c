import { ApiProperty } from '@nestjs/swagger';
import { TodoStatus } from '../../enum/todo-status.enum';
import { ProjectMemberRole } from '../../enum/project-member-role.enum';

/**
 * DTO cho thông tin công việc trong biểu đồ Gantt
 */
export class GanttTaskDto {
  /**
   * ID công việc
   * @example 1
   */
  @ApiProperty({
    description: 'ID công việc',
    example: 1,
  })
  id: number;

  /**
   * Tiêu đề công việc
   * @example "Thiết kế giao diện"
   */
  @ApiProperty({
    description: 'Tiêu đề công việc',
    example: 'Thiết kế giao diện',
  })
  title: string;

  /**
   * Thời gian bắt đầu (timestamp)
   * @example 1625097600000
   */
  @ApiProperty({
    description: 'Thời gian bắt đầ<PERSON> (timestamp)',
    example: 1625097600000,
  })
  start: number;

  /**
   * Thời gian kết thúc (timestamp)
   * @example 1625702400000
   */
  @ApiProperty({
    description: 'Thời gian kết thúc (timestamp)',
    example: 1625702400000,
  })
  end: number;

  /**
   * Tiến độ hoàn thành (%)
   * @example 75
   */
  @ApiProperty({
    description: 'Tiến độ hoàn thành (%)',
    example: 75,
  })
  progress: number;

  /**
   * Trạng thái công việc
   * @example "in_progress"
   */
  @ApiProperty({
    description: 'Trạng thái công việc',
    enum: TodoStatus,
    example: TodoStatus.IN_PROGRESS,
  })
  status: TodoStatus;

  /**
   * ID người được giao
   * @example 2
   */
  @ApiProperty({
    description: 'ID người được giao',
    example: 2,
  })
  assigneeId: number;

  /**
   * Tên người được giao
   * @example "User 2"
   */
  @ApiProperty({
    description: 'Tên người được giao',
    example: 'User 2',
  })
  assigneeName: string;

  /**
   * Danh sách ID công việc phụ thuộc
   * @example [3, 4]
   */
  @ApiProperty({
    description: 'Danh sách ID công việc phụ thuộc',
    example: [3, 4],
    type: [Number],
  })
  dependencies: number[];
}

/**
 * DTO cho thông tin thành viên trong biểu đồ Gantt
 */
export class GanttMemberDto {
  /**
   * ID người dùng
   * @example 1
   */
  @ApiProperty({
    description: 'ID người dùng',
    example: 1,
  })
  userId: number;

  /**
   * Vai trò trong dự án
   * @example "admin"
   */
  @ApiProperty({
    description: 'Vai trò trong dự án',
    enum: ProjectMemberRole,
    example: ProjectMemberRole.ADMIN,
  })
  role: ProjectMemberRole;
}

/**
 * DTO cho phản hồi dữ liệu biểu đồ Gantt
 */
export class ProjectGanttResponseDto {
  /**
   * Thông tin dự án
   */
  @ApiProperty({
    description: 'Thông tin dự án',
    example: {
      id: 1,
      name: 'Dự án A',
      description: 'Mô tả dự án A',
      isActive: true,
    },
  })
  project: {
    id: number;
    name: string;
    description: string | null;
    isActive: boolean;
  };

  /**
   * Phạm vi thời gian
   */
  @ApiProperty({
    description: 'Phạm vi thời gian',
    example: {
      start: 1625097600000,
      end: 1627689600000,
    },
  })
  timeRange: {
    start: number;
    end: number;
  };

  /**
   * Danh sách công việc
   */
  @ApiProperty({
    description: 'Danh sách công việc',
    type: [GanttTaskDto],
  })
  tasks: GanttTaskDto[];

  /**
   * Danh sách thành viên
   */
  @ApiProperty({
    description: 'Danh sách thành viên',
    type: [GanttMemberDto],
  })
  members: GanttMemberDto[];
}
