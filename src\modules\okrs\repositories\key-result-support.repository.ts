import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { KeyResultSupport } from '../entities/key-result-supports';

/**
 * Repository for key result support relationships
 */
@Injectable()
export class KeyResultSupportRepository {
  private readonly logger = new Logger(KeyResultSupportRepository.name);

  constructor(
    @InjectRepository(KeyResultSupport)
    private readonly repository: Repository<KeyResultSupport>,
  ) {}

  /**
   * Find supporting key results for a parent key result
   * @param parentId Parent key result ID
   * @returns List of key result support relationships
   */
  async findByParentId(parentId: number): Promise<KeyResultSupport[]> {
    return this.repository.find({
      where: { parentId },
    });
  }

  /**
   * Find key results supported by a child key result
   * @param childId Child key result ID
   * @returns List of key result support relationships
   */
  async findByChildId(childId: number): Promise<KeyResultSupport[]> {
    return this.repository.find({
      where: { childId },
    });
  }

  /**
   * Create a new key result support relationship
   * @param parentId Parent key result ID
   * @param childId Child key result ID
   * @returns Created key result support relationship
   */
  async create(parentId: number, childId: number): Promise<KeyResultSupport> {
    const support = this.repository.create({ parentId, childId });
    return this.repository.save(support);
  }

  /**
   * Create multiple key result support relationships
   * @param parentId Parent key result ID
   * @param childIds List of child key result IDs
   * @returns List of created key result support relationships
   */
  async createMany(
    parentId: number,
    childIds: number[],
  ): Promise<KeyResultSupport[]> {
    const supports = childIds.map((childId) =>
      this.repository.create({ parentId, childId }),
    );
    return this.repository.save(supports);
  }

  /**
   * Delete a key result support relationship
   * @param parentId Parent key result ID
   * @param childId Child key result ID
   * @returns True if deleted, false if not found
   */
  async delete(parentId: number, childId: number): Promise<boolean> {
    const result = await this.repository.delete({ parentId, childId });
    return (result.affected ?? 0) > 0;
  }

  /**
   * Delete all support relationships for a parent key result
   * @param parentId Parent key result ID
   * @returns True if deleted, false if not found
   */
  async deleteByParentId(parentId: number): Promise<boolean> {
    const result = await this.repository.delete({ parentId });
    return (result.affected ?? 0) > 0;
  }

  /**
   * Delete all support relationships for a child key result
   * @param childId Child key result ID
   * @returns True if deleted, false if not found
   */
  async deleteByChildId(childId: number): Promise<boolean> {
    const result = await this.repository.delete({ childId });
    return (result.affected ?? 0) > 0;
  }

  /**
   * Check if a support relationship exists
   * @param parentId Parent key result ID
   * @param childId Child key result ID
   * @returns True if exists, false if not
   */
  async exists(parentId: number, childId: number): Promise<boolean> {
    const count = await this.repository.count({
      where: { parentId, childId },
    });
    return count > 0;
  }

  /**
   * Check if adding a support relationship would create a cycle
   * @param parentId Parent key result ID
   * @param childId Child key result ID
   * @returns True if a cycle would be created, false otherwise
   */
  async wouldCreateCycle(parentId: number, childId: number): Promise<boolean> {
    // If parent and child are the same, it would create a cycle
    if (parentId === childId) {
      return true;
    }

    // Check if child supports parent (direct cycle)
    const directCycle = await this.exists(childId, parentId);
    if (directCycle) {
      return true;
    }

    // Check for indirect cycles
    return this.checkIndirectCycle(parentId, childId, new Set<number>());
  }

  /**
   * Recursively check for indirect cycles in support relationships
   * @param targetId Target key result ID to check if it's supported by startId
   * @param startId Starting key result ID
   * @param visited Set of visited key result IDs
   * @returns True if a cycle would be created, false otherwise
   */
  private async checkIndirectCycle(
    targetId: number,
    startId: number,
    visited: Set<number>,
  ): Promise<boolean> {
    // Mark current node as visited
    visited.add(startId);

    // Get all key results supported by startId
    const supports = await this.findByParentId(startId);

    for (const support of supports) {
      // If we found the target, there's a cycle
      if (support.childId === targetId) {
        return true;
      }

      // If we haven't visited this child yet, check it recursively
      if (!visited.has(support.childId)) {
        const hasCycle = await this.checkIndirectCycle(
          targetId,
          support.childId,
          visited,
        );
        if (hasCycle) {
          return true;
        }
      }
    }

    return false;
  }
}
