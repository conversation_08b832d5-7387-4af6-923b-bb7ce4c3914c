import { ApiProperty } from '@nestjs/swagger';
import { Exclude, Expose } from 'class-transformer';

/**
 * DTO đại diện cho thông tin vai trò trong danh sách vai trò của người dùng
 */
@Exclude()
export class UserRoleItemDto {
  /**
   * ID của vai trò
   */
  @Expose()
  @ApiProperty({
    description: 'ID của vai trò',
    example: 1,
  })
  id: number;

  /**
   * Tên vai trò
   */
  @Expose()
  @ApiProperty({
    description: 'Tên vai trò',
    example: 'Admin',
  })
  name: string;

  /**
   * <PERSON>ô tả vai trò
   */
  @Expose()
  @ApiProperty({
    description: 'Mô tả vai trò',
    example: 'Quản trị viên với tất cả quyền',
    nullable: true,
  })
  description: string | null;

  /**
   * Loại vai trò
   */
  @Expose()
  @ApiProperty({
    description: 'Loại vai trò',
    example: 'ADMIN',
    nullable: true,
  })
  type: string | null;

  constructor(partial: Partial<UserRoleItemDto>) {
    Object.assign(this, partial);
  }
}

/**
 * DTO đại diện cho danh sách vai trò của người dùng
 */
export class UserRolesResponseDto {
  /**
   * ID của người dùng
   */
  @ApiProperty({
    description: 'ID của người dùng',
    example: 1,
  })
  userId: number;

  /**
   * Danh sách vai trò của người dùng
   */
  @ApiProperty({
    description: 'Danh sách vai trò của người dùng',
    type: [UserRoleItemDto],
  })
  roles: UserRoleItemDto[];
}
