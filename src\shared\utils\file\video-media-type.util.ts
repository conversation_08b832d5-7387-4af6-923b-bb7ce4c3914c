import { AppException, ErrorCode } from '@common/exceptions/app.exception';

/**
 * Enum định nghĩa các loại MIME cho video
 */
export enum VideoTypeEnum {
  FLV = 'video/x-flv',
  MOV = 'video/quicktime',
  MPEG = 'video/mpeg',
  MPEGS = 'video/mpegs',
  MPGS = 'video/mpgs',
  MPG = 'video/mpg',
  MP4 = 'video/mp4',
  WEBM = 'video/webm',
  WMV = 'video/wmv',
  GP3 = 'video/3gpp',
}

/**
 * Object tiện ích để làm việc với VideoTypeEnum
 */
export const VideoType = {
  /**
   * Lấy giá trị chuỗi của một loại video
   * @param type Loại video
   * @returns Giá trị MIME tương ứng
   */
  getValue(type: VideoTypeEnum): string {
    return type;
  },

  /**
   * Lấy enum VideoTypeEnum từ tên loại video hoặc giá trị MIME type
   * @param type Tên loại video (key của enum) hoặc giá trị MIME type (ví dụ: 'video/mp4')
   * @returns Giá trị enum VideoTypeEnum tương ứng
   * @throws AppException nếu loại video không tồn tại
   */
  getType(type: string): VideoTypeEnum {
    // Kiểm tra nếu là giá trị MIME type (ví dụ: 'video/mp4')
    const entries = Object.entries(VideoTypeEnum);
    const entry = entries.find(([_, value]) => value === type);

    if (entry) {
      return VideoTypeEnum[entry[0] as keyof typeof VideoTypeEnum];
    }

    // Nếu không tìm thấy, ném exception
    throw new AppException(
      ErrorCode.FILE_TYPE_NOT_FOUND,
      `Loại video '${type}' không được hỗ trợ`,
    );
  },
};
