import { ApiProperty } from '@nestjs/swagger';
import { EmployeeStatus } from '../enum/employee-status.enum';
import { EmploymentType } from '../enum/employment-type.enum';
import { MaritalStatus } from '../enum/marital-status.enum';
import { Gender } from '../enum/gender.enum';

/**
 * DTO for employee response
 */
export class EmployeeResponseDto {
  /**
   * Employee ID
   * @example 1
   */
  @ApiProperty({ description: 'Employee ID', example: 1 })
  id: number;

  /**
   * Employee code
   * @example "EMP001"
   */
  @ApiProperty({ description: 'Employee code', example: 'EMP001' })
  employeeCode: string;

  /**
   * Employee name
   * @example "Nguyễn Văn A"
   */
  @ApiProperty({ description: 'Employee name', example: 'Nguyễn Văn A' })
  employeeName: string;

  /**
   * Date of birth
   * @example "1990-01-15"
   */
  @ApiProperty({
    description: 'Date of birth',
    example: '1990-01-15',
    nullable: true,
  })
  dateOfBirth: Date | null;

  /**
   * Gender
   * @example "male"
   */
  @ApiProperty({
    description: 'Gender',
    enum: Gender,
    example: Gender.MALE,
    nullable: true,
  })
  gender: Gender | null;

  /**
   * Department ID
   * @example 1
   */
  @ApiProperty({ description: 'Department ID', example: 1, nullable: true })
  departmentId: number | null;

  /**
   * Job title
   * @example "Software Engineer"
   */
  @ApiProperty({
    description: 'Job title',
    example: 'Software Engineer',
    nullable: true,
  })
  jobTitle: string | null;

  /**
   * Job level
   * @example "Senior"
   */
  @ApiProperty({ description: 'Job level', example: 'Senior', nullable: true })
  jobLevel: string | null;

  /**
   * Manager ID
   * @example 2
   */
  @ApiProperty({ description: 'Manager ID', example: 2, nullable: true })
  managerId: number | null;

  /**
   * Employment type
   * @example "full_time"
   */
  @ApiProperty({
    description: 'Employment type',
    enum: EmploymentType,
    example: EmploymentType.FULL_TIME,
    nullable: true,
  })
  employmentType: EmploymentType | null;

  /**
   * Employee status
   * @example "active"
   */
  @ApiProperty({
    description: 'Employee status',
    enum: EmployeeStatus,
    example: EmployeeStatus.ACTIVE,
  })
  status: EmployeeStatus;

  /**
   * Hire date
   * @example "2023-01-15"
   */
  @ApiProperty({
    description: 'Hire date',
    example: '2023-01-15',
    nullable: true,
  })
  hireDate: Date | null;

  /**
   * Termination date
   * @example null
   */
  @ApiProperty({
    description: 'Termination date',
    example: null,
    nullable: true,
  })
  terminationDate: Date | null;

  /**
   * Termination reason
   * @example null
   */
  @ApiProperty({
    description: 'Termination reason',
    example: null,
    nullable: true,
  })
  terminationReason: string | null;

  /**
   * Probation end date
   * @example "2023-04-15"
   */
  @ApiProperty({
    description: 'Probation end date',
    example: '2023-04-15',
    nullable: true,
  })
  probationEndDate: Date | null;

  /**
   * Marital status
   * @example "single"
   */
  @ApiProperty({
    description: 'Marital status',
    enum: MaritalStatus,
    example: MaritalStatus.SINGLE,
    nullable: true,
  })
  maritalStatus: MaritalStatus | null;

  /**
   * Number of dependents
   * @example 0
   */
  @ApiProperty({
    description: 'Number of dependents',
    example: 0,
    nullable: true,
  })
  numberOfDependents: number | null;

  /**
   * Emergency contact name
   * @example "John Doe"
   */
  @ApiProperty({
    description: 'Emergency contact name',
    example: 'John Doe',
    nullable: true,
  })
  emergencyContactName: string | null;

  /**
   * Emergency contact phone
   * @example "0123456789"
   */
  @ApiProperty({
    description: 'Emergency contact phone',
    example: '0123456789',
    nullable: true,
  })
  emergencyContactPhone: string | null;

  /**
   * Emergency contact relationship
   * @example "Parent"
   */
  @ApiProperty({
    description: 'Emergency contact relationship',
    example: 'Parent',
    nullable: true,
  })
  emergencyContactRelationship: string | null;

  /**
   * Notes
   * @example "Excellent team player"
   */
  @ApiProperty({
    description: 'Notes',
    example: 'Excellent team player',
    nullable: true,
  })
  notes: string | null;

  /**
   * Created at timestamp
   * @example 1672531200000
   */
  @ApiProperty({
    description: 'Created at timestamp',
    example: 1672531200000,
    nullable: true,
  })
  createdAt: number | null;

  /**
   * Updated at timestamp
   * @example 1672531200000
   */
  @ApiProperty({
    description: 'Updated at timestamp',
    example: 1672531200000,
    nullable: true,
  })
  updatedAt: number | null;
}
