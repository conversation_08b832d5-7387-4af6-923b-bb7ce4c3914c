import { Injectable, Logger } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';
import { AppException, ErrorCode } from '@/common';

// Define TokenType enum
export enum TokenType {
  ACCESS = 'ACCESS',
  REFRESH = 'REFRESH',
  OTP = 'OTP',
  VERIFY = 'VERIFY',
  TWO_FA = 'TWO_FA',
  CHANGE_PASSWORD = 'CHANGE_PASSWORD',
  FORGOT_PASSWORD = 'FORGOT_PASSWORD',
}

export interface JwtPayload {
  id: number;
  sub: number; // Subject (usually user ID or employee ID)
  username?: string; // Username (optional)
  permissions?: string[]; // User permissions (optional)
  typeToken?: TokenType; // Token type (optional for backward compatibility)
  tenantId?: number | string; // C<PERSON> thể là chuỗi từ JWT
  domain?: string | null;
  type: 'SYSTEM_ADMIN' | 'COMPANY_ADMIN' | 'EMPLOYEE'; // Loại người dùng
}

@Injectable()
export class JwtUtilService {
  private readonly logger = new Logger(JwtUtilService.name);
  constructor(
    private readonly jwtService: JwtService,
    private readonly configService: ConfigService,
  ) {}

  /**
   * Generates a JWT token with the provided payload.
   * @param payload - The JWT payload containing all required fields
   * @param tokenType - The type of token to generate (ACCESS, REFRESH, etc.)
   * @param expiryTimeConfig - Configuration key for expiry time (defaults based on token type)
   * @returns Object containing the signed token and its expiration time in seconds
   */
  generateToken(
    payload: Partial<JwtPayload>,
    tokenType: TokenType = TokenType.ACCESS,
    expiryTimeConfig?: string,
  ): { token: string; expiresInSeconds: number } {
    // Ensure required fields are present
    if (
      !payload.id ||
      !payload.sub ||
      payload.tenantId === undefined ||
      payload.tenantId === null ||
      !payload.type
    ) {
      this.logger.error('Missing required fields in token payload');
      throw new Error('Missing required fields in token payload');
    }

    // Create full payload with all fields
    const fullPayload: JwtPayload = {
      id: payload.id,
      sub: payload.sub,
      tenantId: payload.tenantId,
      type: payload.type,
      typeToken: tokenType,
      // Optional fields
      username: payload.username,
      permissions: payload.permissions,
      domain: payload.domain,
    };

    // Determine which expiry time config to use based on token type
    let configKey: string;
    if (expiryTimeConfig) {
      configKey = expiryTimeConfig;
    } else {
      configKey =
        tokenType === TokenType.REFRESH
          ? 'JWT_REFRESH_TOKEN_EXPIRATION_TIME'
          : 'JWT_ACCESS_TOKEN_EXPIRATION_TIME';
    }

    // Get expiry time from config
    const expiryTime = this.configService.get<string>(
      configKey,
      tokenType === TokenType.REFRESH ? '7d' : '1d',
    );
    const expiresInSeconds = this.parseExpiryTime(expiryTime);

    // Sign the token
    const token = this.jwtService.sign(fullPayload, {
      secret: this.configService.get<string>('JWT_SECRET'),
      expiresIn: expiryTime,
    });

    return { token, expiresInSeconds };
  }

  /**
   * Phân tích chuỗi thời gian hết hạn (như '7d', '24h', '30m') thành số giây
   * @param expiryTime Chuỗi thời gian hết hạn
   * @returns Số giây
   */
  private parseExpiryTime(expiryTime: string): number {
    const match = expiryTime.match(/^(\d+)([dhms])$/);
    if (!match) {
      this.logger.warn(
        `Invalid expiry time format: ${expiryTime}, using default 7 days`,
      );
      return 7 * 24 * 60 * 60; // 7 days in seconds as default
    }

    const value = parseInt(match[1], 10);
    const unit = match[2];

    switch (unit) {
      case 'd':
        return value * 24 * 60 * 60; // days to seconds
      case 'h':
        return value * 60 * 60; // hours to seconds
      case 'm':
        return value * 60; // minutes to seconds
      case 's':
        return value; // seconds
      default:
        return 7 * 24 * 60 * 60; // default: 7 days
    }
  }

  /**
   * Verifies a token's signature and expiration.
   * @param token - The JWT to verify.
   * @param expectedType - Optional: The expected TokenType.
   * @returns The decoded payload if valid and matches expected type.
   */
  verifyToken(token: string, expectedType?: TokenType): JwtPayload {
    try {
      // Thử xác thực token với secret key
      try {
        const payload = this.jwtService.verify(token, {
          secret: this.configService.get<string>('JWT_SECRET'),
        });

        // Chỉ kiểm tra typeToken nếu expectedType được cung cấp và payload có trường typeToken
        if (
          expectedType &&
          payload.typeToken &&
          payload.typeToken !== expectedType
        ) {
          throw new Error(
            `Invalid token type. Expected ${expectedType}, got ${payload.typeToken}`,
          );
        }

        // Đảm bảo trường typeToken tồn tại
        if (!payload.typeToken) {
          this.logger.warn(
            'Token payload missing typeToken field. Treating as ACCESS token.',
          );
          payload.typeToken = TokenType.ACCESS;
        }

        // Đảm bảo các trường bắt buộc tồn tại
        this.validateRequiredFields(payload);

        return payload as JwtPayload;
      } catch (verifyError) {
        // Nếu xác thực thất bại và đang ở môi trường phát triển, giải mã token mà không xác thực
        if (this.configService.get<string>('NODE_ENV') === 'development') {
          this.logger.warn(
            'DEVELOPMENT MODE: Bypassing token signature verification',
          );
          const payload = this.jwtService.decode(token);

          if (!payload) {
            throw new Error('Invalid token format');
          }

          // Đảm bảo trường typeToken tồn tại
          if (!payload['typeToken']) {
            this.logger.warn(
              'Token payload missing typeToken field. Treating as ACCESS token.',
            );
            payload['typeToken'] = TokenType.ACCESS;
          }

          // Đảm bảo các trường bắt buộc tồn tại
          this.validateRequiredFields(payload as JwtPayload);

          return payload as JwtPayload;
        } else {
          // Ở môi trường production, tiếp tục ném lỗi
          throw verifyError;
        }
      }
    } catch (error) {
      this.logger.error('Error verifying token:', error.message);
      throw new AppException(
        ErrorCode.TOKEN_INVALID_OR_EXPIRED,
        'Invalid token: ' + error.message,
      );
    }
  }

  /**
   * Validates that all required fields are present in the payload
   * @param payload - The JWT payload to validate
   * @throws Error if any required field is missing
   */
  private validateRequiredFields(payload: any): void {
    // Check required fields
    if (!payload.id) {
      this.logger.warn('Token payload missing id field');
      throw new Error('Token payload missing id field');
    }

    if (!payload.sub) {
      this.logger.warn('Token payload missing sub field');
      throw new Error('Token payload missing sub field');
    }

    if (payload.tenantId === undefined || payload.tenantId === null) {
      this.logger.warn('Token payload missing tenantId field');
      throw new Error('Token payload missing tenantId field');
    }

    if (!payload.type) {
      this.logger.warn('Token payload missing type field');
      throw new Error('Token payload missing type field');
    }
  }

  /**
   * Verifies a token and checks if it matches the expected type and user type.
   * @param token - The JWT string to verify.
   * @param expectedType - The expected TokenType.
   * @param expectedUserType - Optional: The expected user type.
   * @returns The decoded JwtPayload if valid.
   * @throws {AppException} If token is invalid, wrong type, expired, or wrong user type.
   */
  verifyTokenWithType(
    token: string,
    expectedType: TokenType,
    expectedUserType?: 'SYSTEM_ADMIN' | 'COMPANY_ADMIN' | 'EMPLOYEE',
  ): JwtPayload {
    try {
      // Verify token and get payload
      const payload = this.verifyToken(token, expectedType);

      // Kiểm tra thời gian hết hạn
      const currentTime = Math.floor(Date.now() / 1000);
      if (payload['exp'] && payload['exp'] < currentTime) {
        this.logger.warn(
          'Token has expired. Expiration time:',
          new Date(payload['exp'] * 1000),
        );
        throw new AppException(
          ErrorCode.TOKEN_INVALID_OR_EXPIRED,
          'Token has expired.',
        );
      }

      // Kiểm tra loại người dùng nếu được chỉ định
      if (expectedUserType && payload.type !== expectedUserType) {
        this.logger.warn(
          `Access denied. Expected user type ${expectedUserType} but got: ${payload.type}`,
        );
        throw new AppException(
          ErrorCode.UNAUTHORIZED_ACCESS,
          `Access denied. ${expectedUserType} privileges required.`,
        );
      }

      return payload;
    } catch (error) {
      // Nếu lỗi đã được xử lý trong hàm, chuyển tiếp lỗi
      if (error instanceof AppException) {
        throw error;
      }

      // Xử lý các lỗi khác
      this.logger.error('Error verifying token with type:', error.message);
      throw new AppException(
        ErrorCode.TOKEN_INVALID_OR_EXPIRED,
        'Invalid token: ' + error.message,
      );
    }
  }

  /**
   * Decodes a token without verifying its signature or expiration.
   * @param token - The JWT to decode.
   * @returns The decoded payload or null if invalid format.
   */
  decodeToken(token: string): any {
    // Decode token without type assertion
    return this.jwtService.decode(token);
  }
}
