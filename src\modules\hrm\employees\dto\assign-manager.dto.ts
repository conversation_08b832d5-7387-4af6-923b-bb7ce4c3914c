import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsInt } from 'class-validator';
import { Type } from 'class-transformer';

/**
 * DTO for assigning manager to employee
 */
export class AssignManagerDto {
  /**
   * Manager ID to assign
   * @example 2
   */
  @ApiProperty({ description: 'Manager ID to assign', example: 2 })
  @IsNotEmpty()
  @IsInt()
  @Type(() => Number)
  managerId: number;
}
