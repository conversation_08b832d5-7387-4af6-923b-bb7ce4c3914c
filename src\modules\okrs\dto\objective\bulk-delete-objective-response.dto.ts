import { ApiProperty } from '@nestjs/swagger';

/**
 * DTO cho kết quả xóa nhiều mục tiêu
 */
export class BulkDeleteObjectiveResponseDto {
  /**
   * Tổng số mục tiêu được yêu cầu xóa
   */
  @ApiProperty({
    description: 'Tổng số mục tiêu được yêu cầu xóa',
    example: 5,
    type: Number,
  })
  totalRequested: number;

  /**
   * Số mục tiêu đã xóa thành công
   */
  @ApiProperty({
    description: 'Số mục tiêu đã xóa thành công',
    example: 3,
    type: Number,
  })
  successCount: number;

  /**
   * Số mục tiêu không thể xóa
   */
  @ApiProperty({
    description: 'Số mục tiêu không thể xóa',
    example: 2,
    type: Number,
  })
  failureCount: number;

  /**
   * <PERSON>h sách ID các mục tiêu đã xóa thành công
   */
  @ApiProperty({
    description: '<PERSON>h sách ID các mục tiêu đã xóa thành công',
    example: [1, 3, 5],
    type: [Number],
  })
  deletedObjectiveIds: number[];

  /**
   * Tổng số key-result đã xóa
   */
  @ApiProperty({
    description: 'Tổng số key-result đã xóa cùng với các mục tiêu',
    example: 8,
    type: Number,
  })
  deletedKeyResultsCount: number;

  /**
   * Danh sách ID các mục tiêu không thể xóa và lý do
   */
  @ApiProperty({
    description: 'Danh sách ID các mục tiêu không thể xóa và lý do',
    example: [
      { id: 2, reason: 'Không tìm thấy mục tiêu với ID 2' },
      { id: 4, reason: 'Mục tiêu đang được sử dụng' },
    ],
    type: 'array',
    items: {
      type: 'object',
      properties: {
        id: { type: 'number' },
        reason: { type: 'string' },
      },
    },
  })
  failures: Array<{ id: number; reason: string }>;
}
