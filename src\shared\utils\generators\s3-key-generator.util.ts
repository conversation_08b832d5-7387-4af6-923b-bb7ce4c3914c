import { v4 as uuidv4 } from 'uuid';

/**
 * Enum định nghĩa các thư mục phân loại file
 */
export enum CategoryFolderEnum {
  KNOWLEDGE_FILES = 'knowledge_files',
  MODEL = 'models',
  PROFILE = 'profile',
  AGENT = 'agents',
  CHAT = 'chats',
  MEDIA = 'media',
  AFFILIATE = 'affiliates',
  STRATEGY = 'strategies',
  FUNCTION = 'functions',
  PRODUCT = 'products',
  USER_PRODUCT = 'user_products',
  BUSINESSES = 'businesses',
  EMPLOYEE_AVATAR_FOLDER = 'employee_avatars',
  DOCUMENT = 'DOCUMENT',
  IMAGE = 'IMAGE',
  VIDEO = 'VIDEO',
  OTHER = 'OTHER',
}

/**
 * Cấu hình tạo S3 key
 */
export interface S3KeyOptions {
  /** Thư mục gốc */
  baseFolder: string;
  /** Tiền tố (tùy chọn) */
  prefix?: string;
  /** <PERSON><PERSON><PERSON> mục phân loại tùy chỉnh (nếu không muốn dùng phân loại tự động) */
  categoryFolder?: CategoryFolderEnum;
  /** Tên file (tùy chọn) */
  fileName?: string;
  /** Sử dụng thư mục phân theo thời gian (năm/tháng) */
  useTimeFolder?: boolean;
}

/**
 * Tạo S3 key với cấu hình linh hoạt
 * @param options Cấu hình tạo key
 * @returns S3 key được tạo theo cấu hình
 */
export const generateS3Key = (options: S3KeyOptions): string => {
  const { baseFolder, prefix, categoryFolder, fileName, useTimeFolder } =
    options;

  const timestamp = Date.now();
  const uuid = uuidv4();

  // Tạo cấu trúc thư mục - Luôn có thư mục gốc và thư mục phân loại
  let folderStructure = `${baseFolder}/${categoryFolder}`;

  // Thêm thư mục phân theo thời gian nếu cần
  if (useTimeFolder) {
    const date = new Date();
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    folderStructure += `/${year}/${month}`;
  }

  // Thêm tiền tố nếu có
  if (prefix) {
    folderStructure += `/${prefix}`;
  }

  // Xử lý tên file
  let fileNamePart = '';
  if (fileName) {
    // Loại bỏ các ký tự đặc biệt, khoảng trắng và thay thế bằng dấu gạch dưới
    const sanitizedFileName = fileName.replace(/[^\w.-]/g, '_');
    const extension = sanitizedFileName.includes('.')
      ? sanitizedFileName.split('.').pop()
      : '';

    if (extension) {
      const baseName = sanitizedFileName.substring(
        0,
        sanitizedFileName.lastIndexOf('.'),
      );
      fileNamePart = `${baseName}-${timestamp}-${uuid}.${extension}`;
    } else {
      fileNamePart = `${sanitizedFileName}-${timestamp}-${uuid}`;
    }
  } else {
    fileNamePart = `${timestamp}-${uuid}`;
  }

  // Tạo key cuối cùng
  return `${folderStructure}/${fileNamePart}`;
};
