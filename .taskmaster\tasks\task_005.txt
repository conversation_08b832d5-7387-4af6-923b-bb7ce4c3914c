# Task ID: 5
# Title: Update OKRs Module - Services Layer
# Status: done
# Dependencies: 2, 3, 4
# Priority: high
# Description: Update OKRs services to pass tenantId to repository methods
# Details:
Update service classes:
- ObjectiveService: Add tenantId parameter to all methods
- OkrCycleService: Update to pass tenantId to repository
- KeyResultService: Add tenantId handling
- Remove @WithTenant decorators and use explicit tenantId passing
- Update business logic to validate tenantId consistency

# Test Strategy:
Integration tests to verify service layer properly handles tenantId
