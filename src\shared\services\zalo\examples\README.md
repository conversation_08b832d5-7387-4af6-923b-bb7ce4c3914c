# Zalo Integration Examples

<PERSON><PERSON><PERSON> mục này chứa các ví dụ về cách sử dụng Zalo Integration Services trong ứng dụng. Các file trong thư mục này chỉ là ví dụ và không được sử dụng trực tiếp trong ứng dụng.

## C<PERSON>u trúc

- `zalo-controller.example.ts`: <PERSON><PERSON> dụ về controller để xử lý các request liên quan đến Zalo
- `zalo-entity.example.ts`: Ví dụ về các entity để lưu trữ dữ liệu Zalo
- `zalo-repository.example.ts`: Ví dụ về các repository để tương tác với database
- `zalo-service.example.ts`: Ví dụ về service để xử lý logic nghiệp vụ

## Cách sử dụng

Để sử dụng Zalo Integration Services trong ứng dụng của bạn, bạn cần:

1. Tạo các entity tương tự như trong `zalo-entity.example.ts`
2. Tạo các repository tương tự như trong `zalo-repository.example.ts`
3. Tạo các service tương tự như trong `zalo-service.example.ts`
4. Tạo các controller tương tự như trong `zalo-controller.example.ts`

## Tích hợp với Agent

Để tích hợp Zalo với Agent trong hệ thống, bạn cần:

1. Tạo một service để xử lý việc chuyển tiếp tin nhắn giữa Zalo và Agent
2. Sử dụng `ZaloAgentService` để gửi tin nhắn từ Agent đến người dùng Zalo
3. Xử lý webhook từ Zalo và chuyển tiếp tin nhắn đến Agent

## Tích hợp với ZNS

Để sử dụng Zalo Notification Service (ZNS), bạn cần:

1. Tạo các template ZNS trên Zalo Developer Portal
2. Lưu thông tin template vào database
3. Sử dụng `ZaloZnsService` để gửi tin nhắn ZNS

## Xử lý webhook

Để xử lý webhook từ Zalo, bạn cần:

1. Tạo một controller để nhận các sự kiện webhook từ Zalo
2. Sử dụng `ZaloWebhookService` để xác thực và xử lý các sự kiện webhook
3. Chuyển tiếp tin nhắn đến Agent hoặc xử lý các sự kiện khác tùy theo nhu cầu

## Cấu hình môi trường

Để sử dụng Zalo Integration Services, bạn cần thêm các biến môi trường sau vào file `.env`:

```env
# Zalo API Configuration
ZALO_APP_ID=your_zalo_app_id           # ID của ứng dụng Zalo
ZALO_APP_SECRET=your_zalo_app_secret   # Secret của ứng dụng Zalo
ZALO_WEBHOOK_SECRET=your_webhook_secret # Secret để xác thực webhook
ZALO_WEBHOOK_URL=https://your-domain.com/api/zalo/webhook # URL webhook
```

Bạn có thể lấy `ZALO_APP_ID` và `ZALO_APP_SECRET` từ trang [Zalo Developers](https://developers.zalo.me/) sau khi đăng ký và tạo ứng dụng.

## Lưu ý

- Các ví dụ trong thư mục này chỉ là hướng dẫn và có thể cần điều chỉnh để phù hợp với ứng dụng của bạn
- Đảm bảo cấu hình các biến môi trường cần thiết trong file `.env`
- Đảm bảo đăng ký các module và service trong module của bạn
- Đảm bảo đã tạo các bảng cần thiết trong database bằng cách chạy script SQL trong file `zalo_database.sql`
