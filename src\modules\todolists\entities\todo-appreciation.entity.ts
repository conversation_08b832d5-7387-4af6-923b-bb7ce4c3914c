import { Entity, PrimaryGeneratedColumn, Column } from 'typeorm';

/**
 * Entity representing appreciations for completed todos
 */
@Entity('todo_appreciations')
export class TodoAppreciation {
  /**
   * Unique identifier for the appreciation record
   */
  @PrimaryGeneratedColumn()
  id: number;

  /**
   * ID of the todo being appreciated
   */
  @Column({ name: 'todo_id', type: 'integer', nullable: true })
  todoId: number | null;

  /**
   * ID of the user giving the appreciation
   */
  @Column({ name: 'user_id', type: 'integer', nullable: true })
  userId: number | null;

  /**
   * Appreciation note/evaluation
   */
  @Column({ type: 'text', nullable: true })
  note: string | null;

  /**
   * Creation timestamp (in milliseconds)
   */
  @Column({ name: 'created_at', type: 'bigint', nullable: true })
  createdAt: number | null;

  /**
   * ID of the company/organization that owns this record
   */
  @Column({ name: 'tenant_id', type: 'integer', nullable: true })
  tenantId: number | null;
}
