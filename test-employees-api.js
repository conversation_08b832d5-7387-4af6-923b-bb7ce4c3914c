// Test script để kiểm tra API employees và chức năng search
const https = require('https');
const http = require('http');

// Cấu hình test
const BASE_URL = 'http://localhost:3001';
const TEST_TOKEN = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6MSwidGVuYW50SWQiOjEsInR5cGUiOiJVU0VSIiwiaWF0IjoxNzMzMzU5NzM5LCJleHAiOjE3MzMzNjMzMzl9.test'; // Token test

// Function để gọi API
function makeRequest(path, method = 'GET', data = null) {
  return new Promise((resolve, reject) => {
    const url = new URL(BASE_URL + path);
    const options = {
      hostname: url.hostname,
      port: url.port,
      path: url.pathname + url.search,
      method: method,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${TEST_TOKEN}`
      }
    };

    if (data) {
      const postData = JSON.stringify(data);
      options.headers['Content-Length'] = Buffer.byteLength(postData);
    }

    const req = http.request(options, (res) => {
      let responseData = '';
      
      res.on('data', (chunk) => {
        responseData += chunk;
      });
      
      res.on('end', () => {
        try {
          const parsedData = JSON.parse(responseData);
          resolve({
            status: res.statusCode,
            headers: res.headers,
            data: parsedData
          });
        } catch (e) {
          resolve({
            status: res.statusCode,
            headers: res.headers,
            data: responseData
          });
        }
      });
    });

    req.on('error', (err) => {
      reject(err);
    });

    if (data) {
      req.write(JSON.stringify(data));
    }
    
    req.end();
  });
}

// Test cases
async function runTests() {
  console.log('🚀 Bắt đầu test API Employees...\n');

  // Test 1: GET employees without search
  console.log('📋 Test 1: GET /v1/api/hrm/employees (no search)');
  try {
    const response = await makeRequest('/v1/api/hrm/employees?page=1&limit=5');
    console.log(`Status: ${response.status}`);
    if (response.status === 200) {
      console.log(`✅ Success! Found ${response.data.data?.meta?.totalItems || 0} employees`);
      if (response.data.data?.items?.length > 0) {
        console.log('Sample employee:', {
          id: response.data.data.items[0].id,
          employeeCode: response.data.data.items[0].employeeCode,
          employeeName: response.data.data.items[0].employeeName
        });
      }
    } else {
      console.log('❌ Failed:', response.data);
    }
  } catch (error) {
    console.log('❌ Error:', error.message);
  }
  console.log('');

  // Test 2: Search by employee code
  console.log('🔍 Test 2: Search by employee code (REDAI)');
  try {
    const response = await makeRequest('/v1/api/hrm/employees?page=1&limit=5&search=REDAI');
    console.log(`Status: ${response.status}`);
    if (response.status === 200) {
      console.log(`✅ Search results: ${response.data.data?.meta?.totalItems || 0} employees found`);
      if (response.data.data?.items?.length > 0) {
        response.data.data.items.forEach((emp, index) => {
          console.log(`  ${index + 1}. ${emp.employeeCode} - ${emp.employeeName}`);
        });
      }
    } else {
      console.log('❌ Failed:', response.data);
    }
  } catch (error) {
    console.log('❌ Error:', error.message);
  }
  console.log('');

  // Test 3: Search by employee name
  console.log('🔍 Test 3: Search by employee name');
  try {
    const response = await makeRequest('/v1/api/hrm/employees?page=1&limit=5&search=Nguyễn');
    console.log(`Status: ${response.status}`);
    if (response.status === 200) {
      console.log(`✅ Search results: ${response.data.data?.meta?.totalItems || 0} employees found`);
      if (response.data.data?.items?.length > 0) {
        response.data.data.items.forEach((emp, index) => {
          console.log(`  ${index + 1}. ${emp.employeeCode} - ${emp.employeeName}`);
        });
      }
    } else {
      console.log('❌ Failed:', response.data);
    }
  } catch (error) {
    console.log('❌ Error:', error.message);
  }
  console.log('');

  // Test 4: Search with no results
  console.log('🔍 Test 4: Search with no results');
  try {
    const response = await makeRequest('/v1/api/hrm/employees?page=1&limit=5&search=NOTFOUND123');
    console.log(`Status: ${response.status}`);
    if (response.status === 200) {
      console.log(`✅ Search results: ${response.data.data?.meta?.totalItems || 0} employees found`);
    } else {
      console.log('❌ Failed:', response.data);
    }
  } catch (error) {
    console.log('❌ Error:', error.message);
  }
  console.log('');

  // Test 5: Test pagination
  console.log('📄 Test 5: Test pagination');
  try {
    const response = await makeRequest('/v1/api/hrm/employees?page=2&limit=3');
    console.log(`Status: ${response.status}`);
    if (response.status === 200) {
      const meta = response.data.data?.meta;
      console.log(`✅ Pagination: Page ${meta?.currentPage}/${meta?.totalPages}, Total: ${meta?.totalItems}`);
    } else {
      console.log('❌ Failed:', response.data);
    }
  } catch (error) {
    console.log('❌ Error:', error.message);
  }

  console.log('\n🏁 Test hoàn thành!');
}

// Chạy tests
runTests().catch(console.error);
