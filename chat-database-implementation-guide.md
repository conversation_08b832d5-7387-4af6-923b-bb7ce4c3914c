# Chat Database Integration - Implementation Guide

## 🚀 Bắt đầu triển khai

### Prerequisites
- NestJS project đã setup
- PostgreSQL database
- Redis cache
- OpenAI API key
- TypeORM configured

## 📁 C<PERSON>u trú<PERSON> thư mục

```
src/modules/chat/
├── services/
│   ├── database-schema.service.ts      # Schema introspection
│   ├── text-to-sql.service.ts          # Natural language to SQL
│   ├── safe-sql-executor.service.ts    # Safe SQL execution
│   ├── query-cache.service.ts          # Query caching
│   ├── query-router.service.ts         # Query routing logic
│   ├── query-validator.service.ts      # SQL validation
│   └── audit-logger.service.ts         # Audit logging
├── tools/
│   ├── hr-tools.ts                     # HR domain tools
│   ├── sales-tools.ts                  # Sales domain tools
│   └── finance-tools.ts                # Finance domain tools
├── guards/
│   └── sql-security.guard.ts           # SQL security guard
├── interfaces/
│   ├── schema.interface.ts             # Schema interfaces
│   ├── sql-generation.interface.ts     # SQL generation interfaces
│   └── cache.interface.ts              # Cache interfaces
└── dto/
    ├── schema.dto.ts                   # Schema DTOs
    ├── sql-query.dto.ts                # SQL query DTOs
    └── sql-result.dto.ts               # SQL result DTOs
```

## 🔧 Core Services Implementation

### 1. DatabaseSchemaService

```typescript
@Injectable()
export class DatabaseSchemaService {
  private schemaCache = new Map<string, TableSchema>();

  async getTableSchema(tableName: string): Promise<TableSchema> {
    // Introspect table structure
    // Cache results in Redis
    // Return business-friendly schema
  }

  async getBusinessConcepts(): Promise<BusinessConcept[]> {
    // Map database tables to business concepts
    // Return AI-friendly descriptions
  }

  async getRelationships(tableName: string): Promise<Relationship[]> {
    // Discover foreign key relationships
    // Return relationship metadata
  }
}
```

### 2. TextToSqlService

```typescript
@Injectable()
export class TextToSqlService {
  async generateSQL(
    query: string, 
    tenantId: number,
    context?: QueryContext
  ): Promise<GeneratedSQL> {
    // 1. Analyze natural language query
    // 2. Get relevant schema information
    // 3. Generate SQL with OpenAI
    // 4. Validate and sanitize SQL
    // 5. Inject tenant isolation
    // 6. Return safe SQL
  }

  private async analyzeQuery(query: string): Promise<QueryAnalysis> {
    // Use OpenAI to understand intent
    // Extract entities and relationships
    // Determine query complexity
  }
}
```

### 3. SafeSqlExecutorService

```typescript
@Injectable()
export class SafeSqlExecutorService {
  async executeQuery(
    sql: string,
    tenantId: number,
    options: ExecutionOptions = {}
  ): Promise<QueryResult> {
    // 1. Validate SQL safety
    // 2. Inject tenant filters
    // 3. Apply resource limits
    // 4. Execute with timeout
    // 5. Format results
    // 6. Log for audit
  }

  private validateSqlSafety(sql: string): ValidationResult {
    // Check for dangerous operations
    // Validate parameterization
    // Ensure read-only operations
  }
}
```

## 🛡️ Security Implementation

### SQL Security Guard

```typescript
@Injectable()
export class SqlSecurityGuard implements CanActivate {
  canActivate(context: ExecutionContext): boolean {
    // Validate user permissions
    // Check rate limits
    // Audit access attempts
    return true;
  }
}
```

### Query Validator

```typescript
@Injectable()
export class QueryValidatorService {
  validateQuery(sql: string): ValidationResult {
    // Parse SQL AST
    // Check for dangerous patterns
    // Validate table access permissions
    // Ensure tenant isolation
  }
}
```

## 🎯 Business Tools Examples

### HR Tools

```typescript
export class HRTools {
  @Tool({
    name: 'get_employee_count',
    description: 'Get total number of employees'
  })
  async getEmployeeCount(tenantId: number): Promise<number> {
    const sql = `
      SELECT COUNT(*) as count 
      FROM users 
      WHERE tenant_id = $1 AND deleted_at IS NULL
    `;
    const result = await this.sqlExecutor.executeQuery(sql, tenantId);
    return result.data[0].count;
  }

  @Tool({
    name: 'get_department_employees',
    description: 'Get employees by department'
  })
  async getDepartmentEmployees(
    departmentName: string, 
    tenantId: number
  ): Promise<Employee[]> {
    // Implementation
  }
}
```

## 📊 Query Examples

### Supported Query Types

```typescript
// 1. Simple counting
"Có bao nhiêu nhân viên trong công ty?"
→ SELECT COUNT(*) FROM users WHERE tenant_id = ? AND deleted_at IS NULL

// 2. Filtering with conditions  
"Danh sách nhân viên phòng IT"
→ SELECT * FROM users WHERE department = 'IT' AND tenant_id = ?

// 3. Aggregation queries
"Tổng doanh thu tháng này"
→ SELECT SUM(amount) FROM orders 
  WHERE EXTRACT(MONTH FROM created_at) = EXTRACT(MONTH FROM NOW())
  AND tenant_id = ?

// 4. Complex joins
"Top 5 khách hàng mua nhiều nhất"
→ SELECT c.name, SUM(o.amount) as total
  FROM customers c 
  JOIN orders o ON c.id = o.customer_id
  WHERE c.tenant_id = ? AND o.tenant_id = ?
  GROUP BY c.id, c.name
  ORDER BY total DESC
  LIMIT 5
```

## 🔄 Integration Flow

```typescript
// AI Orchestrator Integration
export class AIOrchestatorService {
  async processMessage(message: ChatMessage): Promise<AIResponse> {
    // 1. Detect if query needs database access
    const needsDatabase = await this.detectDatabaseIntent(message.content);
    
    if (needsDatabase) {
      // 2. Route to database query handler
      return await this.handleDatabaseQuery(message);
    }
    
    // 3. Fallback to regular RAG processing
    return await this.ragService.processQuery(message.content);
  }

  private async handleDatabaseQuery(message: ChatMessage): Promise<AIResponse> {
    // 1. Generate SQL from natural language
    const sql = await this.textToSql.generateSQL(message.content, message.tenantId);
    
    // 2. Execute SQL safely
    const result = await this.sqlExecutor.executeQuery(sql.query, message.tenantId);
    
    // 3. Format result for chat response
    const formattedResponse = await this.formatDatabaseResult(result, message.content);
    
    return {
      text: formattedResponse,
      confidence: 0.9,
      intent: 'database_query',
      context: { sql: sql.query, rowCount: result.rowCount }
    };
  }
}
```

## 🧪 Testing Strategy

### Unit Tests
```typescript
describe('TextToSqlService', () => {
  it('should generate safe SQL for employee count query', async () => {
    const query = "Có bao nhiêu nhân viên?";
    const result = await textToSqlService.generateSQL(query, 1);
    
    expect(result.sql).toContain('SELECT COUNT(*)');
    expect(result.sql).toContain('tenant_id = $1');
    expect(result.parameters).toEqual([1]);
  });
});
```

### Integration Tests
```typescript
describe('Database Integration', () => {
  it('should execute employee query end-to-end', async () => {
    const message = { content: "Có bao nhiêu nhân viên?", tenantId: 1 };
    const response = await aiOrchestrator.processMessage(message);
    
    expect(response.text).toMatch(/\d+ nhân viên/);
    expect(response.intent).toBe('database_query');
  });
});
```

## 📈 Performance Considerations

### Caching Strategy
- Schema metadata: 1 hour TTL
- Query results: 5 minutes TTL  
- Business concepts: 24 hours TTL

### Query Optimization
- Automatic LIMIT injection
- Index usage hints
- Query complexity scoring
- Resource usage monitoring

## 🚨 Error Handling

```typescript
export class DatabaseQueryError extends Error {
  constructor(
    message: string,
    public readonly code: string,
    public readonly sql?: string
  ) {
    super(message);
  }
}

// Error types:
// - SQL_GENERATION_FAILED
// - SQL_VALIDATION_FAILED  
// - SQL_EXECUTION_FAILED
// - TENANT_ISOLATION_VIOLATION
// - QUERY_TIMEOUT
// - RESOURCE_LIMIT_EXCEEDED
```

---

**Tài liệu này cung cấp framework chi tiết để implement database integration cho chat system.**
