/**
 * Enum định nghĩa các kích thước file thông dụng
 */
export enum FileSizeEnum {
  ONE_KB = 1024,
  TEN_KB = 10 * 1024,
  HUNDRED_KB = 100 * 1024,
  HALF_MB = 512 * 1024,
  ONE_MB = 1024 * 1024,
  TWO_MB = 2 * 1024 * 1024,
  FIVE_MB = 5 * 1024 * 1024,
  TEN_MB = 10 * 1024 * 1024,
  TWENTY_MB = 20 * 1024 * 1024,
  FIFTY_MB = 50 * 1024 * 1024,
  HUNDRED_MB = 100 * 1024 * 1024,
  TWO_HUNDRED_MB = 200 * 1024 * 1024,
  FIVE_HUNDRED_MB = 500 * 1024 * 1024,
  ONE_GB = 1024 * 1024 * 1024,
}

/**
 * Object tiện ích để làm việc với FileSizeEnum
 */
export const FileSize = {
  /**
   * Lấy giá trị số của một kích thước file
   * @param key Tên của kích thước file
   * @returns Gi<PERSON> trị số tương ứng
   */
  getValue(key: keyof typeof FileSizeEnum): number {
    return FileSizeEnum[key];
  },
};

/**
 * Hàm helper để lấy giá trị số của một FileSizeEnum
 */
export function getFileSize(size: FileSizeEnum): number {
  return size;
}
