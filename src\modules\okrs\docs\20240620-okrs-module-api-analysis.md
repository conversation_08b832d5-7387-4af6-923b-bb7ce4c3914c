# Ph<PERSON> tích Module OKRs

## 1. T<PERSON>ng quan

Module OKRs (Objectives and Key Results) là một phần quan trọng của hệ thống, cho phép quản lý mục tiêu và kết quả chính của tổ chức, phòng ban và cá nhân. Module này cung cấp các API để tạo, c<PERSON><PERSON>, theo dõi và báo cáo về OKRs.

## 2. Cấu trúc dữ liệu

### 2.1. Các Entity

Module OKRs bao gồm các entity chính sau:

#### 2.1.1. Objective (Mục tiêu)

Entity `Objective` đại diện cho các mục tiêu trong hệ thống OKR:

```typescript
@Entity('objectives')
@Check(`"start_date" <= "end_date"`)
export class Objective {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ type: 'varchar', length: 255, nullable: false })
  title: string;

  @Column({ type: 'text', nullable: true })
  description: string | null;

  @Column({ name: 'owner_id', type: 'integer', nullable: false })
  ownerId: number;

  @Column({ name: 'department_id', type: 'integer', nullable: true })
  departmentId: number | null;

  @Column({ name: 'parent_id', type: 'integer', nullable: true })
  parentId: number | null;

  @Column({ name: 'cycle_id', type: 'integer', nullable: false })
  cycleId: number;

  @Column({ name: 'type', type: 'enum', enum: ObjectiveType, nullable: false })
  type: ObjectiveType;

  @Column({ type: 'integer', default: 0, nullable: true })
  progress: number | null;

  @Column({ type: 'varchar', length: 50, nullable: true })
  status: string | null;

  @Column({ name: 'start_date', type: 'date', nullable: false })
  startDate: Date;

  @Column({ name: 'end_date', type: 'date', nullable: false })
  endDate: Date;

  @Column({ name: 'created_by', type: 'integer', nullable: false })
  createdBy: number;

  @Column({ name: 'created_at', type: 'bigint', nullable: true })
  createdAt: number | null;

  @Column({ name: 'updated_at', type: 'bigint', nullable: true })
  updatedAt: number | null;

  @Column({ name: 'tenant_id', type: 'integer', nullable: true })
  tenantId: number | null;
}
```

#### 2.1.2. KeyResult (Kết quả chính)

Entity `KeyResult` đại diện cho các kết quả chính đo lường sự thành công của mục tiêu:

```typescript
@Entity('key_results')
@Check(`"target_value" <> "start_value"`)
export class KeyResult {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: 'objective_id', type: 'integer', nullable: true })
  objectiveId: number | null;

  @Column({ type: 'varchar', length: 255, nullable: false })
  title: string;

  @Column({ type: 'text', nullable: true })
  description: string | null;

  @Column({ name: 'target_value', type: 'numeric', precision: 18, scale: 2, nullable: false })
  targetValue: number;

  @Column({ name: 'current_value', type: 'numeric', precision: 18, scale: 2, default: 0, nullable: true })
  currentValue: number | null;

  @Column({ name: 'start_value', type: 'numeric', precision: 18, scale: 2, default: 0, nullable: true })
  startValue: number | null;

  @Column({ type: 'varchar', length: 50, nullable: true })
  unit: string | null;

  @Column({ type: 'varchar', length: 50, default: 'number', nullable: true })
  format: string | null;

  @Column({ type: 'integer', default: 0, nullable: true })
  progress: number | null;

  @Column({ type: 'varchar', length: 50, default: 'active', nullable: true })
  status: string | null;

  @Column({ name: 'measurement_method', type: 'varchar', length: 100, nullable: true })
  measurementMethod: string | null;

  @Column({ type: 'integer', default: 100, nullable: true })
  weight: number | null;

  @Column({ name: 'check_in_frequency', type: 'varchar', length: 50, default: 'weekly', nullable: true })
  checkInFrequency: string | null;

  @Column({ name: 'created_at', type: 'bigint', nullable: true })
  createdAt: number | null;

  @Column({ name: 'updated_at', type: 'bigint', nullable: true })
  updatedAt: number | null;

  @Column({ name: 'tenant_id', type: 'integer', nullable: true })
  tenantId: number | null;
}
```

#### 2.1.3. KeyResultUpdate (Cập nhật kết quả chính)

Entity `KeyResultUpdate` đại diện cho lịch sử cập nhật giá trị của kết quả chính:

```typescript
@Entity('key_result_updates')
export class KeyResultUpdate {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: 'key_result_id', type: 'integer', nullable: true })
  keyResultId: number | null;

  @Column({ name: 'previous_value', type: 'numeric', precision: 18, scale: 2, nullable: false })
  previousValue: number;

  @Column({ name: 'new_value', type: 'numeric', precision: 18, scale: 2, nullable: false })
  newValue: number;

  @Column({ name: 'update_by', type: 'integer', nullable: false })
  updateBy: number;

  @Column({ name: 'confidence_level', type: 'integer', nullable: true })
  confidenceLevel: number | null;

  @Column({ type: 'text', nullable: true })
  notes: string | null;

  @Column({ name: 'check_in_type', type: 'varchar', length: 50, default: 'manual', nullable: true })
  checkInType: string | null;

  @Column({ name: 'updated_date', type: 'bigint', nullable: true })
  updatedDate: number | null;

  @Column({ name: 'tenant_id', type: 'integer', nullable: true })
  tenantId: number | null;
}
```

#### 2.1.4. OkrCycle (Chu kỳ OKR)

Entity `OkrCycle` đại diện cho các chu kỳ OKR (quý, năm):

```typescript
@Entity('okr_cycles')
export class OkrCycle {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ type: 'varchar', length: 255, nullable: false })
  name: string;

  @Column({ name: 'start_date', type: 'date', nullable: false })
  startDate: Date;

  @Column({ name: 'end_date', type: 'date', nullable: false })
  endDate: Date;

  @Column({ type: 'enum', enum: OkrCycleStatus, nullable: true })
  status: OkrCycleStatus | null;

  @Column({ type: 'text', nullable: true })
  description: string | null;

  @Column({ name: 'created_by', type: 'integer', nullable: true })
  createdBy: number | null;

  @Column({ name: 'created_at', type: 'bigint', nullable: true })
  createdAt: number | null;

  @Column({ name: 'tenant_id', type: 'integer', nullable: true })
  tenantId: number | null;
}
```

#### 2.1.5. OkrType (Loại OKR)

Entity `OkrType` đại diện cho các loại OKR (công ty, phòng ban, cá nhân):

```typescript
@Entity('okr_types')
export class OkrType {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ type: 'varchar', length: 100, nullable: false })
  name: string;

  @Column({ type: 'text', nullable: true })
  description: string | null;

  @Column({ type: 'integer', nullable: false })
  level: number;

  @Column({ name: 'tenant_id', type: 'integer', nullable: true })
  tenantId: number | null;
}
```

### 2.2. Các Enum

Module OKRs sử dụng các enum sau:

#### 2.2.1. ObjectiveType

```typescript
export enum ObjectiveType {
  COMPANY = 'COMPANY',
  DEPARTMENT = 'DEPARTMENT',
  INDIVIDUAL = 'INDIVIDUAL',
}
```

#### 2.2.2. OkrCycleStatus

```typescript
export enum OkrCycleStatus {
  ACTIVE = 'active',
  CLOSED = 'closed',
  PLANNING = 'planning',
}
```

## 3. Đề xuất API

Dựa trên cấu trúc dữ liệu, module OKRs nên cung cấp các API sau:

### 3.1. API Quản lý Chu kỳ OKR

#### 3.1.1. Tạo chu kỳ OKR mới
- **Endpoint**: `POST /api/okrs/cycles`
- **Quyền**: Admin, Manager
- **Request Body**: Thông tin chu kỳ (tên, ngày bắt đầu, ngày kết thúc, mô tả)
- **Response**: Thông tin chu kỳ đã tạo

#### 3.1.2. Lấy danh sách chu kỳ OKR
- **Endpoint**: `GET /api/okrs/cycles`
- **Quyền**: Tất cả người dùng
- **Query Params**: Phân trang, sắp xếp, lọc theo trạng thái
- **Response**: Danh sách chu kỳ OKR

#### 3.1.3. Lấy chi tiết chu kỳ OKR
- **Endpoint**: `GET /api/okrs/cycles/:id`
- **Quyền**: Tất cả người dùng
- **Response**: Chi tiết chu kỳ OKR

#### 3.1.4. Cập nhật chu kỳ OKR
- **Endpoint**: `PUT /api/okrs/cycles/:id`
- **Quyền**: Admin, Manager
- **Request Body**: Thông tin cập nhật
- **Response**: Thông tin chu kỳ đã cập nhật

#### 3.1.5. Xóa chu kỳ OKR
- **Endpoint**: `DELETE /api/okrs/cycles/:id`
- **Quyền**: Admin
- **Response**: Thông báo xóa thành công

### 3.2. API Quản lý Mục tiêu (Objectives)

#### 3.2.1. Tạo mục tiêu mới
- **Endpoint**: `POST /api/okrs/objectives`
- **Quyền**: Tùy theo loại mục tiêu (COMPANY, DEPARTMENT, INDIVIDUAL)
- **Request Body**: Thông tin mục tiêu
- **Response**: Thông tin mục tiêu đã tạo

#### 3.2.2. Lấy danh sách mục tiêu
- **Endpoint**: `GET /api/okrs/objectives`
- **Quyền**: Tất cả người dùng
- **Query Params**: Phân trang, sắp xếp, lọc theo chu kỳ, loại, phòng ban, người sở hữu
- **Response**: Danh sách mục tiêu

#### 3.2.3. Lấy chi tiết mục tiêu
- **Endpoint**: `GET /api/okrs/objectives/:id`
- **Quyền**: Tất cả người dùng
- **Response**: Chi tiết mục tiêu và danh sách kết quả chính

#### 3.2.4. Cập nhật mục tiêu
- **Endpoint**: `PUT /api/okrs/objectives/:id`
- **Quyền**: Người sở hữu, Admin, Manager
- **Request Body**: Thông tin cập nhật
- **Response**: Thông tin mục tiêu đã cập nhật

#### 3.2.5. Xóa mục tiêu
- **Endpoint**: `DELETE /api/okrs/objectives/:id`
- **Quyền**: Người sở hữu, Admin, Manager
- **Response**: Thông báo xóa thành công

### 3.3. API Quản lý Kết quả chính (Key Results)

#### 3.3.1. Tạo kết quả chính mới
- **Endpoint**: `POST /api/okrs/key-results`
- **Quyền**: Người sở hữu mục tiêu, Admin, Manager
- **Request Body**: Thông tin kết quả chính
- **Response**: Thông tin kết quả chính đã tạo

#### 3.3.2. Lấy danh sách kết quả chính theo mục tiêu
- **Endpoint**: `GET /api/okrs/objectives/:objectiveId/key-results`
- **Quyền**: Tất cả người dùng
- **Response**: Danh sách kết quả chính của mục tiêu

#### 3.3.3. Lấy chi tiết kết quả chính
- **Endpoint**: `GET /api/okrs/key-results/:id`
- **Quyền**: Tất cả người dùng
- **Response**: Chi tiết kết quả chính và lịch sử cập nhật

#### 3.3.4. Cập nhật kết quả chính
- **Endpoint**: `PUT /api/okrs/key-results/:id`
- **Quyền**: Người sở hữu mục tiêu, Admin, Manager
- **Request Body**: Thông tin cập nhật
- **Response**: Thông tin kết quả chính đã cập nhật

#### 3.3.5. Xóa kết quả chính
- **Endpoint**: `DELETE /api/okrs/key-results/:id`
- **Quyền**: Người sở hữu mục tiêu, Admin, Manager
- **Response**: Thông báo xóa thành công

### 3.4. API Cập nhật giá trị kết quả chính

#### 3.4.1. Tạo bản ghi cập nhật mới
- **Endpoint**: `POST /api/okrs/key-results/:id/updates`
- **Quyền**: Người sở hữu mục tiêu, Admin, Manager
- **Request Body**: Giá trị mới, mức độ tự tin, ghi chú
- **Response**: Thông tin cập nhật đã tạo

#### 3.4.2. Lấy lịch sử cập nhật của kết quả chính
- **Endpoint**: `GET /api/okrs/key-results/:id/updates`
- **Quyền**: Tất cả người dùng
- **Query Params**: Phân trang, sắp xếp theo thời gian
- **Response**: Danh sách cập nhật

## 4. Đề xuất DTO

### 4.1. DTO cho Chu kỳ OKR

#### 4.1.1. CreateOkrCycleDto
```typescript
export class CreateOkrCycleDto {
  @ApiProperty({
    description: 'Tên chu kỳ OKR',
    example: 'Q1-2025',
  })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty({
    description: 'Ngày bắt đầu chu kỳ',
    example: '2025-01-01',
  })
  @IsDateString()
  startDate: string;

  @ApiProperty({
    description: 'Ngày kết thúc chu kỳ',
    example: '2025-03-31',
  })
  @IsDateString()
  endDate: string;

  @ApiProperty({
    description: 'Trạng thái chu kỳ',
    enum: OkrCycleStatus,
    example: OkrCycleStatus.PLANNING,
    required: false,
  })
  @IsEnum(OkrCycleStatus)
  @IsOptional()
  status?: OkrCycleStatus;

  @ApiProperty({
    description: 'Mô tả chu kỳ',
    example: 'Chu kỳ Q1 năm 2025',
    required: false,
  })
  @IsString()
  @IsOptional()
  description?: string;
}
```

#### 4.1.2. UpdateOkrCycleDto
```typescript
export class UpdateOkrCycleDto {
  @ApiProperty({
    description: 'Tên chu kỳ OKR',
    example: 'Q1-2025',
    required: false,
  })
  @IsString()
  @IsOptional()
  name?: string;

  @ApiProperty({
    description: 'Ngày bắt đầu chu kỳ',
    example: '2025-01-01',
    required: false,
  })
  @IsDateString()
  @IsOptional()
  startDate?: string;

  @ApiProperty({
    description: 'Ngày kết thúc chu kỳ',
    example: '2025-03-31',
    required: false,
  })
  @IsDateString()
  @IsOptional()
  endDate?: string;

  @ApiProperty({
    description: 'Trạng thái chu kỳ',
    enum: OkrCycleStatus,
    example: OkrCycleStatus.ACTIVE,
    required: false,
  })
  @IsEnum(OkrCycleStatus)
  @IsOptional()
  status?: OkrCycleStatus;

  @ApiProperty({
    description: 'Mô tả chu kỳ',
    example: 'Chu kỳ Q1 năm 2025 đã cập nhật',
    required: false,
  })
  @IsString()
  @IsOptional()
  description?: string;
}
```

#### 4.1.3. OkrCycleResponseDto
```typescript
export class OkrCycleResponseDto {
  @ApiProperty({
    description: 'ID của chu kỳ OKR',
    example: 1,
  })
  id: number;

  @ApiProperty({
    description: 'Tên chu kỳ OKR',
    example: 'Q1-2025',
  })
  name: string;

  @ApiProperty({
    description: 'Ngày bắt đầu chu kỳ',
    example: '2025-01-01',
  })
  startDate: string;

  @ApiProperty({
    description: 'Ngày kết thúc chu kỳ',
    example: '2025-03-31',
  })
  endDate: string;

  @ApiProperty({
    description: 'Trạng thái chu kỳ',
    enum: OkrCycleStatus,
    example: OkrCycleStatus.ACTIVE,
  })
  status: OkrCycleStatus;

  @ApiProperty({
    description: 'Mô tả chu kỳ',
    example: 'Chu kỳ Q1 năm 2025',
    nullable: true,
  })
  description: string | null;

  @ApiProperty({
    description: 'Thời gian tạo (timestamp)',
    example: 1672531200000,
  })
  createdAt: number;
}
```

### 4.2. DTO cho Mục tiêu (Objectives)

#### 4.2.1. CreateObjectiveDto
```typescript
export class CreateObjectiveDto {
  @ApiProperty({
    description: 'Tiêu đề mục tiêu',
    example: 'Tăng doanh thu 20%',
  })
  @IsString()
  @IsNotEmpty()
  title: string;

  @ApiProperty({
    description: 'Mô tả chi tiết mục tiêu',
    example: 'Tăng doanh thu 20% so với quý trước thông qua các chiến dịch marketing mới',
    required: false,
  })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiProperty({
    description: 'ID của người chịu trách nhiệm',
    example: 1,
  })
  @IsNumber()
  ownerId: number;

  @ApiProperty({
    description: 'ID của phòng ban (nếu có)',
    example: 2,
    required: false,
  })
  @IsNumber()
  @IsOptional()
  departmentId?: number;

  @ApiProperty({
    description: 'ID của mục tiêu cha (nếu là mục tiêu con)',
    example: 3,
    required: false,
  })
  @IsNumber()
  @IsOptional()
  parentId?: number;

  @ApiProperty({
    description: 'ID của chu kỳ OKR',
    example: 1,
  })
  @IsNumber()
  cycleId: number;

  @ApiProperty({
    description: 'Loại mục tiêu',
    enum: ObjectiveType,
    example: ObjectiveType.COMPANY,
  })
  @IsEnum(ObjectiveType)
  type: ObjectiveType;

  @ApiProperty({
    description: 'Ngày bắt đầu mục tiêu',
    example: '2025-01-01',
  })
  @IsDateString()
  startDate: string;

  @ApiProperty({
    description: 'Ngày kết thúc mục tiêu',
    example: '2025-03-31',
  })
  @IsDateString()
  endDate: string;
}
```

#### 4.2.2. UpdateObjectiveDto
```typescript
export class UpdateObjectiveDto {
  @ApiProperty({
    description: 'Tiêu đề mục tiêu',
    example: 'Tăng doanh thu 25%',
    required: false,
  })
  @IsString()
  @IsOptional()
  title?: string;

  @ApiProperty({
    description: 'Mô tả chi tiết mục tiêu',
    example: 'Tăng doanh thu 25% so với quý trước thông qua các chiến dịch marketing mới',
    required: false,
  })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiProperty({
    description: 'ID của người chịu trách nhiệm',
    example: 1,
    required: false,
  })
  @IsNumber()
  @IsOptional()
  ownerId?: number;

  @ApiProperty({
    description: 'ID của phòng ban (nếu có)',
    example: 2,
    required: false,
  })
  @IsNumber()
  @IsOptional()
  departmentId?: number;

  @ApiProperty({
    description: 'ID của mục tiêu cha (nếu là mục tiêu con)',
    example: 3,
    required: false,
  })
  @IsNumber()
  @IsOptional()
  parentId?: number;

  @ApiProperty({
    description: 'Trạng thái mục tiêu',
    example: 'active',
    required: false,
  })
  @IsString()
  @IsOptional()
  status?: string;

  @ApiProperty({
    description: 'Ngày bắt đầu mục tiêu',
    example: '2025-01-01',
    required: false,
  })
  @IsDateString()
  @IsOptional()
  startDate?: string;

  @ApiProperty({
    description: 'Ngày kết thúc mục tiêu',
    example: '2025-03-31',
    required: false,
  })
  @IsDateString()
  @IsOptional()
  endDate?: string;
}
```

#### 4.2.3. ObjectiveResponseDto
```typescript
export class ObjectiveResponseDto {
  @ApiProperty({
    description: 'ID của mục tiêu',
    example: 1,
  })
  id: number;

  @ApiProperty({
    description: 'Tiêu đề mục tiêu',
    example: 'Tăng doanh thu 20%',
  })
  title: string;

  @ApiProperty({
    description: 'Mô tả chi tiết mục tiêu',
    example: 'Tăng doanh thu 20% so với quý trước thông qua các chiến dịch marketing mới',
    nullable: true,
  })
  description: string | null;

  @ApiProperty({
    description: 'ID của người chịu trách nhiệm',
    example: 1,
  })
  ownerId: number;

  @ApiProperty({
    description: 'ID của phòng ban (nếu có)',
    example: 2,
    nullable: true,
  })
  departmentId: number | null;

  @ApiProperty({
    description: 'ID của mục tiêu cha (nếu là mục tiêu con)',
    example: 3,
    nullable: true,
  })
  parentId: number | null;

  @ApiProperty({
    description: 'ID của chu kỳ OKR',
    example: 1,
  })
  cycleId: number;

  @ApiProperty({
    description: 'Loại mục tiêu',
    enum: ObjectiveType,
    example: ObjectiveType.COMPANY,
  })
  type: ObjectiveType;

  @ApiProperty({
    description: 'Tiến độ hoàn thành mục tiêu (phần trăm)',
    example: 75,
    nullable: true,
  })
  progress: number | null;

  @ApiProperty({
    description: 'Trạng thái mục tiêu',
    example: 'active',
    nullable: true,
  })
  status: string | null;

  @ApiProperty({
    description: 'Ngày bắt đầu mục tiêu',
    example: '2025-01-01',
  })
  startDate: string;

  @ApiProperty({
    description: 'Ngày kết thúc mục tiêu',
    example: '2025-03-31',
  })
  endDate: string;

  @ApiProperty({
    description: 'ID của người tạo mục tiêu',
    example: 1,
  })
  createdBy: number;

  @ApiProperty({
    description: 'Thời gian tạo (timestamp)',
    example: 1672531200000,
    nullable: true,
  })
  createdAt: number | null;

  @ApiProperty({
    description: 'Thời gian cập nhật gần nhất (timestamp)',
    example: 1672617600000,
    nullable: true,
  })
  updatedAt: number | null;
}
```

### 4.3. DTO cho Kết quả chính (Key Results)

#### 4.3.1. CreateKeyResultDto
```typescript
export class CreateKeyResultDto {
  @ApiProperty({
    description: 'ID của mục tiêu',
    example: 1,
  })
  @IsNumber()
  objectiveId: number;

  @ApiProperty({
    description: 'Tiêu đề kết quả chính',
    example: 'Tăng số lượng khách hàng mới lên 1000',
  })
  @IsString()
  @IsNotEmpty()
  title: string;

  @ApiProperty({
    description: 'Mô tả chi tiết kết quả chính',
    example: 'Tăng số lượng khách hàng mới thông qua các chiến dịch marketing trên mạng xã hội',
    required: false,
  })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiProperty({
    description: 'Giá trị mục tiêu',
    example: 1000,
  })
  @IsNumber()
  targetValue: number;

  @ApiProperty({
    description: 'Giá trị ban đầu',
    example: 500,
    required: false,
  })
  @IsNumber()
  @IsOptional()
  startValue?: number;

  @ApiProperty({
    description: 'Đơn vị đo lường',
    example: 'khách hàng',
    required: false,
  })
  @IsString()
  @IsOptional()
  unit?: string;

  @ApiProperty({
    description: 'Định dạng hiển thị',
    example: 'number',
    required: false,
  })
  @IsString()
  @IsOptional()
  format?: string;

  @ApiProperty({
    description: 'Phương pháp đo lường',
    example: 'Số lượng đăng ký mới',
    required: false,
  })
  @IsString()
  @IsOptional()
  measurementMethod?: string;

  @ApiProperty({
    description: 'Trọng số trong mục tiêu (0-100)',
    example: 30,
    required: false,
  })
  @IsNumber()
  @Min(0)
  @Max(100)
  @IsOptional()
  weight?: number;

  @ApiProperty({
    description: 'Tần suất cập nhật',
    example: 'weekly',
    required: false,
  })
  @IsString()
  @IsOptional()
  checkInFrequency?: string;
}
```

#### 4.3.2. UpdateKeyResultDto
```typescript
export class UpdateKeyResultDto {
  @ApiProperty({
    description: 'Tiêu đề kết quả chính',
    example: 'Tăng số lượng khách hàng mới lên 1200',
    required: false,
  })
  @IsString()
  @IsOptional()
  title?: string;

  @ApiProperty({
    description: 'Mô tả chi tiết kết quả chính',
    example: 'Tăng số lượng khách hàng mới thông qua các chiến dịch marketing trên mạng xã hội và email',
    required: false,
  })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiProperty({
    description: 'Giá trị mục tiêu',
    example: 1200,
    required: false,
  })
  @IsNumber()
  @IsOptional()
  targetValue?: number;

  @ApiProperty({
    description: 'Giá trị hiện tại',
    example: 800,
    required: false,
  })
  @IsNumber()
  @IsOptional()
  currentValue?: number;

  @ApiProperty({
    description: 'Đơn vị đo lường',
    example: 'khách hàng',
    required: false,
  })
  @IsString()
  @IsOptional()
  unit?: string;

  @ApiProperty({
    description: 'Định dạng hiển thị',
    example: 'number',
    required: false,
  })
  @IsString()
  @IsOptional()
  format?: string;

  @ApiProperty({
    description: 'Trạng thái',
    example: 'active',
    required: false,
  })
  @IsString()
  @IsOptional()
  status?: string;

  @ApiProperty({
    description: 'Phương pháp đo lường',
    example: 'Số lượng đăng ký mới',
    required: false,
  })
  @IsString()
  @IsOptional()
  measurementMethod?: string;

  @ApiProperty({
    description: 'Trọng số trong mục tiêu (0-100)',
    example: 40,
    required: false,
  })
  @IsNumber()
  @Min(0)
  @Max(100)
  @IsOptional()
  weight?: number;

  @ApiProperty({
    description: 'Tần suất cập nhật',
    example: 'daily',
    required: false,
  })
  @IsString()
  @IsOptional()
  checkInFrequency?: string;
}
```

#### 4.3.3. KeyResultResponseDto
```typescript
export class KeyResultResponseDto {
  @ApiProperty({
    description: 'ID của kết quả chính',
    example: 1,
  })
  id: number;

  @ApiProperty({
    description: 'ID của mục tiêu',
    example: 1,
    nullable: true,
  })
  objectiveId: number | null;

  @ApiProperty({
    description: 'Tiêu đề kết quả chính',
    example: 'Tăng số lượng khách hàng mới lên 1000',
  })
  title: string;

  @ApiProperty({
    description: 'Mô tả chi tiết kết quả chính',
    example: 'Tăng số lượng khách hàng mới thông qua các chiến dịch marketing trên mạng xã hội',
    nullable: true,
  })
  description: string | null;

  @ApiProperty({
    description: 'Giá trị mục tiêu',
    example: 1000,
  })
  targetValue: number;

  @ApiProperty({
    description: 'Giá trị hiện tại',
    example: 800,
    nullable: true,
  })
  currentValue: number | null;

  @ApiProperty({
    description: 'Giá trị ban đầu',
    example: 500,
    nullable: true,
  })
  startValue: number | null;

  @ApiProperty({
    description: 'Đơn vị đo lường',
    example: 'khách hàng',
    nullable: true,
  })
  unit: string | null;

  @ApiProperty({
    description: 'Định dạng hiển thị',
    example: 'number',
    nullable: true,
  })
  format: string | null;

  @ApiProperty({
    description: 'Tiến độ hoàn thành (phần trăm)',
    example: 60,
    nullable: true,
  })
  progress: number | null;

  @ApiProperty({
    description: 'Trạng thái',
    example: 'active',
    nullable: true,
  })
  status: string | null;

  @ApiProperty({
    description: 'Phương pháp đo lường',
    example: 'Số lượng đăng ký mới',
    nullable: true,
  })
  measurementMethod: string | null;

  @ApiProperty({
    description: 'Trọng số trong mục tiêu (0-100)',
    example: 30,
    nullable: true,
  })
  weight: number | null;

  @ApiProperty({
    description: 'Tần suất cập nhật',
    example: 'weekly',
    nullable: true,
  })
  checkInFrequency: string | null;

  @ApiProperty({
    description: 'Thời gian tạo (timestamp)',
    example: 1672531200000,
    nullable: true,
  })
  createdAt: number | null;

  @ApiProperty({
    description: 'Thời gian cập nhật gần nhất (timestamp)',
    example: 1672617600000,
    nullable: true,
  })
  updatedAt: number | null;
}
```

### 4.4. DTO cho Cập nhật Kết quả chính

#### 4.4.1. CreateKeyResultUpdateDto
```typescript
export class CreateKeyResultUpdateDto {
  @ApiProperty({
    description: 'Giá trị mới',
    example: 800,
  })
  @IsNumber()
  newValue: number;

  @ApiProperty({
    description: 'Mức độ tự tin (1-5)',
    example: 4,
    required: false,
  })
  @IsNumber()
  @Min(1)
  @Max(5)
  @IsOptional()
  confidenceLevel?: number;

  @ApiProperty({
    description: 'Ghi chú cập nhật',
    example: 'Đã hoàn thành chiến dịch marketing trên Facebook, tăng thêm 300 khách hàng mới',
    required: false,
  })
  @IsString()
  @IsOptional()
  notes?: string;

  @ApiProperty({
    description: 'Loại check-in',
    example: 'manual',
    required: false,
  })
  @IsString()
  @IsOptional()
  checkInType?: string;
}
```

#### 4.4.2. KeyResultUpdateResponseDto
```typescript
export class KeyResultUpdateResponseDto {
  @ApiProperty({
    description: 'ID của bản ghi cập nhật',
    example: 1,
  })
  id: number;

  @ApiProperty({
    description: 'ID của kết quả chính',
    example: 1,
    nullable: true,
  })
  keyResultId: number | null;

  @ApiProperty({
    description: 'Giá trị trước khi cập nhật',
    example: 500,
  })
  previousValue: number;

  @ApiProperty({
    description: 'Giá trị mới sau khi cập nhật',
    example: 800,
  })
  newValue: number;

  @ApiProperty({
    description: 'ID của người cập nhật',
    example: 1,
  })
  updateBy: number;

  @ApiProperty({
    description: 'Mức độ tự tin (1-5)',
    example: 4,
    nullable: true,
  })
  confidenceLevel: number | null;

  @ApiProperty({
    description: 'Ghi chú cập nhật',
    example: 'Đã hoàn thành chiến dịch marketing trên Facebook, tăng thêm 300 khách hàng mới',
    nullable: true,
  })
  notes: string | null;

  @ApiProperty({
    description: 'Loại check-in',
    example: 'manual',
    nullable: true,
  })
  checkInType: string | null;

  @ApiProperty({
    description: 'Thời gian cập nhật (timestamp)',
    example: 1672617600000,
    nullable: true,
  })
  updatedDate: number | null;
}
```

## 5. Đề xuất Repositories

### 5.1. OkrCycleRepository
```typescript
@Injectable()
export class OkrCycleRepository {
  private readonly logger = new Logger(OkrCycleRepository.name);

  constructor(
    @InjectRepository(OkrCycle)
    private readonly repository: Repository<OkrCycle>,
  ) {}

  async findAll(tenantId: number): Promise<OkrCycle[]> {
    return this.repository.find({
      where: { tenantId },
      order: { startDate: 'DESC' },
    });
  }

  async findById(id: number, tenantId: number): Promise<OkrCycle | null> {
    return this.repository.findOne({
      where: { id, tenantId },
    });
  }

  async create(data: Partial<OkrCycle>): Promise<OkrCycle> {
    const cycle = this.repository.create(data);
    return this.repository.save(cycle);
  }

  async update(id: number, tenantId: number, data: Partial<OkrCycle>): Promise<OkrCycle | null> {
    await this.repository.update({ id, tenantId }, data);
    return this.findById(id, tenantId);
  }

  async delete(id: number, tenantId: number): Promise<boolean> {
    const result = await this.repository.delete({ id, tenantId });
    return result.affected > 0;
  }

  async findActive(tenantId: number): Promise<OkrCycle | null> {
    return this.repository.findOne({
      where: { tenantId, status: OkrCycleStatus.ACTIVE },
    });
  }
}
```

## 6. Kết luận

Module OKRs là một phần quan trọng của hệ thống, cho phép quản lý mục tiêu và kết quả chính của tổ chức, phòng ban và cá nhân. Dựa trên phân tích các entity và mối quan hệ giữa chúng, chúng tôi đã đề xuất một tập hợp các API và DTO để triển khai module này.

Các API được thiết kế để hỗ trợ đầy đủ các chức năng CRUD cho tất cả các entity chính, cũng như các chức năng đặc biệt như cập nhật giá trị kết quả chính và theo dõi tiến độ. Các DTO được thiết kế để đảm bảo tính nhất quán và dễ sử dụng cho cả frontend và backend.

Để triển khai module này, cần thực hiện các bước sau:

1. Tạo các entity và enum đã được mô tả
2. Tạo các DTO cho các API
3. Tạo các repository để tương tác với database
4. Tạo các service để xử lý logic nghiệp vụ
5. Tạo các controller để xử lý các request từ client
6. Tạo các guard để kiểm soát quyền truy cập
7. Viết unit test và integration test

Với việc triển khai module OKRs, hệ thống sẽ có thể hỗ trợ đầy đủ quy trình quản lý mục tiêu và kết quả chính, giúp tổ chức theo dõi và đánh giá hiệu quả công việc một cách hiệu quả.