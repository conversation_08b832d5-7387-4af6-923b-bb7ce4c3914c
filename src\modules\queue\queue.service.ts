import { Injectable, Logger } from '@nestjs/common';
import { InjectQueue } from '@nestjs/bull';
import { Queue, Job } from 'bull';
import { QueueName, DEFAULT_JOB_OPTIONS } from './queue.constants';

/**
 * Service xử lý các thao tác với queue
 */
@Injectable()
export class QueueService {
  private readonly logger = new Logger(QueueService.name);

  constructor(
    @InjectQueue(QueueName.EMAIL) private readonly emailQueue: Queue,
    @InjectQueue(QueueName.SMS) private readonly smsQueue: Queue,
    @InjectQueue(QueueName.NOTIFICATION)
    private readonly notificationQueue: Queue,
    @InjectQueue(QueueName.DATA_PROCESS)
    private readonly dataProcessQueue: Queue,
    @InjectQueue(QueueName.TODO) private readonly todoQueue: Queue,
    @InjectQueue(QueueName.PROJECT) private readonly projectQueue: Queue,
    @InjectQueue(QueueName.REPORT) private readonly reportQueue: Queue,
    @InjectQueue(QueueName.EXPORT) private readonly exportQueue: Queue,
    @InjectQueue(QueueName.IMPORT) private readonly importQueue: Queue,
    @InjectQueue(QueueName.SYNC) private readonly syncQueue: Queue,
  ) {
    // Đăng ký các event listener cho tất cả các queue
    this.registerQueueEvents();
  }

  /**
   * Đăng ký các event listener cho queue
   */
  private registerQueueEvents() {
    const queues = [
      this.emailQueue,
      this.smsQueue,
      this.notificationQueue,
      this.dataProcessQueue,
      this.todoQueue,
      this.projectQueue,
      this.reportQueue,
      this.exportQueue,
      this.importQueue,
      this.syncQueue,
    ];

    queues.forEach((queue) => {
      queue.on('completed', (job: Job) => {
        this.logger.log(`Job ${job.id} completed in queue ${queue.name}`);
      });

      queue.on('failed', (job: Job, error: Error) => {
        this.logger.error(
          `Job ${job.id} failed in queue ${queue.name}: ${error.message}`,
          error.stack,
        );
      });

      queue.on('stalled', (job: Job) => {
        this.logger.warn(`Job ${job.id} stalled in queue ${queue.name}`);
      });

      queue.on('error', (error: Error) => {
        this.logger.error(
          `Error in queue ${queue.name}: ${error.message}`,
          error.stack,
        );
      });
    });
  }

  /**
   * Thêm job vào queue
   * @param queueName Tên queue
   * @param data Dữ liệu của job
   * @param options Tùy chọn cho job
   * @returns Job đã được tạo
   */
  async addJob<T>(
    queueName: QueueName,
    data: T,
    options: any = {},
  ): Promise<Job<T>> {
    const queue = this.getQueue(queueName);
    const jobOptions = { ...DEFAULT_JOB_OPTIONS, ...options };

    try {
      const job = await queue.add(data, jobOptions);
      this.logger.log(
        `Added job ${job.id} to queue ${queueName} with data:`,
        data,
      );
      return job;
    } catch (error) {
      this.logger.error(
        `Failed to add job to queue ${queueName}: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Lấy queue theo tên
   * @param queueName Tên queue
   * @returns Queue instance
   */
  private getQueue(queueName: QueueName): Queue {
    switch (queueName) {
      case QueueName.EMAIL:
        return this.emailQueue;
      case QueueName.SMS:
        return this.smsQueue;
      case QueueName.NOTIFICATION:
        return this.notificationQueue;
      case QueueName.DATA_PROCESS:
        return this.dataProcessQueue;
      case QueueName.TODO:
        return this.todoQueue;
      case QueueName.PROJECT:
        return this.projectQueue;
      case QueueName.REPORT:
        return this.reportQueue;
      case QueueName.EXPORT:
        return this.exportQueue;
      case QueueName.IMPORT:
        return this.importQueue;
      case QueueName.SYNC:
        return this.syncQueue;
      default:
        throw new Error(`Queue ${queueName} not found`);
    }
  }

  /**
   * Xóa job khỏi queue
   * @param queueName Tên queue
   * @param jobId ID của job
   */
  async removeJob(queueName: QueueName, jobId: string): Promise<void> {
    const queue = this.getQueue(queueName);
    try {
      await queue.removeJobs(jobId);
      this.logger.log(`Removed job ${jobId} from queue ${queueName}`);
    } catch (error) {
      this.logger.error(
        `Failed to remove job ${jobId} from queue ${queueName}: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Xóa tất cả job trong queue
   * @param queueName Tên queue
   */
  async cleanQueue(queueName: QueueName): Promise<void> {
    const queue = this.getQueue(queueName);
    try {
      await queue.empty();
      this.logger.log(`Cleaned queue ${queueName}`);
    } catch (error) {
      this.logger.error(
        `Failed to clean queue ${queueName}: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Tạm dừng queue
   * @param queueName Tên queue
   */
  async pauseQueue(queueName: QueueName): Promise<void> {
    const queue = this.getQueue(queueName);
    try {
      await queue.pause();
      this.logger.log(`Paused queue ${queueName}`);
    } catch (error) {
      this.logger.error(
        `Failed to pause queue ${queueName}: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Tiếp tục queue
   * @param queueName Tên queue
   */
  async resumeQueue(queueName: QueueName): Promise<void> {
    const queue = this.getQueue(queueName);
    try {
      await queue.resume();
      this.logger.log(`Resumed queue ${queueName}`);
    } catch (error) {
      this.logger.error(
        `Failed to resume queue ${queueName}: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Lấy thông tin về queue
   * @param queueName Tên queue
   * @returns Thông tin về queue
   */
  async getQueueInfo(queueName: QueueName): Promise<any> {
    const queue = this.getQueue(queueName);
    try {
      const [
        jobCounts,
        delayedCount,
        waitingCount,
        activeCount,
        completedCount,
        failedCount,
      ] = await Promise.all([
        queue.getJobCounts(),
        queue.getDelayedCount(),
        queue.getWaitingCount(),
        queue.getActiveCount(),
        queue.getCompletedCount(),
        queue.getFailedCount(),
      ]);

      return {
        name: queueName,
        jobCounts,
        delayedCount,
        waitingCount,
        activeCount,
        completedCount,
        failedCount,
      };
    } catch (error) {
      this.logger.error(
        `Failed to get queue info for ${queueName}: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }
}
