import { ApiProperty } from '@nestjs/swagger';
import { IsInt, IsOptional, Min } from 'class-validator';
import { Type } from 'class-transformer';
import { QueryDto } from '@/common/dto/query.dto';

/**
 * DTO cho truy vấn danh sách cộng tác viên của công việc
 */
export class TodoCollaboratorQueryDto extends QueryDto {
  /**
   * Lọc theo ID công việc
   * @example 1
   */
  @ApiProperty({
    description: 'Lọc theo ID công việc',
    example: 1,
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt({ message: 'ID công việc phải là số nguyên' })
  @Min(1, { message: 'ID công việc phải lớn hơn 0' })
  todoId?: number;

  /**
   * Lọc theo ID người dùng
   * @example 1
   */
  @ApiProperty({
    description: 'Lọ<PERSON> theo ID người dùng',
    example: 1,
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt({ message: 'ID người dùng phải là số nguyên' })
  @Min(1, { message: 'ID người dùng phải lớn hơn 0' })
  userId?: number;
}
