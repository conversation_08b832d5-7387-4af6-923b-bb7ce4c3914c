import { ApiProperty } from '@nestjs/swagger';
import { ContractStatus } from '../enum/contract-status.enum';
import { ContractType } from '../enum/contract-type.enum';

/**
 * DTO for contract response
 */
export class ContractResponseDto {
  /**
   * Contract ID
   * @example 1
   */
  @ApiProperty({ description: 'Contract ID', example: 1 })
  id: number;

  /**
   * Contract code
   * @example "CT-2023-001"
   */
  @ApiProperty({ description: 'Contract code', example: 'CT-2023-001' })
  contractCode: string;

  /**
   * Employee ID
   * @example 1
   */
  @ApiProperty({ description: 'Employee ID', example: 1 })
  employeeId: number;

  /**
   * Contract type
   * @example "definite"
   */
  @ApiProperty({
    description: 'Contract type',
    enum: ContractType,
    example: ContractType.DEFINITE,
  })
  contractType: ContractType;

  /**
   * Contract title
   * @example "Employment Contract"
   */
  @ApiProperty({
    description: 'Contract title',
    example: 'Employment Contract',
  })
  title: string;

  /**
   * Contract description
   * @example "Standard employment contract for software engineers"
   */
  @ApiProperty({
    description: 'Contract description',
    example: 'Standard employment contract for software engineers',
    nullable: true,
  })
  description: string | null;

  /**
   * Contract start date
   * @example "2023-01-15"
   */
  @ApiProperty({ description: 'Contract start date', example: '2023-01-15' })
  startDate: Date;

  /**
   * Contract end date
   * @example "2024-01-14"
   */
  @ApiProperty({
    description: 'Contract end date',
    example: '2024-01-14',
    nullable: true,
  })
  endDate: Date | null;

  /**
   * Contract signing date
   * @example "2023-01-10"
   */
  @ApiProperty({
    description: 'Contract signing date',
    example: '2023-01-10',
    nullable: true,
  })
  signingDate: Date | null;

  /**
   * Contract status
   * @example "active"
   */
  @ApiProperty({
    description: 'Contract status',
    enum: ContractStatus,
    example: ContractStatus.ACTIVE,
  })
  status: ContractStatus;

  /**
   * Base salary amount
   * @example 10000000
   */
  @ApiProperty({ description: 'Base salary amount', example: 10000000 })
  baseSalary: number;

  /**
   * Salary currency
   * @example "VND"
   */
  @ApiProperty({ description: 'Salary currency', example: 'VND' })
  currency: string;

  /**
   * Working hours per week
   * @example 40
   */
  @ApiProperty({
    description: 'Working hours per week',
    example: 40,
    nullable: true,
  })
  workingHoursPerWeek: number | null;

  /**
   * Probation period in days
   * @example 60
   */
  @ApiProperty({
    description: 'Probation period in days',
    example: 60,
    nullable: true,
  })
  probationPeriodDays: number | null;

  /**
   * Notice period in days
   * @example 30
   */
  @ApiProperty({
    description: 'Notice period in days',
    example: 30,
    nullable: true,
  })
  noticePeriodDays: number | null;

  /**
   * Contract termination date
   * @example null
   */
  @ApiProperty({
    description: 'Contract termination date',
    example: null,
    nullable: true,
  })
  terminationDate: Date | null;

  /**
   * Reason for termination
   * @example null
   */
  @ApiProperty({
    description: 'Reason for termination',
    example: null,
    nullable: true,
  })
  terminationReason: string | null;

  /**
   * Path to contract document file
   * @example "contracts/2023/CT-2023-001.pdf"
   */
  @ApiProperty({
    description: 'Path to contract document file',
    example: 'contracts/2023/CT-2023-001.pdf',
    nullable: true,
  })
  documentPath: string | null;

  /**
   * Notes about the contract
   * @example "Contract includes special clauses for remote work"
   */
  @ApiProperty({
    description: 'Notes about the contract',
    example: 'Contract includes special clauses for remote work',
    nullable: true,
  })
  notes: string | null;

  /**
   * Created at timestamp
   * @example 1672531200000
   */
  @ApiProperty({
    description: 'Created at timestamp',
    example: 1672531200000,
    nullable: true,
  })
  createdAt: number | null;

  /**
   * Updated at timestamp
   * @example 1672531200000
   */
  @ApiProperty({
    description: 'Updated at timestamp',
    example: 1672531200000,
    nullable: true,
  })
  updatedAt: number | null;
}
