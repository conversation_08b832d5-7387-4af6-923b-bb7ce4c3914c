import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { ChatConversation } from '../entities/chat-conversation.entity';
import { ChatMessage } from '../entities/chat-message.entity';
import { ConversationRepository } from '../repositories/conversation.repository';
import { MessageRepository } from '../repositories/message.repository';
import { AIOrchestatorService } from './ai-orchestrator.service';
import { OpenAiService } from '@/shared/services/ai/openai.service';
import {
  WebappChatMessageDto,
  ChatResponseDto,
  CreateConversationDto,
  ConversationHistoryDto,
  ConversationHistoryResponseDto,
  WebappMessageType,
  ConversationStatus,
} from '../dto/webapp-chat.dto';

/**
 * Service xử lý chat cho webapp với AI agent
 * Quản lý cuộc hội thoại và tin nhắn cho webapp chat
 * Tích hợp với AI Orchestrator để xử lý tin nhắn thông minh
 */
@Injectable()
export class WebappChatService {
  private readonly logger = new Logger(WebappChatService.name);

  constructor(
    // Repository để quản lý cuộc hội thoại
    private readonly conversationRepository: ConversationRepository,
    // Repository để quản lý tin nhắn
    private readonly messageRepository: MessageRepository,
    // AI Orchestrator để xử lý tin nhắn với AI
    private readonly aiOrchestrator: AIOrchestatorService,
    // OpenAI Service để xử lý streaming
    private readonly openAiService: OpenAiService,
  ) {}

  /**
   * Tạo cuộc hội thoại mới cho webapp
   * @param userId ID người dùng
   * @param tenantId ID tenant
   * @param data Dữ liệu tạo cuộc hội thoại
   * @returns Cuộc hội thoại đã tạo
   */
  async createConversation(
    userId: number,
    tenantId: number,
    data: CreateConversationDto,
  ): Promise<ChatConversation> {
    try {
      const conversation = await this.conversationRepository.create(tenantId, {
        facebookUserId: userId.toString(), // Sử dụng userId làm identifier
        pageId: 'webapp', // Đánh dấu đây là cuộc hội thoại từ webapp
        userName: `User ${userId}`,
        userAvatar: null,
        status: ConversationStatus.ACTIVE,
        assignedAgentId: null,
        conversationType: 'webapp',
        language: data.language || 'vi',
        metadata: {
          ...data.metadata,
          source: 'webapp',
          userId,
        },
        lastMessageAt: null,
        createdAt: Date.now(),
        updatedAt: Date.now(),
        tenantId,
      });

      this.logger.log(
        `Created new webapp conversation ${conversation.id} for user ${userId}`,
      );
      return conversation;
    } catch (error) {
      this.logger.error(
        `Failed to create conversation for user ${userId}: ${error.message}`,
      );
      throw error;
    }
  }

  /**
   * Lấy hoặc tạo cuộc hội thoại active cho user
   * @param userId ID người dùng
   * @param tenantId ID tenant
   * @returns Cuộc hội thoại active
   */
  async getOrCreateActiveConversation(
    userId: number,
    tenantId: number,
  ): Promise<ChatConversation> {
    try {
      // Tìm cuộc hội thoại active hiện tại
      let conversation = await this.conversationRepository.findActiveByUser(
        tenantId,
        userId.toString(),
        'webapp',
      );

      if (!conversation) {
        // Tạo cuộc hội thoại mới nếu chưa có
        conversation = await this.createConversation(userId, tenantId, {
          language: 'vi',
        });
      }

      return conversation;
    } catch (error) {
      this.logger.error(
        `Failed to get or create conversation for user ${userId}: ${error.message}`,
      );
      throw error;
    }
  }

  /**
   * Xử lý tin nhắn từ webapp với streaming AI response
   * @param userId ID người dùng gửi tin nhắn
   * @param tenantId ID tenant để đảm bảo isolation
   * @param messageData Dữ liệu tin nhắn từ webapp
   * @param onChunk Callback function để gửi streaming chunks
   * @returns Phản hồi từ AI agent
   */
  async processWebappMessageWithStreaming(
    userId: number,
    tenantId: number,
    messageData: WebappChatMessageDto,
    onChunk: (chunk: string, messageId: number, conversationId: number) => void,
  ): Promise<ChatResponseDto> {
    try {
      // Bước 1: Lấy hoặc tạo cuộc hội thoại
      let conversation: ChatConversation;

      if (messageData.conversationId) {
        const foundConversation = await this.conversationRepository.findById(
          tenantId,
          messageData.conversationId,
        );
        if (!foundConversation) {
          throw new NotFoundException(
            `Conversation ${messageData.conversationId} not found`,
          );
        }
        conversation = foundConversation;
      } else {
        conversation = await this.getOrCreateActiveConversation(userId, tenantId);
      }

      // Lưu tin nhắn từ user
      const userMessage = await this.messageRepository.create(tenantId, {
        conversationId: conversation.id,
        facebookMessageId: null,
        messageType: messageData.type || WebappMessageType.TEXT,
        content: messageData.content,
        senderType: 'user',
        senderAgentId: userId,
        direction: 'incoming',
        status: 'sent',
        isAiGenerated: false,
        aiContext: null,
        aiConfidence: null,
        detectedIntent: null,
        metadata: messageData.metadata,
        createdAt: Date.now(),
        tenantId,
      });

      // Tạo AI message placeholder
      const aiMessage = await this.messageRepository.create(tenantId, {
        conversationId: conversation.id,
        facebookMessageId: null,
        messageType: WebappMessageType.TEXT,
        content: '', // Sẽ được cập nhật khi streaming hoàn thành
        senderType: 'agent',
        senderAgentId: null,
        direction: 'outgoing',
        status: 'sent',
        isAiGenerated: true,
        aiContext: {},
        aiConfidence: 0.8,
        detectedIntent: 'general_question',
        metadata: {},
        createdAt: Date.now(),
        tenantId,
      });

      // Xử lý streaming với OpenAI
      let fullResponse = '';
      const streamingResponse = await this.openAiService.streamChatCompletion(
        messageData.content,
        'Bạn là trợ lý AI hữu ích cho hệ thống ERP. Hãy trả lời câu hỏi một cách ngắn gọn và chính xác.',
        'gpt-3.5-turbo',
        (chunk: string) => {
          fullResponse += chunk;
          // Gọi callback để gửi chunk qua WebSocket
          onChunk(chunk, aiMessage.id, conversation.id);
        }
      );

      // Cập nhật AI message với full response
      await this.messageRepository.update(tenantId, aiMessage.id, {
        content: streamingResponse,
        updatedAt: Date.now(),
      });

      // Cập nhật thời gian tin nhắn cuối
      await this.conversationRepository.updateLastMessageTime(
        tenantId,
        conversation.id,
        Date.now(),
      );

      // Tạo response DTO
      const response: ChatResponseDto = {
        messageId: aiMessage.id.toString(),
        content: streamingResponse,
        type: WebappMessageType.TEXT,
        confidence: 0.8,
        intent: 'general_question',
        requiresHumanHandoff: false,
        timestamp: aiMessage.createdAt,
        conversationId: conversation.id,
        metadata: aiMessage.metadata,
      };

      this.logger.log(
        `Processed webapp streaming message for user ${userId}, conversation ${conversation.id}`,
      );

      return response;
    } catch (error) {
      this.logger.error(
        `Failed to process webapp streaming message for user ${userId}: ${error.message}`,
      );
      throw error;
    }
  }

  /**
   * Xử lý tin nhắn từ webapp và tạo phản hồi AI
   * Method chính để xử lý tin nhắn từ webapp với AI agent
   * @param userId ID người dùng gửi tin nhắn
   * @param tenantId ID tenant để đảm bảo isolation
   * @param messageData Dữ liệu tin nhắn từ webapp
   * @returns Phản hồi từ AI agent
   */
  async processWebappMessage(
    userId: number,
    tenantId: number,
    messageData: WebappChatMessageDto,
  ): Promise<ChatResponseDto> {
    try {
      // Bước 1: Lấy hoặc tạo cuộc hội thoại
      let conversation: ChatConversation;

      if (messageData.conversationId) {
        // Nếu có conversationId, tìm conversation đó
        const foundConversation = await this.conversationRepository.findById(
          tenantId,
          messageData.conversationId,
        );
        if (!foundConversation) {
          throw new NotFoundException(
            `Conversation ${messageData.conversationId} not found`,
          );
        }
        conversation = foundConversation;
      } else {
        // Nếu không có, lấy hoặc tạo conversation active
        conversation = await this.getOrCreateActiveConversation(userId, tenantId);
      }

      // Lưu tin nhắn từ user
      const userMessage = await this.messageRepository.create(tenantId, {
        conversationId: conversation.id,
        facebookMessageId: null,
        messageType: messageData.type || WebappMessageType.TEXT,
        content: messageData.content,
        senderType: 'user',
        senderAgentId: userId, // Lưu userId vào senderAgentId cho user
        direction: 'incoming',
        status: 'sent',
        isAiGenerated: false,
        aiContext: null,
        aiConfidence: null,
        detectedIntent: null,
        metadata: messageData.metadata,
        createdAt: Date.now(),
        tenantId,
      });

      // Cập nhật thời gian tin nhắn cuối của cuộc hội thoại
      await this.conversationRepository.updateLastMessageTime(
        tenantId,
        conversation.id,
        Date.now(),
      );

      // Xử lý với AI để tạo phản hồi
      const aiResponse = await this.aiOrchestrator.processMessage(
        conversation,
        userMessage,
        tenantId,
      );

      // Lưu phản hồi AI
      const aiMessage = await this.messageRepository.create(tenantId, {
        conversationId: conversation.id,
        facebookMessageId: null,
        messageType: WebappMessageType.TEXT,
        content: aiResponse.text,
        senderType: 'agent',
        senderAgentId: null, // AI agent không có ID cụ thể
        direction: 'outgoing',
        status: 'sent',
        isAiGenerated: true,
        aiContext: aiResponse.context,
        aiConfidence: aiResponse.confidence,
        detectedIntent: aiResponse.intent,
        metadata: {
          requiresHumanHandoff: aiResponse.requiresHumanHandoff,
          quickReplies: aiResponse.quickReplies,
        },
        createdAt: Date.now(),
        tenantId,
      });

      // Cập nhật lại thời gian tin nhắn cuối
      await this.conversationRepository.updateLastMessageTime(
        tenantId,
        conversation.id,
        Date.now(),
      );

      // Tạo response DTO
      const response: ChatResponseDto = {
        messageId: aiMessage.id.toString(),
        content: aiResponse.text,
        type: WebappMessageType.TEXT,
        confidence: aiResponse.confidence,
        intent: aiResponse.intent,
        requiresHumanHandoff: aiResponse.requiresHumanHandoff,
        timestamp: aiMessage.createdAt,
        conversationId: conversation.id,
        metadata: aiMessage.metadata,
      };

      this.logger.log(
        `Processed webapp message for user ${userId}, conversation ${conversation.id}`,
      );

      return response;
    } catch (error) {
      this.logger.error(
        `Failed to process webapp message for user ${userId}: ${error.message}`,
      );
      throw error;
    }
  }

  /**
   * Lấy lịch sử cuộc hội thoại
   * @param userId ID người dùng
   * @param tenantId ID tenant
   * @param query Tham số truy vấn
   * @returns Lịch sử cuộc hội thoại
   */
  async getConversationHistory(
    userId: number,
    tenantId: number,
    query: ConversationHistoryDto,
  ): Promise<ConversationHistoryResponseDto> {
    try {
      // Lấy cuộc hội thoại
      const conversation = await this.conversationRepository.findById(
        tenantId,
        query.conversationId,
      );

      if (!conversation) {
        throw new NotFoundException(
          `Conversation ${query.conversationId} not found`,
        );
      }

      // Kiểm tra quyền truy cập (conversation phải thuộc về user)
      if (conversation.facebookUserId !== userId.toString()) {
        throw new NotFoundException('Conversation not found');
      }

      // Lấy tin nhắn với phân trang
      const messagesResult = await this.messageRepository.findByConversation(
        tenantId,
        query.conversationId,
        {
          page: query.page || 1,
          limit: query.limit || 50,
          sortBy: 'createdAt',
          sortDirection: 'DESC',
        },
      );

      // Chuyển đổi sang DTO
      const response: ConversationHistoryResponseDto = {
        conversationId: conversation.id,
        status: conversation.status as ConversationStatus,
        language: conversation.language,
        createdAt: conversation.createdAt,
        lastMessageAt: conversation.lastMessageAt,
        messages: messagesResult.items.map((msg) => ({
          id: msg.id,
          content: msg.content || '',
          type: msg.messageType as WebappMessageType,
          isAiGenerated: msg.isAiGenerated,
          aiConfidence: msg.aiConfidence ?? undefined, // Convert null to undefined
          detectedIntent: msg.detectedIntent ?? undefined, // Convert null to undefined
          timestamp: msg.createdAt,
          metadata: msg.metadata,
        })),
        pagination: {
          page: messagesResult.meta.currentPage,
          limit: messagesResult.meta.itemsPerPage,
          total: messagesResult.meta.totalItems,
          totalPages: messagesResult.meta.totalPages,
        },
      };

      return response;
    } catch (error) {
      this.logger.error(
        `Failed to get conversation history for user ${userId}: ${error.message}`,
      );
      throw error;
    }
  }

  /**
   * Đóng cuộc hội thoại
   * @param userId ID người dùng
   * @param tenantId ID tenant
   * @param conversationId ID cuộc hội thoại
   */
  async closeConversation(
    userId: number,
    tenantId: number,
    conversationId: number,
  ): Promise<void> {
    try {
      const conversation = await this.conversationRepository.findById(
        tenantId,
        conversationId,
      );

      if (!conversation) {
        throw new NotFoundException(`Conversation ${conversationId} not found`);
      }

      // Kiểm tra quyền
      if (conversation.facebookUserId !== userId.toString()) {
        throw new NotFoundException('Conversation not found');
      }

      await this.conversationRepository.updateStatus(
        tenantId,
        conversationId,
        ConversationStatus.CLOSED,
      );

      this.logger.log(
        `Closed conversation ${conversationId} for user ${userId}`,
      );
    } catch (error) {
      this.logger.error(
        `Failed to close conversation ${conversationId} for user ${userId}: ${error.message}`,
      );
      throw error;
    }
  }
}
