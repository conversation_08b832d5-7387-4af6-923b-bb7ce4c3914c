# Task ID: 1
# Title: Audit Current Codebase for TenantId Usage
# Status: done
# Dependencies: None
# Priority: high
# Description: Systematically review the entire codebase to identify all database queries, repositories, and services that need tenantId filtering
# Details:
Scan all modules (OKRs, Todolists, HRM, Calendar, Chat, System, Email) to identify:
- Repository methods without tenantId filtering
- Service methods that don't pass tenantId
- Controller endpoints missing @CurrentTenant()
- Raw SQL queries without tenantId
- QueryBuilder calls without tenantId conditions

# Test Strategy:
Create a checklist of all files that need updates and verify completeness
