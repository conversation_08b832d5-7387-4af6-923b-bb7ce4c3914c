import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsNotEmpty, IsNumber, ArrayMinSize } from 'class-validator';
import { Type } from 'class-transformer';

/**
 * DTO cho việc xóa nhiều nhân viên
 */
export class BulkDeleteEmployeeDto {
  /**
   * Danh sách ID các nhân viên cần xóa
   */
  @ApiProperty({
    description: 'Danh sách ID các nhân viên cần xóa',
    example: [1, 2, 3],
    type: [Number],
    required: true,
  })
  @IsNotEmpty()
  @IsArray()
  @ArrayMinSize(1, { message: '<PERSON><PERSON><PERSON> có ít nhất 1 nhân viên để xóa' })
  @IsNumber({}, { each: true, message: 'Mỗi ID phải là số' })
  @Type(() => Number)
  ids: number[];
}
