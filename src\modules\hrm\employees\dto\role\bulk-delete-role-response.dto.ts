import { ApiProperty } from '@nestjs/swagger';

/**
 * Chi tiết lỗi khi xóa vai trò
 */
export class RoleDeleteFailureDto {
  /**
   * ID vai trò bị lỗi
   */
  @ApiProperty({
    description: 'ID vai trò bị lỗi',
    example: 1,
    type: Number,
  })
  id: number;

  /**
   * Lý do lỗi
   */
  @ApiProperty({
    description: 'Lý do lỗi',
    example: 'Vai trò không tồn tại',
    type: String,
  })
  reason: string;
}

/**
 * DTO cho kết quả xóa nhiều vai trò
 */
export class BulkDeleteRoleResponseDto {
  /**
   * Tổng số vai trò được yêu cầu xóa
   */
  @ApiProperty({
    description: 'Tổng số vai trò được yêu cầu xóa',
    example: 5,
    type: Number,
  })
  totalRequested: number;

  /**
   * Số vai trò đã xóa thành công
   */
  @ApiProperty({
    description: 'Số vai trò đã xóa thành công',
    example: 3,
    type: Number,
  })
  successCount: number;

  /**
   * Số vai trò không thể xóa
   */
  @ApiProperty({
    description: 'Số vai trò không thể xóa',
    example: 2,
    type: Number,
  })
  failureCount: number;

  /**
   * Danh sách ID các vai trò đã xóa thành công
   */
  @ApiProperty({
    description: 'Danh sách ID các vai trò đã xóa thành công',
    example: [1, 3, 5],
    type: [Number],
  })
  deletedIds: number[];

  /**
   * Danh sách các vai trò không thể xóa và lý do
   */
  @ApiProperty({
    description: 'Danh sách các vai trò không thể xóa và lý do',
    type: [RoleDeleteFailureDto],
    example: [
      { id: 2, reason: 'Vai trò không tồn tại' },
      { id: 4, reason: 'Vai trò đang được sử dụng bởi người dùng' },
    ],
  })
  failures: RoleDeleteFailureDto[];
}
