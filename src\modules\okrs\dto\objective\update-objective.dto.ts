import { ApiProperty } from '@nestjs/swagger';
import {
  IsDateString,
  IsEnum,
  IsNumber,
  IsOptional,
  IsString,
} from 'class-validator';
import { ObjectiveType } from '../../enum/objective-type.enum';

/**
 * DTO for updating an objective
 */
export class UpdateObjectiveDto {
  /**
   * Title of the objective
   * @example "Tăng doanh thu 25%"
   */
  @ApiProperty({
    description: 'Tiêu đề mục tiêu',
    example: 'Tăng doanh thu 25%',
    required: false,
  })
  @IsString()
  @IsOptional()
  title?: string;

  /**
   * Detailed description of the objective
   * @example "Tăng doanh thu 25% so với quý trước thông qua các chiến dịch marketing mới"
   */
  @ApiProperty({
    description: 'Mô tả chi tiết mục tiêu',
    example:
      'Tăng doanh thu 25% so với quý trước thông qua các chiến dịch marketing mới',
    required: false,
  })
  @IsString()
  @IsOptional()
  description?: string;

  /**
   * ID of the user responsible for the objective
   * Chỉ áp dụng cho type DEPARTMENT và COMPANY
   * Với type INDIVIDUAL, ownerId sẽ tự động là user đang đăng nhập
   * @example 1
   */
  @ApiProperty({
    description: 'ID của người chịu trách nhiệm (chỉ cho DEPARTMENT và COMPANY)',
    example: 1,
    required: false,
  })
  @IsNumber()
  @IsOptional()
  ownerId?: number;

  /**
   * ID of the department (if applicable)
   * @example 2
   */
  @ApiProperty({
    description: 'ID của phòng ban (nếu có)',
    example: 2,
    required: false,
  })
  @IsNumber()
  @IsOptional()
  departmentId?: number;

  /**
   * ID of the parent objective (if this is a child objective)
   * @example 3
   */
  @ApiProperty({
    description: 'ID của mục tiêu cha (nếu là mục tiêu con)',
    example: 3,
    required: false,
  })
  @IsNumber()
  @IsOptional()
  parentId?: number;

  /**
   * Status of the objective
   * @example "active"
   */
  @ApiProperty({
    description: 'Trạng thái mục tiêu',
    example: 'active',
    required: false,
  })
  @IsString()
  @IsOptional()
  status?: string;

  /**
   * Type of the objective
   * @example "COMPANY"
   */
  @ApiProperty({
    description: 'Loại mục tiêu',
    enum: ObjectiveType,
    example: ObjectiveType.COMPANY,
    required: false,
  })
  @IsEnum(ObjectiveType)
  @IsOptional()
  type?: ObjectiveType;

  /**
   * Start date of the objective
   * @example "2025-01-01"
   */
  @ApiProperty({
    description: 'Ngày bắt đầu mục tiêu',
    example: '2025-01-01',
    required: false,
  })
  @IsDateString()
  @IsOptional()
  startDate?: string;

  /**
   * End date of the objective
   * @example "2025-03-31"
   */
  @ApiProperty({
    description: 'Ngày kết thúc mục tiêu',
    example: '2025-03-31',
    required: false,
  })
  @IsDateString()
  @IsOptional()
  endDate?: string;
}
