import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, In } from 'typeorm';
import { Department } from '../entities/department.entity';
import { DepartmentQueryDto } from '../dto/department';
import { PaginatedResult } from '@/common/response/api-response-dto';

/**
 * Repository for department entity
 */
@Injectable()
export class DepartmentRepository {
  private readonly logger = new Logger(DepartmentRepository.name);

  constructor(
    @InjectRepository(Department)
    private readonly repository: Repository<Department>,
  ) {}

  /**
   * Find all departments with pagination and filtering
   * @param tenantId ID tenant (required for tenant isolation)
   * @param query Query parameters
   * @returns Paginated list of departments
   */
  async findAll(
    tenantId: number,
    query: DepartmentQueryDto,
  ): Promise<PaginatedResult<Department>> {
    const {
      page = 1,
      limit = 10,
      search,
      sortBy = 'name',
      sortDirection = 'ASC',
      managerId,
      parentId,
    } = query;

    const queryBuilder = this.repository.createQueryBuilder('department');

    // Add tenantId filtering - REQUIRED for tenant isolation
    queryBuilder.andWhere('department.tenantId = :tenantId', { tenantId });

    // Apply filters if provided
    if (managerId) {
      queryBuilder.andWhere('department.managerId = :managerId', { managerId });
    }

    if (parentId !== undefined) {
      if (parentId === null) {
        queryBuilder.andWhere('department.parentId IS NULL');
      } else {
        queryBuilder.andWhere('department.parentId = :parentId', { parentId });
      }
    }

    // Apply search filter if provided
    if (search) {
      queryBuilder.andWhere(
        'department.name ILIKE :search OR department.description ILIKE :search',
        { search: `%${search}%` },
      );
    }

    // Apply sorting
    queryBuilder.orderBy(`department.${sortBy}`, sortDirection);

    // Apply pagination
    const [items, totalItems] = await queryBuilder
      .skip((page - 1) * limit)
      .take(limit)
      .getManyAndCount();

    return {
      items,
      meta: {
        totalItems,
        itemCount: items.length,
        itemsPerPage: limit,
        totalPages: Math.ceil(totalItems / limit),
        currentPage: page,
      },
    };
  }

  /**
   * Find department by ID
   * @param tenantId ID tenant (required for tenant isolation)
   * @param id Department ID
   * @returns Department or null if not found
   */
  async findById(tenantId: number, id: number): Promise<Department | null> {
    return this.repository.findOne({
      where: { id, tenantId },
    });
  }

  /**
   * Find department by name
   * @param tenantId ID tenant (required for tenant isolation)
   * @param name Department name
   * @returns Department or null if not found
   */
  async findByName(tenantId: number, name: string): Promise<Department | null> {
    return this.repository.findOne({
      where: { name, tenantId },
    });
  }

  /**
   * Check if department has children
   * @param tenantId ID tenant (required for tenant isolation)
   * @param id Department ID
   * @returns True if department has children, false otherwise
   */
  async hasChildren(tenantId: number, id: number): Promise<boolean> {
    const count = await this.repository.count({
      where: { parentId: id, tenantId },
    });
    return count > 0;
  }

  /**
   * Create a new department
   * @param tenantId ID tenant (required for tenant isolation)
   * @param data Department data
   * @returns Created department
   */
  async create(
    tenantId: number,
    data: Partial<Department>,
  ): Promise<Department> {
    const department = this.repository.create({ ...data, tenantId });
    return this.repository.save(department);
  }

  /**
   * Update department
   * @param tenantId ID tenant (required for tenant isolation)
   * @param id Department ID
   * @param data Updated department data
   * @returns Updated department or null if not found
   */
  async update(
    tenantId: number,
    id: number,
    data: Partial<Department>,
  ): Promise<Department | null> {
    await this.repository.update({ id, tenantId }, data);
    return this.findById(tenantId, id);
  }

  /**
   * Delete department
   * @param tenantId ID tenant (required for tenant isolation)
   * @param id Department ID
   * @returns True if deleted, false if not found
   */
  async delete(tenantId: number, id: number): Promise<boolean> {
    const result = await this.repository.delete({ id, tenantId });
    return (result.affected ?? 0) > 0;
  }

  /**
   * Get all departments by tenant
   * @param tenantId ID tenant (required for tenant isolation)
   * @returns List of departments
   */
  async getAllByTenantId(tenantId: number): Promise<Department[]> {
    return this.repository.find({
      where: { tenantId },
      order: { name: 'ASC' },
    });
  }

  /**
   * Tìm nhiều phòng ban theo danh sách ID
   * @param tenantId ID tenant (required for tenant isolation)
   * @param ids Danh sách ID phòng ban
   * @returns Danh sách phòng ban tìm thấy
   */
  async findByIds(tenantId: number, ids: number[]): Promise<Department[]> {
    if (ids.length === 0) return [];

    return this.repository.find({
      where: {
        id: In(ids),
        tenantId,
      },
    });
  }

  /**
   * Find all departments with manager and parent department information
   * @param tenantId ID tenant (required for tenant isolation)
   * @param query Query parameters
   * @returns Paginated list of departments with related data
   */
  async findAllWithRelations(
    tenantId: number,
    query: DepartmentQueryDto,
  ): Promise<PaginatedResult<any>> {
    const {
      page = 1,
      limit = 10,
      search,
      sortBy = 'name',
      sortDirection = 'ASC',
      managerId,
      parentId,
    } = query;

    const queryBuilder = this.repository.createQueryBuilder('department');

    // Left join với bảng users để lấy thông tin manager
    queryBuilder.leftJoin(
      'users',
      'manager',
      'department.managerId = manager.id AND manager.tenantId = :tenantId',
      { tenantId },
    );

    // Left join với bảng departments để lấy thông tin parent department
    queryBuilder.leftJoin(
      'departments',
      'parentDept',
      'department.parentId = parentDept.id AND parentDept.tenantId = :tenantId',
    );

    // Select các field cần thiết
    queryBuilder.select([
      'department.id as department_id',
      'department.name as department_name',
      'department.description as department_description',
      'department.managerId as department_managerId',
      'department.parentId as department_parentId',
      'department.createdAt as department_createdAt',
      'manager.id as manager_id',
      'manager.fullName as manager_fullName',
      'manager.email as manager_email',
      'manager.position as manager_position',
      'parentDept.id as parent_dept_id',
      'parentDept.name as parent_dept_name',
    ]);

    // Add tenantId filtering - REQUIRED for tenant isolation
    queryBuilder.andWhere('department.tenantId = :tenantId', { tenantId });

    // Apply filters if provided
    if (managerId) {
      queryBuilder.andWhere('department.managerId = :managerId', { managerId });
    }

    if (parentId !== undefined) {
      if (parentId === null) {
        queryBuilder.andWhere('department.parentId IS NULL');
      } else {
        queryBuilder.andWhere('department.parentId = :parentId', { parentId });
      }
    }

    // Apply search filter if provided
    if (search) {
      queryBuilder.andWhere(
        'department.name ILIKE :search OR department.description ILIKE :search',
        { search: `%${search}%` },
      );
    }

    // Apply sorting
    queryBuilder.orderBy(`department.${sortBy}`, sortDirection);

    // Apply pagination
    const totalItems = await queryBuilder.getCount();
    const items = await queryBuilder
      .skip((page - 1) * limit)
      .take(limit)
      .getRawMany();

    return {
      items,
      meta: {
        totalItems,
        itemCount: items.length,
        itemsPerPage: limit,
        totalPages: Math.ceil(totalItems / limit),
        currentPage: page,
      },
    };
  }

}
