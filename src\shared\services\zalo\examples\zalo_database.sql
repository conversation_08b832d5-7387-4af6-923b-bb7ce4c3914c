/*
 * SQL script để tạo các bảng cần thiết cho tích hợp <PERSON>
 *
 * Script này tạo 6 bảng chính:
 * 1. zalo_official_accounts: <PERSON><PERSON><PERSON> thông tin về các Official Account Zalo
 * 2. zalo_zns_templates: <PERSON><PERSON><PERSON> thông tin về các template ZNS (Zalo Notification Service)
 * 3. zalo_messages: <PERSON><PERSON><PERSON><PERSON> sử tin nhắn giữa người dùng và Official Account
 * 4. zalo_zns_messages: <PERSON><PERSON><PERSON> lịch sử các tin nhắn ZNS đã gửi
 * 5. zalo_followers: <PERSON><PERSON><PERSON> thông tin về người dùng Zalo đã theo dõi Official Account
 * 6. zalo_webhook_logs: <PERSON><PERSON><PERSON> lị<PERSON> sử các sự kiện webhook từ Zalo
 *
 * Ngoài ra, script cũng tạo các chỉ mục (indexes) và ràng buộc khóa ngoại (foreign keys)
 * để tối ưu hiệu suất truy vấn và đảm bảo tính toàn vẹn dữ liệu.
 */

-- Tạ<PERSON> bảng zalo_official_accounts
-- <PERSON><PERSON>ng này lưu trữ thông tin về các Official Account của Zalo mà người dùng đã kết nối với hệ thống
CREATE TABLE zalo_official_accounts (
  id SERIAL PRIMARY KEY,
  user_id INTEGER NOT NULL,                -- ID người dùng sở hữu Official Account
  oa_id VARCHAR(50) NOT NULL,              -- ID của Official Account trên Zalo
  name VARCHAR(255) NOT NULL,              -- Tên của Official Account
  description VARCHAR(500),                -- Mô tả của Official Account
  avatar_url VARCHAR(500),                 -- URL avatar của Official Account
  access_token VARCHAR(500) NOT NULL,      -- Access token của Official Account
  refresh_token VARCHAR(500),              -- Refresh token của Official Account
  expires_at BIGINT NOT NULL,              -- Thời gian hết hạn của access token (Unix timestamp)
  agent_id INTEGER,                        -- ID của agent được kết nối với Official Account
  status VARCHAR(20) DEFAULT 'active',     -- Trạng thái kết nối (active, inactive)
  created_at BIGINT NOT NULL DEFAULT (EXTRACT(EPOCH FROM NOW())::BIGINT),  -- Thời điểm tạo (Unix timestamp)
  updated_at BIGINT NOT NULL DEFAULT (EXTRACT(EPOCH FROM NOW())::BIGINT),  -- Thời điểm cập nhật (Unix timestamp)
  UNIQUE(user_id, oa_id)
);

-- Tạo bảng zalo_zns_templates
-- Bảng này lưu trữ thông tin về các template ZNS (Zalo Notification Service) mà người dùng đã tạo hoặc được cấp quyền sử dụng
-- Template ZNS được sử dụng để gửi thông báo đến người dùng Zalo thông qua số điện thoại
CREATE TABLE zalo_zns_templates (
  id SERIAL PRIMARY KEY,
  user_id INTEGER NOT NULL,                -- ID người dùng sở hữu template
  oa_id VARCHAR(50) NOT NULL,              -- ID của Official Account
  template_id VARCHAR(50) NOT NULL,        -- ID của template trên Zalo
  template_name VARCHAR(255) NOT NULL,     -- Tên của template
  template_content TEXT NOT NULL,          -- Nội dung của template
  params JSONB DEFAULT '[]',               -- Các tham số của template (JSON)
  status VARCHAR(20) NOT NULL,             -- Trạng thái của template (approved, pending, rejected)
  created_at BIGINT NOT NULL DEFAULT (EXTRACT(EPOCH FROM NOW())::BIGINT),  -- Thời điểm tạo (Unix timestamp)
  updated_at BIGINT NOT NULL DEFAULT (EXTRACT(EPOCH FROM NOW())::BIGINT),  -- Thời điểm cập nhật (Unix timestamp)
  UNIQUE(oa_id, template_id)
);

-- Tạo bảng zalo_messages
-- Bảng này lưu trữ lịch sử tin nhắn giữa người dùng Zalo và Official Account
-- Bao gồm cả tin nhắn đến (từ người dùng Zalo) và tin nhắn đi (từ hệ thống đến người dùng Zalo)
CREATE TABLE zalo_messages (
  id SERIAL PRIMARY KEY,
  oa_id VARCHAR(50) NOT NULL,              -- ID của Official Account
  user_id VARCHAR(50) NOT NULL,            -- ID của người dùng Zalo
  message_id VARCHAR(50),                  -- ID của tin nhắn trên Zalo
  message_type VARCHAR(20) NOT NULL,       -- Loại tin nhắn (text, image, file, template)
  content TEXT,                            -- Nội dung tin nhắn
  data JSONB,                              -- Dữ liệu bổ sung của tin nhắn (JSON)
  direction VARCHAR(10) NOT NULL,          -- Hướng tin nhắn (incoming, outgoing)
  agent_id INTEGER,                        -- ID của agent xử lý tin nhắn
  timestamp BIGINT NOT NULL,               -- Thời điểm gửi/nhận (Unix timestamp)
  created_at BIGINT NOT NULL DEFAULT (EXTRACT(EPOCH FROM NOW())::BIGINT)  -- Thời điểm tạo bản ghi (Unix timestamp)
);

-- Tạo bảng zalo_zns_messages
-- Bảng này lưu trữ lịch sử các tin nhắn ZNS đã gửi
-- ZNS (Zalo Notification Service) cho phép gửi thông báo đến người dùng Zalo thông qua số điện thoại,
-- ngay cả khi họ chưa theo dõi Official Account
CREATE TABLE zalo_zns_messages (
  id SERIAL PRIMARY KEY,
  user_id INTEGER NOT NULL,                -- ID người dùng gửi tin nhắn
  oa_id VARCHAR(50) NOT NULL,              -- ID của Official Account
  template_id VARCHAR(50) NOT NULL,        -- ID của template
  phone VARCHAR(20) NOT NULL,              -- Số điện thoại người nhận
  message_id VARCHAR(50),                  -- ID của tin nhắn trên Zalo
  tracking_id VARCHAR(50) NOT NULL,        -- ID giao dịch
  template_data JSONB NOT NULL,            -- Dữ liệu đã gửi cho template (JSON)
  status VARCHAR(20) DEFAULT 'pending',    -- Trạng thái tin nhắn (pending, delivered, failed)
  delivered_time BIGINT,                   -- Thời điểm gửi thành công (Unix timestamp)
  created_at BIGINT NOT NULL DEFAULT (EXTRACT(EPOCH FROM NOW())::BIGINT),  -- Thời điểm tạo (Unix timestamp)
  updated_at BIGINT NOT NULL DEFAULT (EXTRACT(EPOCH FROM NOW())::BIGINT)   -- Thời điểm cập nhật (Unix timestamp)
);

-- Tạo bảng zalo_followers
-- Bảng này lưu trữ thông tin về người dùng Zalo đã theo dõi Official Account
-- Bao gồm thông tin cá nhân của người dùng (nếu được cấp quyền) và trạng thái theo dõi
CREATE TABLE zalo_followers (
  id SERIAL PRIMARY KEY,
  oa_id VARCHAR(50) NOT NULL,              -- ID của Official Account
  user_id VARCHAR(50) NOT NULL,            -- ID của người dùng Zalo
  display_name VARCHAR(255),               -- Tên hiển thị của người dùng
  avatar_url VARCHAR(500),                 -- URL avatar của người dùng
  phone VARCHAR(20),                       -- Số điện thoại của người dùng (nếu được cấp quyền)
  gender SMALLINT,                         -- Giới tính của người dùng (1: Nam, 2: Nữ)
  birth_date VARCHAR(20),                  -- Ngày sinh của người dùng (định dạng dd/mm/yyyy)
  tags JSONB DEFAULT '[]',                 -- Các tag gán cho người dùng (JSON)
  followed_at BIGINT NOT NULL,             -- Thời điểm theo dõi (Unix timestamp)
  unfollowed_at BIGINT,                    -- Thời điểm hủy theo dõi (Unix timestamp)
  status VARCHAR(20) DEFAULT 'active',     -- Trạng thái (active, unfollowed)
  created_at BIGINT NOT NULL DEFAULT (EXTRACT(EPOCH FROM NOW())::BIGINT),  -- Thời điểm tạo (Unix timestamp)
  updated_at BIGINT NOT NULL DEFAULT (EXTRACT(EPOCH FROM NOW())::BIGINT),  -- Thời điểm cập nhật (Unix timestamp)
  UNIQUE(oa_id, user_id)
);

-- Tạo bảng zalo_webhook_logs
-- Bảng này lưu trữ lịch sử các sự kiện webhook từ Zalo
-- Webhook là cơ chế để Zalo thông báo cho hệ thống về các sự kiện như tin nhắn mới, người dùng theo dõi/hủy theo dõi, v.v.
CREATE TABLE zalo_webhook_logs (
  id SERIAL PRIMARY KEY,
  oa_id VARCHAR(50) NOT NULL,              -- ID của Official Account
  event_name VARCHAR(50) NOT NULL,         -- Tên sự kiện
  event_id VARCHAR(50) NOT NULL,           -- ID của sự kiện
  data JSONB NOT NULL,                     -- Dữ liệu của sự kiện (JSON)
  processed BOOLEAN DEFAULT false,         -- Đã xử lý chưa
  timestamp BIGINT NOT NULL,               -- Thời điểm xảy ra sự kiện (Unix timestamp)
  created_at BIGINT NOT NULL DEFAULT (EXTRACT(EPOCH FROM NOW())::BIGINT)  -- Thời điểm tạo bản ghi (Unix timestamp)
);

/*
 * Tạo các chỉ mục (indexes) để tối ưu hiệu suất truy vấn
 *
 * Chỉ mục giúp tăng tốc độ truy vấn dữ liệu, đặc biệt là các truy vấn tìm kiếm và sắp xếp.
 * Tuy nhiên, chỉ mục cũng làm chậm các thao tác thêm, sửa, xóa dữ liệu.
 * Vì vậy, chỉ nên tạo chỉ mục cho các cột thường xuyên được sử dụng trong điều kiện WHERE, ORDER BY, JOIN.
 */

-- Chỉ mục cho bảng zalo_official_accounts
CREATE INDEX idx_zalo_oa_user_id ON zalo_official_accounts(user_id);
CREATE INDEX idx_zalo_oa_agent_id ON zalo_official_accounts(agent_id);

-- Chỉ mục cho bảng zalo_zns_templates
CREATE INDEX idx_zalo_zns_templates_user_id ON zalo_zns_templates(user_id);
CREATE INDEX idx_zalo_zns_templates_oa_id ON zalo_zns_templates(oa_id);

-- Chỉ mục cho bảng zalo_messages
CREATE INDEX idx_zalo_messages_oa_id ON zalo_messages(oa_id);
CREATE INDEX idx_zalo_messages_user_id ON zalo_messages(user_id);
CREATE INDEX idx_zalo_messages_timestamp ON zalo_messages(timestamp);
CREATE INDEX idx_zalo_messages_agent_id ON zalo_messages(agent_id);

-- Chỉ mục cho bảng zalo_zns_messages
CREATE INDEX idx_zalo_zns_messages_user_id ON zalo_zns_messages(user_id);
CREATE INDEX idx_zalo_zns_messages_oa_id ON zalo_zns_messages(oa_id);
CREATE INDEX idx_zalo_zns_messages_phone ON zalo_zns_messages(phone);
CREATE INDEX idx_zalo_zns_messages_status ON zalo_zns_messages(status);

-- Chỉ mục cho bảng zalo_followers
CREATE INDEX idx_zalo_followers_oa_id ON zalo_followers(oa_id);
CREATE INDEX idx_zalo_followers_status ON zalo_followers(status);

-- Chỉ mục cho bảng zalo_webhook_logs
CREATE INDEX idx_zalo_webhook_logs_oa_id ON zalo_webhook_logs(oa_id);
CREATE INDEX idx_zalo_webhook_logs_event_name ON zalo_webhook_logs(event_name);
CREATE INDEX idx_zalo_webhook_logs_processed ON zalo_webhook_logs(processed);
CREATE INDEX idx_zalo_webhook_logs_timestamp ON zalo_webhook_logs(timestamp);

/*
 * Tạo các ràng buộc khóa ngoại (foreign keys)
 *
 * Ràng buộc khóa ngoại đảm bảo tính toàn vẹn dữ liệu giữa các bảng.
 * Khi một bản ghi trong bảng cha bị xóa, các bản ghi liên quan trong bảng con cũng sẽ bị xóa (ON DELETE CASCADE).
 * Điều này đảm bảo không có dữ liệu "mồ côi" trong hệ thống.
 */

-- Ràng buộc cho bảng zalo_zns_templates
ALTER TABLE zalo_zns_templates
ADD CONSTRAINT fk_zalo_zns_templates_oa
FOREIGN KEY (oa_id) REFERENCES zalo_official_accounts(oa_id)
ON DELETE CASCADE;

-- Ràng buộc cho bảng zalo_messages
ALTER TABLE zalo_messages
ADD CONSTRAINT fk_zalo_messages_oa
FOREIGN KEY (oa_id) REFERENCES zalo_official_accounts(oa_id)
ON DELETE CASCADE;

-- Ràng buộc cho bảng zalo_zns_messages
ALTER TABLE zalo_zns_messages
ADD CONSTRAINT fk_zalo_zns_messages_oa
FOREIGN KEY (oa_id) REFERENCES zalo_official_accounts(oa_id)
ON DELETE CASCADE;

ALTER TABLE zalo_zns_messages
ADD CONSTRAINT fk_zalo_zns_messages_template
FOREIGN KEY (template_id) REFERENCES zalo_zns_templates(template_id)
ON DELETE CASCADE;

-- Ràng buộc cho bảng zalo_followers
ALTER TABLE zalo_followers
ADD CONSTRAINT fk_zalo_followers_oa
FOREIGN KEY (oa_id) REFERENCES zalo_official_accounts(oa_id)
ON DELETE CASCADE;

-- Ràng buộc cho bảng zalo_webhook_logs
ALTER TABLE zalo_webhook_logs
ADD CONSTRAINT fk_zalo_webhook_logs_oa
FOREIGN KEY (oa_id) REFERENCES zalo_official_accounts(oa_id)
ON DELETE CASCADE;

/*
 * Thêm comment vào các bảng và cột trong database
 * Các comment này sẽ hiển thị trong các công cụ quản lý database và giúp người dùng hiểu rõ hơn về cấu trúc database
 */

-- Comment cho bảng zalo_official_accounts
COMMENT ON TABLE zalo_official_accounts IS 'Lưu trữ thông tin về các Official Account của Zalo mà người dùng đã kết nối với hệ thống';
COMMENT ON COLUMN zalo_official_accounts.id IS 'ID tự động tăng';
COMMENT ON COLUMN zalo_official_accounts.user_id IS 'ID người dùng sở hữu Official Account';
COMMENT ON COLUMN zalo_official_accounts.oa_id IS 'ID của Official Account trên Zalo';
COMMENT ON COLUMN zalo_official_accounts.name IS 'Tên của Official Account';
COMMENT ON COLUMN zalo_official_accounts.description IS 'Mô tả của Official Account';
COMMENT ON COLUMN zalo_official_accounts.avatar_url IS 'URL avatar của Official Account';
COMMENT ON COLUMN zalo_official_accounts.access_token IS 'Access token của Official Account';
COMMENT ON COLUMN zalo_official_accounts.refresh_token IS 'Refresh token của Official Account';
COMMENT ON COLUMN zalo_official_accounts.expires_at IS 'Thời gian hết hạn của access token (Unix timestamp)';
COMMENT ON COLUMN zalo_official_accounts.agent_id IS 'ID của agent được kết nối với Official Account';
COMMENT ON COLUMN zalo_official_accounts.status IS 'Trạng thái kết nối (active, inactive)';
COMMENT ON COLUMN zalo_official_accounts.created_at IS 'Thời điểm tạo (Unix timestamp)';
COMMENT ON COLUMN zalo_official_accounts.updated_at IS 'Thời điểm cập nhật (Unix timestamp)';

-- Comment cho bảng zalo_zns_templates
COMMENT ON TABLE zalo_zns_templates IS 'Lưu trữ thông tin về các template ZNS (Zalo Notification Service) mà người dùng đã tạo hoặc được cấp quyền sử dụng';
COMMENT ON COLUMN zalo_zns_templates.id IS 'ID tự động tăng';
COMMENT ON COLUMN zalo_zns_templates.user_id IS 'ID người dùng sở hữu template';
COMMENT ON COLUMN zalo_zns_templates.oa_id IS 'ID của Official Account';
COMMENT ON COLUMN zalo_zns_templates.template_id IS 'ID của template trên Zalo';
COMMENT ON COLUMN zalo_zns_templates.template_name IS 'Tên của template';
COMMENT ON COLUMN zalo_zns_templates.template_content IS 'Nội dung của template';
COMMENT ON COLUMN zalo_zns_templates.params IS 'Các tham số của template (JSON)';
COMMENT ON COLUMN zalo_zns_templates.status IS 'Trạng thái của template (approved, pending, rejected)';
COMMENT ON COLUMN zalo_zns_templates.created_at IS 'Thời điểm tạo (Unix timestamp)';
COMMENT ON COLUMN zalo_zns_templates.updated_at IS 'Thời điểm cập nhật (Unix timestamp)';

-- Comment cho bảng zalo_messages
COMMENT ON TABLE zalo_messages IS 'Lưu trữ lịch sử tin nhắn giữa người dùng Zalo và Official Account';
COMMENT ON COLUMN zalo_messages.id IS 'ID tự động tăng';
COMMENT ON COLUMN zalo_messages.oa_id IS 'ID của Official Account';
COMMENT ON COLUMN zalo_messages.user_id IS 'ID của người dùng Zalo';
COMMENT ON COLUMN zalo_messages.message_id IS 'ID của tin nhắn trên Zalo';
COMMENT ON COLUMN zalo_messages.message_type IS 'Loại tin nhắn (text, image, file, template)';
COMMENT ON COLUMN zalo_messages.content IS 'Nội dung tin nhắn';
COMMENT ON COLUMN zalo_messages.data IS 'Dữ liệu bổ sung của tin nhắn (JSON)';
COMMENT ON COLUMN zalo_messages.direction IS 'Hướng tin nhắn (incoming, outgoing)';
COMMENT ON COLUMN zalo_messages.agent_id IS 'ID của agent xử lý tin nhắn';
COMMENT ON COLUMN zalo_messages.timestamp IS 'Thời điểm gửi/nhận (Unix timestamp)';
COMMENT ON COLUMN zalo_messages.created_at IS 'Thời điểm tạo bản ghi (Unix timestamp)';

-- Comment cho bảng zalo_zns_messages
COMMENT ON TABLE zalo_zns_messages IS 'Lưu trữ lịch sử các tin nhắn ZNS đã gửi';
COMMENT ON COLUMN zalo_zns_messages.id IS 'ID tự động tăng';
COMMENT ON COLUMN zalo_zns_messages.user_id IS 'ID người dùng gửi tin nhắn';
COMMENT ON COLUMN zalo_zns_messages.oa_id IS 'ID của Official Account';
COMMENT ON COLUMN zalo_zns_messages.template_id IS 'ID của template';
COMMENT ON COLUMN zalo_zns_messages.phone IS 'Số điện thoại người nhận';
COMMENT ON COLUMN zalo_zns_messages.message_id IS 'ID của tin nhắn trên Zalo';
COMMENT ON COLUMN zalo_zns_messages.tracking_id IS 'ID giao dịch';
COMMENT ON COLUMN zalo_zns_messages.template_data IS 'Dữ liệu đã gửi cho template (JSON)';
COMMENT ON COLUMN zalo_zns_messages.status IS 'Trạng thái tin nhắn (pending, delivered, failed)';
COMMENT ON COLUMN zalo_zns_messages.delivered_time IS 'Thời điểm gửi thành công (Unix timestamp)';
COMMENT ON COLUMN zalo_zns_messages.created_at IS 'Thời điểm tạo (Unix timestamp)';
COMMENT ON COLUMN zalo_zns_messages.updated_at IS 'Thời điểm cập nhật (Unix timestamp)';

-- Comment cho bảng zalo_followers
COMMENT ON TABLE zalo_followers IS 'Lưu trữ thông tin về người dùng Zalo đã theo dõi Official Account';
COMMENT ON COLUMN zalo_followers.id IS 'ID tự động tăng';
COMMENT ON COLUMN zalo_followers.oa_id IS 'ID của Official Account';
COMMENT ON COLUMN zalo_followers.user_id IS 'ID của người dùng Zalo';
COMMENT ON COLUMN zalo_followers.display_name IS 'Tên hiển thị của người dùng';
COMMENT ON COLUMN zalo_followers.avatar_url IS 'URL avatar của người dùng';
COMMENT ON COLUMN zalo_followers.phone IS 'Số điện thoại của người dùng (nếu được cấp quyền)';
COMMENT ON COLUMN zalo_followers.gender IS 'Giới tính của người dùng (1: Nam, 2: Nữ)';
COMMENT ON COLUMN zalo_followers.birth_date IS 'Ngày sinh của người dùng (định dạng dd/mm/yyyy)';
COMMENT ON COLUMN zalo_followers.tags IS 'Các tag gán cho người dùng (JSON)';
COMMENT ON COLUMN zalo_followers.followed_at IS 'Thời điểm theo dõi (Unix timestamp)';
COMMENT ON COLUMN zalo_followers.unfollowed_at IS 'Thời điểm hủy theo dõi (Unix timestamp)';
COMMENT ON COLUMN zalo_followers.status IS 'Trạng thái (active, unfollowed)';
COMMENT ON COLUMN zalo_followers.created_at IS 'Thời điểm tạo (Unix timestamp)';
COMMENT ON COLUMN zalo_followers.updated_at IS 'Thời điểm cập nhật (Unix timestamp)';

-- Comment cho bảng zalo_webhook_logs
COMMENT ON TABLE zalo_webhook_logs IS 'Lưu trữ lịch sử các sự kiện webhook từ Zalo';
COMMENT ON COLUMN zalo_webhook_logs.id IS 'ID tự động tăng';
COMMENT ON COLUMN zalo_webhook_logs.oa_id IS 'ID của Official Account';
COMMENT ON COLUMN zalo_webhook_logs.event_name IS 'Tên sự kiện';
COMMENT ON COLUMN zalo_webhook_logs.event_id IS 'ID của sự kiện';
COMMENT ON COLUMN zalo_webhook_logs.data IS 'Dữ liệu của sự kiện (JSON)';
COMMENT ON COLUMN zalo_webhook_logs.processed IS 'Đã xử lý chưa';
COMMENT ON COLUMN zalo_webhook_logs.timestamp IS 'Thời điểm xảy ra sự kiện (Unix timestamp)';
COMMENT ON COLUMN zalo_webhook_logs.created_at IS 'Thời điểm tạo bản ghi (Unix timestamp)';

/*
 * Lưu ý quan trọng:
 *
 * 1. Bạn cần điều chỉnh các ràng buộc khóa ngoại để phù hợp với cấu trúc database hiện tại của bạn.
 *    Ví dụ: Thêm ràng buộc khóa ngoại từ zalo_official_accounts.user_id đến bảng users.id
 *    và từ zalo_official_accounts.agent_id đến bảng agents.id
 *
 * 2. Kiểu dữ liệu trong file SQL được thiết kế cho PostgreSQL. Nếu bạn sử dụng hệ quản trị cơ sở dữ liệu khác,
 *    bạn có thể cần điều chỉnh kiểu dữ liệu cho phù hợp.
 *
 * 3. Các trường thời gian (created_at, updated_at) được thiết lập giá trị mặc định là thời gian hiện tại
 *    dưới dạng Unix timestamp. Bạn có thể điều chỉnh theo nhu cầu.
 *
 * 4. Sau khi tạo các bảng trong database, bạn cần tạo các entity TypeORM tương ứng để sử dụng
 *    trong ứng dụng NestJS của bạn.
 */
