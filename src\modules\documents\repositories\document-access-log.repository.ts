import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import {
  DocumentAccessLog,
  AccessType,
} from '../entities/document-access-log.entity';
import { PaginatedResult } from '@/common/response/api-response-dto';

/**
 * Repository cho entity DocumentAccessLog với tenant isolation
 */
@Injectable()
export class DocumentAccessLogRepository {
  private readonly logger = new Logger(DocumentAccessLogRepository.name);

  constructor(
    @InjectRepository(DocumentAccessLog)
    private readonly repository: Repository<DocumentAccessLog>,
  ) {}

  /**
   * Tạo log truy cập mới
   * @param tenantId ID tenant (required for tenant isolation)
   * @param data Dữ liệu log
   * @returns Log đã tạo
   */
  async create(
    tenantId: number,
    data: Partial<DocumentAccessLog>,
  ): Promise<DocumentAccessLog> {
    const log = this.repository.create({
      ...data,
      tenantId,
      createdAt: Date.now(),
    });
    return this.repository.save(log);
  }

  /**
   * Tìm tất cả logs với phân trang và lọc
   * @param tenantId ID tenant (required for tenant isolation)
   * @param options Tùy chọn truy vấn
   * @returns Danh sách logs đã phân trang
   */
  async findAll(
    tenantId: number,
    options: {
      page?: number;
      limit?: number;
      documentId?: number;
      userId?: number;
      accessType?: AccessType;
      success?: boolean;
      startDate?: number;
      endDate?: number;
      sortBy?: string;
      sortDirection?: 'ASC' | 'DESC';
    } = {},
  ): Promise<PaginatedResult<DocumentAccessLog>> {
    const {
      page = 1,
      limit = 10,
      documentId,
      userId,
      accessType,
      success,
      startDate,
      endDate,
      sortBy = 'createdAt',
      sortDirection = 'DESC',
    } = options;

    const queryBuilder = this.repository.createQueryBuilder('log');

    // Add tenantId filtering - REQUIRED for tenant isolation
    queryBuilder.andWhere('log.tenantId = :tenantId', { tenantId });

    // Áp dụng bộ lọc documentId nếu được cung cấp
    if (documentId) {
      queryBuilder.andWhere('log.documentId = :documentId', { documentId });
    }

    // Áp dụng bộ lọc userId nếu được cung cấp
    if (userId) {
      queryBuilder.andWhere('log.userId = :userId', { userId });
    }

    // Áp dụng bộ lọc accessType nếu được cung cấp
    if (accessType) {
      queryBuilder.andWhere('log.accessType = :accessType', { accessType });
    }

    // Áp dụng bộ lọc success nếu được cung cấp
    if (success !== undefined) {
      queryBuilder.andWhere('log.success = :success', { success });
    }

    // Áp dụng bộ lọc thời gian
    if (startDate) {
      queryBuilder.andWhere('log.createdAt >= :startDate', { startDate });
    }

    if (endDate) {
      queryBuilder.andWhere('log.createdAt <= :endDate', { endDate });
    }

    // Sắp xếp
    queryBuilder.orderBy(`log.${sortBy}`, sortDirection);

    // Phân trang
    const offset = (page - 1) * limit;
    queryBuilder.skip(offset).take(limit);

    const [items, total] = await queryBuilder.getManyAndCount();

    return {
      items,
      meta: {
        totalItems: total,
        itemCount: items.length,
        itemsPerPage: limit,
        totalPages: Math.ceil(total / limit),
        currentPage: page,
      },
    };
  }

  /**
   * Tìm log theo ID
   * @param tenantId ID tenant (required for tenant isolation)
   * @param id ID log
   * @returns Log hoặc null
   */
  async findById(
    tenantId: number,
    id: number,
  ): Promise<DocumentAccessLog | null> {
    return this.repository.findOne({
      where: { id, tenantId },
    });
  }

  /**
   * Tìm logs của tài liệu
   * @param tenantId ID tenant (required for tenant isolation)
   * @param documentId ID tài liệu
   * @param limit Giới hạn số lượng
   * @returns Danh sách logs
   */
  async findByDocument(
    tenantId: number,
    documentId: number,
    limit?: number,
  ): Promise<DocumentAccessLog[]> {
    const queryBuilder = this.repository.createQueryBuilder('log');

    queryBuilder
      .where('log.tenantId = :tenantId', { tenantId })
      .andWhere('log.documentId = :documentId', { documentId })
      .orderBy('log.createdAt', 'DESC');

    if (limit) {
      queryBuilder.take(limit);
    }

    return queryBuilder.getMany();
  }

  /**
   * Tìm logs của user
   * @param tenantId ID tenant (required for tenant isolation)
   * @param userId ID user
   * @param limit Giới hạn số lượng
   * @returns Danh sách logs
   */
  async findByUser(
    tenantId: number,
    userId: number,
    limit?: number,
  ): Promise<DocumentAccessLog[]> {
    const queryBuilder = this.repository.createQueryBuilder('log');

    queryBuilder
      .where('log.tenantId = :tenantId', { tenantId })
      .andWhere('log.userId = :userId', { userId })
      .orderBy('log.createdAt', 'DESC');

    if (limit) {
      queryBuilder.take(limit);
    }

    return queryBuilder.getMany();
  }

  /**
   * Tìm RAG query logs
   * @param tenantId ID tenant (required for tenant isolation)
   * @param options Tùy chọn truy vấn
   * @returns Danh sách RAG logs đã phân trang
   */
  async findRagQueries(
    tenantId: number,
    options: {
      page?: number;
      limit?: number;
      userId?: number;
      minConfidence?: number;
      startDate?: number;
      endDate?: number;
    } = {},
  ): Promise<PaginatedResult<DocumentAccessLog>> {
    const {
      page = 1,
      limit = 10,
      userId,
      minConfidence,
      startDate,
      endDate,
    } = options;

    const queryBuilder = this.repository.createQueryBuilder('log');

    // Add tenantId filtering - REQUIRED for tenant isolation
    queryBuilder.andWhere('log.tenantId = :tenantId', { tenantId });
    queryBuilder.andWhere('log.accessType = :accessType', {
      accessType: 'rag_query',
    });

    // Áp dụng bộ lọc userId nếu được cung cấp
    if (userId) {
      queryBuilder.andWhere('log.userId = :userId', { userId });
    }

    // Áp dụng bộ lọc confidence tối thiểu
    if (minConfidence !== undefined) {
      queryBuilder.andWhere('log.queryConfidence >= :minConfidence', {
        minConfidence,
      });
    }

    // Áp dụng bộ lọc thời gian
    if (startDate) {
      queryBuilder.andWhere('log.createdAt >= :startDate', { startDate });
    }

    if (endDate) {
      queryBuilder.andWhere('log.createdAt <= :endDate', { endDate });
    }

    // Sắp xếp theo thời gian tạo
    queryBuilder.orderBy('log.createdAt', 'DESC');

    // Phân trang
    const offset = (page - 1) * limit;
    queryBuilder.skip(offset).take(limit);

    const [items, total] = await queryBuilder.getManyAndCount();

    return {
      items,
      meta: {
        totalItems: total,
        itemCount: items.length,
        itemsPerPage: limit,
        totalPages: Math.ceil(total / limit),
        currentPage: page,
      },
    };
  }

  /**
   * Tìm logs chậm (slow queries)
   * @param tenantId ID tenant (required for tenant isolation)
   * @param minResponseTime Thời gian phản hồi tối thiểu (ms)
   * @param limit Giới hạn số lượng
   * @returns Danh sách logs chậm
   */
  async findSlowQueries(
    tenantId: number,
    minResponseTime: number = 3000,
    limit?: number,
  ): Promise<DocumentAccessLog[]> {
    const queryBuilder = this.repository.createQueryBuilder('log');

    queryBuilder
      .where('log.tenantId = :tenantId', { tenantId })
      .andWhere('log.responseTimeMs >= :minResponseTime', { minResponseTime })
      .orderBy('log.responseTimeMs', 'DESC');

    if (limit) {
      queryBuilder.take(limit);
    }

    return queryBuilder.getMany();
  }

  /**
   * Tìm logs lỗi
   * @param tenantId ID tenant (required for tenant isolation)
   * @param limit Giới hạn số lượng
   * @returns Danh sách logs lỗi
   */
  async findErrors(
    tenantId: number,
    limit?: number,
  ): Promise<DocumentAccessLog[]> {
    const queryBuilder = this.repository.createQueryBuilder('log');

    queryBuilder
      .where('log.tenantId = :tenantId', { tenantId })
      .andWhere('log.success = false')
      .orderBy('log.createdAt', 'DESC');

    if (limit) {
      queryBuilder.take(limit);
    }

    return queryBuilder.getMany();
  }

  /**
   * Xóa logs cũ
   * @param tenantId ID tenant (required for tenant isolation)
   * @param olderThan Timestamp - xóa logs cũ hơn thời điểm này
   * @returns Số logs đã xóa
   */
  async deleteOldLogs(tenantId: number, olderThan: number): Promise<number> {
    const result = await this.repository.delete({
      tenantId,
      createdAt: { $lt: olderThan } as any,
    });
    return result.affected || 0;
  }

  /**
   * Lấy thống kê truy cập
   * @param tenantId ID tenant (required for tenant isolation)
   * @param startDate Ngày bắt đầu
   * @param endDate Ngày kết thúc
   * @returns Thống kê truy cập
   */
  async getAccessStats(
    tenantId: number,
    startDate?: number,
    endDate?: number,
  ): Promise<{
    totalAccess: number;
    successfulAccess: number;
    failedAccess: number;
    ragQueries: number;
    avgResponseTime: number;
    accessByType: Record<AccessType, number>;
  }> {
    const queryBuilder = this.repository.createQueryBuilder('log');

    queryBuilder.where('log.tenantId = :tenantId', { tenantId });

    if (startDate) {
      queryBuilder.andWhere('log.createdAt >= :startDate', { startDate });
    }

    if (endDate) {
      queryBuilder.andWhere('log.createdAt <= :endDate', { endDate });
    }

    const [
      totalAccess,
      successfulAccess,
      failedAccess,
      ragQueries,
      avgResponseTimeResult,
      accessByTypeResult,
    ] = await Promise.all([
      queryBuilder.getCount(),
      queryBuilder.clone().andWhere('log.success = true').getCount(),
      queryBuilder.clone().andWhere('log.success = false').getCount(),
      queryBuilder
        .clone()
        .andWhere('log.accessType = :accessType', { accessType: 'rag_query' })
        .getCount(),
      queryBuilder
        .clone()
        .select('AVG(log.responseTimeMs)', 'avgResponseTime')
        .getRawOne(),
      queryBuilder
        .clone()
        .select('log.accessType', 'type')
        .addSelect('COUNT(*)', 'count')
        .groupBy('log.accessType')
        .getRawMany(),
    ]);

    const accessByType = {} as Record<AccessType, number>;
    accessByTypeResult.forEach((row) => {
      accessByType[row.type as AccessType] = parseInt(row.count);
    });

    return {
      totalAccess,
      successfulAccess,
      failedAccess,
      ragQueries,
      avgResponseTime: parseFloat(avgResponseTimeResult?.avgResponseTime) || 0,
      accessByType,
    };
  }

  /**
   * Lấy top queries
   * @param tenantId ID tenant (required for tenant isolation)
   * @param limit Giới hạn số lượng
   * @returns Top queries
   */
  async getTopQueries(
    tenantId: number,
    limit: number = 10,
  ): Promise<
    {
      queryText: string;
      count: number;
      avgConfidence: number;
    }[]
  > {
    const result = await this.repository
      .createQueryBuilder('log')
      .select('log.queryText', 'queryText')
      .addSelect('COUNT(*)', 'count')
      .addSelect('AVG(log.queryConfidence)', 'avgConfidence')
      .where('log.tenantId = :tenantId', { tenantId })
      .andWhere('log.accessType = :accessType', { accessType: 'rag_query' })
      .andWhere('log.queryText IS NOT NULL')
      .groupBy('log.queryText')
      .orderBy('count', 'DESC')
      .limit(limit)
      .getRawMany();

    return result.map((row) => ({
      queryText: row.queryText,
      count: parseInt(row.count),
      avgConfidence: parseFloat(row.avgConfidence) || 0,
    }));
  }
}
