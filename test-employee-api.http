### Test Employee API with Department Information

# Lấy danh sách employees với thông tin department
GET http://localhost:3001/api/hrm/employees?page=1&limit=10
Authorization: Bearer YOUR_JWT_TOKEN_HERE
Content-Type: application/json

###

# Lấy danh sách employees với filter theo department
GET http://localhost:3001/api/hrm/employees?page=1&limit=10&departmentId=1
Authorization: Bearer YOUR_JWT_TOKEN_HERE
Content-Type: application/json

###

# Lấy danh sách employees với search
GET http://localhost:3001/api/hrm/employees?page=1&limit=10&search=nguyen
Authorization: Bearer YOUR_JWT_TOKEN_HERE
Content-Type: application/json

###

# Lấy thông tin chi tiết một employee
GET http://localhost:3001/api/hrm/employees/1
Authorization: Bearer YOUR_JWT_TOKEN_HERE
Content-Type: application/json
