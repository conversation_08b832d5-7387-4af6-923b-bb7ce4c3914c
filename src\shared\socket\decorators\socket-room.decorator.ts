import { createParamDecorator, ExecutionContext } from '@nestjs/common';
import { SocketClient } from '../interfaces/socket-client.interface';

/**
 * Decorator để lấy thông tin phòng từ socket
 *
 * @example
 * ```typescript
 * @SubscribeMessage('message')
 * handleMessage(@MessageBody() data: any, @SocketRoom() room: string) {
 *   console.log(`Message in room ${room}: ${data.content}`);
 * }
 * ```
 */
export const SocketRoom = createParamDecorator(
  (data: unknown, ctx: ExecutionContext) => {
    const client = ctx.switchToWs().getClient<SocketClient>();
    const event = ctx.switchToWs().getData();
    return event.roomId || null;
  },
);
