import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { TodoScore } from '../entities/todo-score.entity';
import { PaginatedResult } from '@/common/response/api-response-dto';
import { QueryDto } from '@/common/dto/query.dto';

/**
 * Repository cho entity TodoScore
 */
@Injectable()
export class TodoScoreRepository {
  constructor(
    @InjectRepository(TodoScore)
    private readonly repository: Repository<TodoScore>,
  ) {}

  /**
   * Tạo bản ghi chấm điểm mới
   * @param tenantId ID tenant (required for tenant isolation)
   * @param data Dữ liệu chấm điểm
   * @returns Bản ghi chấm điểm đã tạo
   */
  async create(tenantId: number, data: Partial<TodoScore>): Promise<TodoScore> {
    const todoScore = this.repository.create({ ...data, tenantId });
    return this.repository.save(todoScore);
  }

  /**
   * Tìm bản ghi chấm điểm theo ID
   * @param tenantId ID tenant (required for tenant isolation)
   * @param id ID bản ghi chấm điểm
   * @returns Bản ghi chấm điểm hoặc null nếu không tìm thấy
   */
  async findById(tenantId: number, id: number): Promise<TodoScore | null> {
    return this.repository.findOne({
      where: { id, tenantId },
    });
  }

  /**
   * Tìm tất cả bản ghi chấm điểm của một công việc
   * @param tenantId ID tenant (required for tenant isolation)
   * @param todoId ID công việc
   * @returns Danh sách bản ghi chấm điểm
   */
  async findByTodoId(tenantId: number, todoId: number): Promise<TodoScore[]> {
    return this.repository.find({
      where: { todoId, tenantId },
      order: { createdAt: 'DESC' },
    });
  }

  /**
   * Tìm bản ghi chấm điểm mới nhất của một công việc
   * @param tenantId ID tenant (required for tenant isolation)
   * @param todoId ID công việc
   * @returns Bản ghi chấm điểm mới nhất hoặc null nếu không tìm thấy
   */
  async findLatestByTodoId(
    tenantId: number,
    todoId: number,
  ): Promise<TodoScore | null> {
    return this.repository.findOne({
      where: { todoId, tenantId },
      order: { createdAt: 'DESC' },
    });
  }

  /**
   * Tìm tất cả bản ghi chấm điểm của một người chấm
   * @param tenantId ID tenant (required for tenant isolation)
   * @param scorerId ID người chấm
   * @param queryDto Tham số truy vấn
   * @returns Danh sách phân trang các bản ghi chấm điểm
   */
  async findByScorerIdWithPagination(
    tenantId: number,
    scorerId: number,
    queryDto: QueryDto,
  ): Promise<PaginatedResult<TodoScore>> {
    const {
      page = 1,
      limit = 10,
      sortBy = 'createdAt',
      sortDirection = 'DESC',
    } = queryDto;

    const [items, totalItems] = await this.repository.findAndCount({
      where: { scorerId, tenantId },
      order: { [sortBy]: sortDirection },
      skip: (page - 1) * limit,
      take: limit,
    });

    return {
      items,
      meta: {
        totalItems,
        itemCount: items.length,
        itemsPerPage: limit,
        totalPages: Math.ceil(totalItems / limit),
        currentPage: page,
      },
    };
  }

  /**
   * Xóa bản ghi chấm điểm
   * @param tenantId ID tenant (required for tenant isolation)
   * @param id ID bản ghi chấm điểm
   * @returns true nếu xóa thành công
   */
  async delete(tenantId: number, id: number): Promise<boolean> {
    const result = await this.repository.delete({ id, tenantId });
    return (
      result.affected !== null &&
      result.affected !== undefined &&
      result.affected > 0
    );
  }
}
