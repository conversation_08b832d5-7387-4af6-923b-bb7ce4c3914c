import { ApiProperty } from '@nestjs/swagger';

/**
 * DTO cho thống kê tổng quan nhân viên
 */
export class EmployeeOverviewResponseDto {
  /**
   * Tổng số nhân viên trong hệ thống
   * @example 150
   */
  @ApiProperty({
    description: 'Tổng số nhân viên trong hệ thống',
    example: 150,
  })
  totalEmployees: number;

  /**
   * Tổng số tài khoản người dùng có liên kết với nhân viên
   * @example 120
   */
  @ApiProperty({
    description: 'Tổng số tài khoản người dùng có liên kết với nhân viên',
    example: 120,
  })
  totalUserAccounts: number;

  /**
   * Số lượng nhân viên đang hoạt động
   * @example 140
   */
  @ApiProperty({
    description: 'Số lượng nhân viên đang hoạt động (status = active)',
    example: 140,
  })
  activeEmployees: number;

  /**
   * <PERSON><PERSON> lượng nhân viên không hoạt động
   * @example 5
   */
  @ApiProperty({
    description: 'Số lượng nhân viên không hoạt động (status = inactive)',
    example: 5,
  })
  inactiveEmployees: number;

  /**
   * Số lượng nhân viên mới được tuyển trong tháng này
   * @example 8
   */
  @ApiProperty({
    description: 'Số lượng nhân viên mới được tuyển trong tháng này',
    example: 8,
  })
  newEmployeesThisMonth: number;

  /**
   * Số lượng nhân viên đang trong thời gian thử việc
   * @example 12
   */
  @ApiProperty({
    description: 'Số lượng nhân viên đang trong thời gian thử việc (status = probation)',
    example: 12,
  })
  probationEmployees: number;

  /**
   * Phần trăm nhân viên hoạt động so với tổng số
   * @example 93.33
   */
  @ApiProperty({
    description: 'Phần trăm nhân viên hoạt động so với tổng số',
    example: 93.33,
  })
  activeEmployeePercentage: number;

  /**
   * Phần trăm nhân viên có tài khoản so với tổng số
   * @example 80.0
   */
  @ApiProperty({
    description: 'Phần trăm nhân viên có tài khoản so với tổng số',
    example: 80.0,
  })
  accountCoveragePercentage: number;
}
