import { Controller, Post, Body } from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiExtraModels,
} from '@nestjs/swagger';
import { SocialAuthService } from '../services/social-auth.service';
import { SocialLoginDto, SocialRegisterDto } from '../dto/social-auth.dto';
import { ApiResponseDto } from '@/common/response/api-response-dto';
import { UserLoginResponseDto } from '../dto/user-response.dto';
import { SWAGGER_API_TAG } from '@/common/swagger/swagger.tags';

/**
 * Controller xử lý xác thực qua mạng xã hội
 */
@ApiTags(SWAGGER_API_TAG.AUTHENTICATION)
@ApiExtraModels(ApiResponseDto, UserLoginResponseDto)
@Controller('api/auth/social')
export class SocialAuthController {
  constructor(private readonly socialAuthService: SocialAuthService) {}

  /**
   * Đăng nhập bằng mạng xã hội
   */
  @Post('login')
  @ApiOperation({
    summary: 'Đăng nhập bằng mạng xã hội (Google, Facebook, Zalo)',
  })
  @ApiResponse({
    status: 200,
    description: 'Đăng nhập thành công',
    schema: ApiResponseDto.getSchema(UserLoginResponseDto),
  })
  @ApiResponse({
    status: 400,
    description: 'Dữ liệu không hợp lệ',
  })
  @ApiResponse({
    status: 401,
    description: 'Token mạng xã hội không hợp lệ',
  })
  @ApiResponse({
    status: 404,
    description: 'Tài khoản mạng xã hội chưa được liên kết',
  })
  async login(@Body() loginDto: SocialLoginDto) {
    return this.socialAuthService.login(loginDto);
  }

  /**
   * Đăng ký bằng mạng xã hội
   */
  @Post('register')
  @ApiOperation({
    summary: 'Đăng ký bằng mạng xã hội (Google, Facebook, Zalo)',
  })
  @ApiResponse({
    status: 201,
    description: 'Đăng ký thành công',
    schema: ApiResponseDto.getSchema(UserLoginResponseDto),
  })
  @ApiResponse({
    status: 400,
    description: 'Dữ liệu không hợp lệ',
  })
  @ApiResponse({
    status: 401,
    description: 'Token mạng xã hội không hợp lệ',
  })
  @ApiResponse({
    status: 409,
    description: 'Tài khoản mạng xã hội đã được liên kết hoặc email đã tồn tại',
  })
  async register(@Body() registerDto: SocialRegisterDto) {
    return this.socialAuthService.register(registerDto);
  }
}
