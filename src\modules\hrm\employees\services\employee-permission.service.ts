import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, In } from 'typeorm';
import { Permission } from '@/modules/auth/entities/permission.entity';
import { Role } from '@/modules/auth/entities/role.entity';
import { UserRole } from '@/modules/auth/entities/user-role.entity';
import { UserPermission } from '@/modules/auth/entities/user-permission.entity';
import { RolePermission } from '@/modules/auth/entities/role-permission.entity';
import { UserRepository } from '@/modules/auth/repositories/user.repository';
import { EmployeeRepository } from '../repositories/employee.repository';
import { EmployeePermissionRepository } from '../repositories/employee-permission.repository';
import { AppException } from '@/common/exceptions/app.exception';
import { HRM_ERROR_CODES } from '../../errors/hrm-error.code';
import {
  PermissionGroupDto,
  PermissionDto,
  PermissionGroupsResponseDto,
  UserPermissionsResponseDto,
} from '../dto/permission/permission-group.dto';
import {
  UpdateEmployeeRoleDto,
  UpdateEmployeeRoleResponseDto,
} from '../dto/permission/update-employee-role.dto';
import {
  UpdateEmployeePermissionDto,
  UpdateEmployeePermissionResponseDto,
} from '../dto/permission/update-employee-permission.dto';
import {
  UpdateRolePermissionDto,
  UpdateRolePermissionResponseDto,
} from '../dto/permission/update-role-permission.dto';

/**
 * Service quản lý phân quyền cho nhân viên
 */
@Injectable()
export class EmployeePermissionService {
  private readonly logger = new Logger(EmployeePermissionService.name);

  constructor(
    private readonly employeePermissionRepository: EmployeePermissionRepository,
    private readonly userRepository: UserRepository,
    private readonly employeeRepository: EmployeeRepository,
    @InjectRepository(Role)
    private readonly roleRepository: Repository<Role>,
    @InjectRepository(UserRole)
    private readonly userRoleRepository: Repository<UserRole>,
    @InjectRepository(UserPermission)
    private readonly userPermissionRepository: Repository<UserPermission>,
    @InjectRepository(RolePermission)
    private readonly rolePermissionRepository: Repository<RolePermission>,
    @InjectRepository(Permission)
    private readonly permissionRepository: Repository<Permission>,
  ) {}

  /**
   * Lấy danh sách tất cả các quyền được nhóm theo module
   * @returns Danh sách các nhóm quyền
   */
  async getPermissionGroups(): Promise<PermissionGroupsResponseDto> {
    try {
      // Lấy tất cả các quyền từ database
      const permissions = await this.permissionRepository.find();

      // Nhóm các quyền theo module
      const moduleMap = new Map<string, PermissionDto[]>();

      permissions.forEach((permission: Permission) => {
        const permissionDto: PermissionDto = {
          id: permission.id,
          module: permission.module,
          action: permission.action,
          description: permission.description,
          key: `${permission.module}:${permission.action}`,
        };

        if (!moduleMap.has(permission.module)) {
          moduleMap.set(permission.module, []);
        }

        moduleMap.get(permission.module)?.push(permissionDto);
      });

      // Chuyển đổi Map thành mảng các nhóm
      const groups: PermissionGroupDto[] = [];
      moduleMap.forEach((permissions, module) => {
        groups.push({
          module,
          permissions,
        });
      });

      // Sắp xếp các nhóm theo tên module
      groups.sort((a, b) => a.module.localeCompare(b.module));

      return { groups };
    } catch (error) {
      this.logger.error(
        `Error getting permission groups: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        HRM_ERROR_CODES.PERMISSION_FETCH_FAILED,
        `Không thể lấy danh sách quyền: ${error.message}`,
      );
    }
  }

  /**
   * Lấy danh sách quyền của một người dùng
   * @param userId ID của người dùng
   * @returns Danh sách quyền của người dùng
   */
  async getUserPermissions(
    userId: number,
  ): Promise<UserPermissionsResponseDto> {
    try {
      // Kiểm tra người dùng tồn tại
      const user = await this.userRepository.findById(userId);
      if (!user) {
        throw new AppException(
          HRM_ERROR_CODES.USER_NOT_FOUND,
          `Không tìm thấy người dùng với ID ${userId}`,
        );
      }

      // Lấy danh sách quyền của người dùng
      const permissions =
        await this.employeePermissionRepository.getUserPermissions(userId);

      return {
        userId,
        permissions,
      };
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(
        `Error getting user permissions: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        HRM_ERROR_CODES.PERMISSION_FETCH_FAILED,
        `Không thể lấy danh sách quyền của người dùng: ${error.message}`,
      );
    }
  }

  /**
   * Cập nhật vai trò cho nhân viên
   * @param tenantId ID tenant (required for tenant isolation)
   * @param dto Dữ liệu cập nhật vai trò
   * @param currentUserId ID của người dùng thực hiện cập nhật
   * @returns Thông tin sau khi cập nhật
   */
  async updateEmployeeRole(
    tenantId: number,
    dto: UpdateEmployeeRoleDto,
    currentUserId: number,
  ): Promise<UpdateEmployeeRoleResponseDto> {
    try {
      // Kiểm tra nhân viên tồn tại
      const employee = await this.employeeRepository.findById(
        tenantId,
        dto.employeeId,
      );
      if (!employee) {
        throw new AppException(
          HRM_ERROR_CODES.EMPLOYEE_NOT_FOUND,
          `Không tìm thấy nhân viên với ID ${dto.employeeId}`,
        );
      }

      // Kiểm tra các vai trò tồn tại
      if (dto.roleIds.length > 0) {
        const roles = await this.roleRepository.find({
          where: { id: In(dto.roleIds) },
        });

        if (roles.length !== dto.roleIds.length) {
          throw new AppException(
            HRM_ERROR_CODES.ROLE_NOT_FOUND,
            'Một hoặc nhiều vai trò không tồn tại',
          );
        }
      }

      // Tìm user tương ứng với employee này
      const userForEmployee = await this.userRepository.findByEmployeeId(
        employee.id,
      );
      if (!userForEmployee) {
        throw new AppException(
          HRM_ERROR_CODES.USER_NOT_FOUND,
          `Không tìm thấy tài khoản người dùng cho nhân viên ID ${dto.employeeId}`,
        );
      }

      // Xóa tất cả các vai trò hiện tại của người dùng
      await this.userRoleRepository.delete({ userId: userForEmployee.id });

      // Thêm các vai trò mới
      const now = Date.now();
      const userRoles = dto.roleIds.map((roleId) => {
        return this.userRoleRepository.create({
          userId: userForEmployee.id,
          roleId,
          createdAt: now,
        });
      });

      if (userRoles.length > 0) {
        await this.userRoleRepository.save(userRoles);
      }

      return {
        employeeId: dto.employeeId,
        userId: userForEmployee.id,
        roleIds: dto.roleIds,
      };
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(
        `Error updating employee role: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        HRM_ERROR_CODES.ROLE_UPDATE_FAILED,
        `Không thể cập nhật vai trò cho nhân viên: ${error.message}`,
      );
    }
  }

  /**
   * Cập nhật quyền trực tiếp cho nhân viên
   * @param tenantId ID tenant (required for tenant isolation)
   * @param dto Dữ liệu cập nhật quyền
   * @param currentUserId ID của người dùng thực hiện cập nhật
   * @returns Thông tin sau khi cập nhật
   */
  async updateEmployeePermission(
    tenantId: number,
    dto: UpdateEmployeePermissionDto,
    currentUserId: number,
  ): Promise<UpdateEmployeePermissionResponseDto> {
    try {
      // Kiểm tra nhân viên tồn tại
      const employee = await this.employeeRepository.findById(
        tenantId,
        dto.employeeId,
      );
      if (!employee) {
        throw new AppException(
          HRM_ERROR_CODES.EMPLOYEE_NOT_FOUND,
          `Không tìm thấy nhân viên với ID ${dto.employeeId}`,
        );
      }

      // Kiểm tra các quyền tồn tại
      if (dto.permissionIds.length > 0) {
        const permissions = await this.permissionRepository.find();
        const existingPermissionIds = permissions.map((p: Permission) => p.id);

        const invalidPermissionIds = dto.permissionIds.filter(
          (id) => !existingPermissionIds.includes(id),
        );

        if (invalidPermissionIds.length > 0) {
          throw new AppException(
            HRM_ERROR_CODES.PERMISSION_NOT_FOUND,
            `Một hoặc nhiều quyền không tồn tại: ${invalidPermissionIds.join(', ')}`,
          );
        }
      }

      // Tìm user tương ứng với employee này
      const userForEmployee = await this.userRepository.findByEmployeeId(
        employee.id,
      );
      if (!userForEmployee) {
        throw new AppException(
          HRM_ERROR_CODES.USER_NOT_FOUND,
          `Không tìm thấy tài khoản người dùng cho nhân viên ID ${dto.employeeId}`,
        );
      }

      // Xóa tất cả các quyền trực tiếp hiện tại của người dùng
      await this.userPermissionRepository.delete({
        userId: userForEmployee.id,
      });

      // Thêm các quyền mới
      const now = Date.now();
      const userPermissions = dto.permissionIds.map((permissionId) => {
        return this.userPermissionRepository.create({
          userId: userForEmployee.id,
          permissionId,
          createdAt: now,
        });
      });

      if (userPermissions.length > 0) {
        await this.userPermissionRepository.save(userPermissions);
      }

      return {
        employeeId: dto.employeeId,
        userId: userForEmployee.id,
        permissionIds: dto.permissionIds,
      };
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(
        `Error updating employee permission: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        HRM_ERROR_CODES.PERMISSION_UPDATE_FAILED,
        `Không thể cập nhật quyền cho nhân viên: ${error.message}`,
      );
    }
  }

  /**
   * Cập nhật quyền cho vai trò
   * @param dto Dữ liệu cập nhật quyền cho vai trò
   * @param currentUserId ID của người dùng thực hiện cập nhật
   * @returns Thông tin sau khi cập nhật
   */
  async updateRolePermission(
    dto: UpdateRolePermissionDto,
    currentUserId: number,
  ): Promise<UpdateRolePermissionResponseDto> {
    try {
      // Kiểm tra vai trò tồn tại
      const role = await this.roleRepository.findOne({
        where: { id: dto.roleId },
      });

      if (!role) {
        throw new AppException(
          HRM_ERROR_CODES.ROLE_NOT_FOUND,
          `Không tìm thấy vai trò với ID ${dto.roleId}`,
        );
      }

      // Kiểm tra các quyền tồn tại
      if (dto.permissionIds.length > 0) {
        const permissions = await this.permissionRepository.find();
        const existingPermissionIds = permissions.map((p: Permission) => p.id);

        const invalidPermissionIds = dto.permissionIds.filter(
          (id) => !existingPermissionIds.includes(id),
        );

        if (invalidPermissionIds.length > 0) {
          throw new AppException(
            HRM_ERROR_CODES.PERMISSION_NOT_FOUND,
            `Một hoặc nhiều quyền không tồn tại: ${invalidPermissionIds.join(', ')}`,
          );
        }
      }

      // Xóa tất cả các quyền hiện tại của vai trò
      await this.rolePermissionRepository.delete({ roleId: dto.roleId });

      // Thêm các quyền mới
      const now = Date.now();
      const rolePermissions = dto.permissionIds.map((permissionId) => {
        return this.rolePermissionRepository.create({
          roleId: dto.roleId,
          permissionId,
          createdAt: now,
        });
      });

      if (rolePermissions.length > 0) {
        await this.rolePermissionRepository.save(rolePermissions);
      }

      return {
        roleId: dto.roleId,
        roleName: role.name,
        permissionIds: dto.permissionIds,
      };
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(
        `Error updating role permission: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        HRM_ERROR_CODES.PERMISSION_UPDATE_FAILED,
        `Không thể cập nhật quyền cho vai trò: ${error.message}`,
      );
    }
  }
}
