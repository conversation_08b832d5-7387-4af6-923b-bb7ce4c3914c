# Task ID: 14
# Title: Update Calendar Module - Services and Controllers
# Status: done
# Dependencies: 13
# Priority: medium
# Description: Update Calendar services and controllers for tenantId handling
# Details:
Update service and controller layers:
- EventService: Add tenantId parameter to all methods
- CalendarService: Update to pass tenantId to repository
- EventController: Add @CurrentTenant() to all endpoints
- Update calendar view generation with tenantId filtering
- Ensure event sharing respects tenant boundaries

# Test Strategy:
Integration and API tests for Calendar module
