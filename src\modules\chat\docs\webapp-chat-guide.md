# Webapp Chat với AI Agent - Hướng dẫn sử dụng

## Tổng quan

Module Webapp Chat cung cấp tích hợp WebSocket và REST API để xây dựng hệ thống chat với AI agent cho webapp. Hệ thống hỗ trợ:

- Chat real-time qua WebSocket
- AI agent tự động trả lời
- Quản lý cuộc hội thoại
- Lịch sử tin nhắn
- Tenant isolation

## Kiến trúc

```
Frontend (Webapp) 
    ↓ WebSocket/REST API
WebappChatController & WebappChatGateway
    ↓
WebappChatService
    ↓
ConversationRepository & MessageRepository
    ↓
AIOrchestatorService → RAGService → OpenAI
```

## API Endpoints

### REST API

#### 1. Tạo cuộc hội thoại mới
```http
POST /webapp-chat/conversations
Authorization: Bearer <jwt_token>
Content-Type: application/json

{
  "language": "vi",
  "metadata": {
    "source": "webapp",
    "userAgent": "Mozilla/5.0..."
  }
}

Response:
{
  "code": 201,
  "message": "<PERSON><PERSON><PERSON><PERSON> hội thoại đã được tạo thành công",
  "result": {
    "conversationId": 123,
    "status": "active",
    "language": "vi",
    "createdAt": 1640995200000
  }
}
```

#### 2. Lấy cuộc hội thoại active
```http
GET /webapp-chat/conversations/active
Authorization: Bearer <jwt_token>
```

#### 3. Gửi tin nhắn
```http
POST /webapp-chat/messages
Authorization: Bearer <jwt_token>
Content-Type: application/json

{
  "content": "Xin chào, tôi cần hỗ trợ",
  "type": "text",
  "conversationId": 123,
  "metadata": {
    "fileUrl": "https://example.com/file.pdf",
    "fileName": "document.pdf"
  }
}
```

#### 4. Lấy lịch sử cuộc hội thoại
```http
GET /webapp-chat/conversations/123/history?page=1&limit=50
Authorization: Bearer <jwt_token>

Response:
{
  "conversationId": 123,
  "status": "active",
  "language": "vi",
  "createdAt": 1640995200000,
  "lastMessageAt": 1640995800000,
  "messages": [
    {
      "id": 456,
      "content": "Xin chào AI agent",
      "type": "text",
      "isAiGenerated": false,
      "timestamp": 1640995200000
    },
    {
      "id": 457,
      "content": "Xin chào! Tôi có thể giúp gì cho bạn?",
      "type": "text",
      "isAiGenerated": true,
      "aiConfidence": 0.95,
      "detectedIntent": "greeting",
      "timestamp": 1640995210000
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 50,
    "total": 25,
    "totalPages": 1
  }
}
```

#### 5. Đóng cuộc hội thoại
```http
POST /webapp-chat/conversations/123/close
Authorization: Bearer <jwt_token>
```

#### 6. Kiểm tra trạng thái AI agent
```http
GET /webapp-chat/agent/status
Authorization: Bearer <jwt_token>
```

### WebSocket Events

#### Kết nối
```javascript
const socket = io('/webapp-chat', {
  auth: {
    token: 'your_jwt_token'
  }
});
```

#### Sự kiện gửi tin nhắn
```javascript
// Gửi tin nhắn
socket.emit('webapp_chat:send_message', {
  content: 'Xin chào AI agent',
  type: 'text',
  conversationId: 123
});

// Nhận phản hồi từ AI
socket.on('webapp_chat:ai_response', (response) => {
  console.log('AI Response:', response);
  // {
  //   messageId: "msg_123",
  //   content: "Xin chào! Tôi có thể giúp gì cho bạn?",
  //   type: "text",
  //   confidence: 0.95,
  //   intent: "greeting",
  //   timestamp: 1640995200000,
  //   conversationId: 123
  // }
});
```

#### Sự kiện cuộc hội thoại
```javascript
// Tham gia cuộc hội thoại
socket.emit('webapp_chat:join_conversation', {
  conversationId: 123
});

socket.on('webapp_chat:conversation_joined', (data) => {
  console.log('Joined conversation:', data);
});

// Rời cuộc hội thoại
socket.emit('webapp_chat:leave_conversation', {
  conversationId: 123
});
```

#### Sự kiện trạng thái gõ
```javascript
// Bắt đầu gõ
socket.emit('webapp_chat:typing_start', {
  conversationId: 123
});

// Dừng gõ
socket.emit('webapp_chat:typing_stop', {
  conversationId: 123
});

// Nhận thông báo AI đang gõ
socket.on('webapp_chat:ai_typing', (data) => {
  if (data.isTyping) {
    showTypingIndicator();
  } else {
    hideTypingIndicator();
  }
});
```

#### Sự kiện lỗi
```javascript
socket.on('webapp_chat:error', (error) => {
  console.error('Chat error:', error);
});
```

## Frontend Integration

### React Example

```jsx
import { useEffect, useState } from 'react';
import io from 'socket.io-client';

const ChatComponent = () => {
  const [socket, setSocket] = useState(null);
  const [messages, setMessages] = useState([]);
  const [inputMessage, setInputMessage] = useState('');
  const [conversationId, setConversationId] = useState(null);
  const [isAiTyping, setIsAiTyping] = useState(false);

  useEffect(() => {
    // Kết nối WebSocket
    const newSocket = io('/webapp-chat', {
      auth: {
        token: localStorage.getItem('jwt_token')
      }
    });

    // Lắng nghe sự kiện
    newSocket.on('webapp_chat:connection_status', (data) => {
      console.log('Connected:', data);
    });

    newSocket.on('webapp_chat:ai_response', (response) => {
      setMessages(prev => [...prev, {
        id: response.messageId,
        content: response.content,
        type: 'ai',
        timestamp: response.timestamp
      }]);
      setIsAiTyping(false);
    });

    newSocket.on('webapp_chat:ai_typing', (data) => {
      setIsAiTyping(data.isTyping);
    });

    newSocket.on('webapp_chat:conversation_joined', (data) => {
      setConversationId(data.conversationId);
    });

    setSocket(newSocket);

    // Tham gia cuộc hội thoại
    newSocket.emit('webapp_chat:join_conversation', {});

    return () => newSocket.close();
  }, []);

  const sendMessage = () => {
    if (!inputMessage.trim() || !socket) return;

    // Thêm tin nhắn của user vào UI
    const userMessage = {
      id: Date.now(),
      content: inputMessage,
      type: 'user',
      timestamp: Date.now()
    };
    setMessages(prev => [...prev, userMessage]);

    // Gửi tin nhắn qua WebSocket
    socket.emit('webapp_chat:send_message', {
      content: inputMessage,
      type: 'text',
      conversationId
    });

    setInputMessage('');
    setIsAiTyping(true);
  };

  return (
    <div className="chat-container">
      <div className="messages">
        {messages.map(msg => (
          <div key={msg.id} className={`message ${msg.type}`}>
            <div className="content">{msg.content}</div>
            <div className="timestamp">
              {new Date(msg.timestamp).toLocaleTimeString()}
            </div>
          </div>
        ))}
        {isAiTyping && (
          <div className="typing-indicator">
            AI đang gõ...
          </div>
        )}
      </div>
      
      <div className="input-area">
        <input
          type="text"
          value={inputMessage}
          onChange={(e) => setInputMessage(e.target.value)}
          onKeyPress={(e) => e.key === 'Enter' && sendMessage()}
          placeholder="Nhập tin nhắn..."
        />
        <button onClick={sendMessage}>Gửi</button>
      </div>
    </div>
  );
};

export default ChatComponent;
```

### Vue.js Example

```vue
<template>
  <div class="chat-container">
    <div class="messages">
      <div 
        v-for="msg in messages" 
        :key="msg.id" 
        :class="['message', msg.type]"
      >
        <div class="content">{{ msg.content }}</div>
        <div class="timestamp">
          {{ formatTime(msg.timestamp) }}
        </div>
      </div>
      <div v-if="isAiTyping" class="typing-indicator">
        AI đang gõ...
      </div>
    </div>
    
    <div class="input-area">
      <input
        v-model="inputMessage"
        @keyup.enter="sendMessage"
        placeholder="Nhập tin nhắn..."
      />
      <button @click="sendMessage">Gửi</button>
    </div>
  </div>
</template>

<script>
import io from 'socket.io-client';

export default {
  data() {
    return {
      socket: null,
      messages: [],
      inputMessage: '',
      conversationId: null,
      isAiTyping: false
    };
  },
  
  mounted() {
    this.initSocket();
  },
  
  beforeUnmount() {
    if (this.socket) {
      this.socket.close();
    }
  },
  
  methods: {
    initSocket() {
      this.socket = io('/webapp-chat', {
        auth: {
          token: localStorage.getItem('jwt_token')
        }
      });

      this.socket.on('webapp_chat:ai_response', (response) => {
        this.messages.push({
          id: response.messageId,
          content: response.content,
          type: 'ai',
          timestamp: response.timestamp
        });
        this.isAiTyping = false;
      });

      this.socket.on('webapp_chat:ai_typing', (data) => {
        this.isAiTyping = data.isTyping;
      });

      this.socket.on('webapp_chat:conversation_joined', (data) => {
        this.conversationId = data.conversationId;
      });

      // Tham gia cuộc hội thoại
      this.socket.emit('webapp_chat:join_conversation', {});
    },
    
    sendMessage() {
      if (!this.inputMessage.trim() || !this.socket) return;

      // Thêm tin nhắn user
      this.messages.push({
        id: Date.now(),
        content: this.inputMessage,
        type: 'user',
        timestamp: Date.now()
      });

      // Gửi qua WebSocket
      this.socket.emit('webapp_chat:send_message', {
        content: this.inputMessage,
        type: 'text',
        conversationId: this.conversationId
      });

      this.inputMessage = '';
      this.isAiTyping = true;
    },
    
    formatTime(timestamp) {
      return new Date(timestamp).toLocaleTimeString();
    }
  }
};
</script>
```

## Cấu hình

### Environment Variables

```env
# OpenAI Configuration
OPENAI_API_KEY=your_openai_api_key
OPENAI_MODEL=gpt-4

# JWT Configuration
JWT_SECRET=your_jwt_secret

# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_USERNAME=postgres
DB_PASSWORD=password
DB_DATABASE=ai_erp
```

### Swagger Documentation

API documentation có sẵn tại: `http://localhost:3000/api-docs`

## Troubleshooting

### Lỗi thường gặp

1. **WebSocket connection failed**
   - Kiểm tra JWT token hợp lệ
   - Đảm bảo CORS được cấu hình đúng

2. **AI không phản hồi**
   - Kiểm tra OPENAI_API_KEY
   - Xem logs trong AIOrchestatorService

3. **Tenant isolation issues**
   - Đảm bảo tenantId được truyền đúng trong JWT
   - Kiểm tra repository methods có sử dụng tenantId

### Logs

```bash
# Xem logs WebSocket
docker logs -f backend-container | grep "WebappChatGateway"

# Xem logs AI processing
docker logs -f backend-container | grep "AIOrchestatorService"
```
