import { Entity, PrimaryGeneratedColumn, Column } from 'typeorm';

/**
 * Entity representing the history of key result value updates
 */
@Entity('key_result_updates')
export class KeyResultUpdate {
  /**
   * Unique identifier for the key result update record
   */
  @PrimaryGeneratedColumn()
  id: number;

  /**
   * ID of the key result being updated
   */
  @Column({ name: 'key_result_id', type: 'integer', nullable: true })
  keyResultId: number | null;

  /**
   * Previous value of the key result before the update
   */
  @Column({
    name: 'previous_value',
    type: 'numeric',
    precision: 18,
    scale: 2,
    nullable: false,
  })
  previousValue: number;

  /**
   * New value of the key result after the update
   */
  @Column({
    name: 'new_value',
    type: 'numeric',
    precision: 18,
    scale: 2,
    nullable: false,
  })
  newValue: number;

  /**
   * ID of the user who performed the update
   */
  @Column({ name: 'update_by', type: 'integer', nullable: false })
  updateBy: number;

  /**
   * Confidence level for completing the key result (1-5)
   */
  @Column({ name: 'confidence_level', type: 'integer', nullable: true })
  confidenceLevel: number | null;

  /**
   * Notes for the update
   */
  @Column({ type: 'text', nullable: true })
  notes: string | null;

  /**
   * Type of check-in (manual, automatic, task-linked)
   */
  @Column({
    name: 'check_in_type',
    type: 'varchar',
    length: 50,
    default: 'manual',
    nullable: true,
  })
  checkInType: string | null;

  /**
   * Update timestamp (in milliseconds)
   */
  @Column({ name: 'updated_date', type: 'bigint', nullable: true })
  updatedDate: number | null;

  /**
   * ID of the company/organization that owns this record
   */
  @Column({ name: 'tenant_id', type: 'integer', nullable: true })
  tenantId: number | null;
}
