import { Injectable, Logger } from '@nestjs/common';
import { DiscoveryService, MetadataScanner, Reflector } from '@nestjs/core';
import { InstanceWrapper } from '@nestjs/core/injector/instance-wrapper';
import { TENANT_SECURITY_KEY } from '../decorators/tenant-security.decorator';

/**
 * Provider để quét và đăng ký middleware bảo mật tenant cho các controller hoặc route
 * được đánh dấu bằng decorator TenantSecurity
 */
@Injectable()
export class TenantSecurityProvider {
  private readonly logger = new Logger(TenantSecurityProvider.name);

  constructor(
    private readonly discoveryService: DiscoveryService,
    private readonly metadataScanner: MetadataScanner,
    private readonly reflector: Reflector,
  ) {}

  /**
   * Quét tất cả các controller và route để tìm những nơi cần áp dụng bảo mật tenant
   * @returns Danh sách các controller và route cần áp dụng bảo mật tenant
   */
  scanControllers(): { controller: string; routes: string[] }[] {
    const controllers = this.discoveryService.getControllers();
    const result: { controller: string; routes: string[] }[] = [];

    controllers.forEach((wrapper: InstanceWrapper) => {
      const { instance, metatype } = wrapper;
      if (!instance || !metatype) return;

      const controllerName = metatype.name;
      const controllerHasTenantSecurity = this.reflector.get(
        TENANT_SECURITY_KEY,
        metatype,
      );

      const routes: string[] = [];

      // Quét tất cả các phương thức của controller
      const prototype = Object.getPrototypeOf(instance);
      this.metadataScanner.scanFromPrototype(
        instance,
        prototype,
        (methodName: string) => {
          // Kiểm tra xem phương thức có được đánh dấu bảo mật tenant không
          const methodHasTenantSecurity = this.reflector.get(
            TENANT_SECURITY_KEY,
            instance[methodName],
          );

          // Nếu controller hoặc phương thức được đánh dấu, thêm vào danh sách
          if (controllerHasTenantSecurity || methodHasTenantSecurity) {
            routes.push(methodName);
          }
        },
      );

      if (routes.length > 0) {
        result.push({ controller: controllerName, routes });
      }
    });

    return result;
  }
}
