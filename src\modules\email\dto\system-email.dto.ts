import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsArray,
  IsEmail,
  IsNotEmpty,
  IsObject,
  IsOptional,
  IsString,
  MaxLength,
  ValidateNested,
} from 'class-validator';
import { Type } from 'class-transformer';

/**
 * DTO cho tệp đính kèm email
 */
export class EmailAttachmentDto {
  @ApiProperty({
    description: 'Tên file đính kèm',
    example: 'report.pdf',
  })
  @IsNotEmpty()
  @IsString()
  filename: string;

  @ApiProperty({
    description: 'Nội dung file (Base64 string)',
    example:
      'JVBERi0xLjQKJcOkw7zDtsOfCjIgMCBvYmoKPDwvTGVuZ3RoIDMgMCBSL0ZpbHRlci9GbGF0...',
  })
  @IsOptional()
  @IsString()
  content?: string;

  @ApiPropertyOptional({
    description: 'Đường dẫn đến file',
    example: '/tmp/reports/monthly-report.pdf',
  })
  @IsOptional()
  @IsString()
  path?: string;

  @ApiPropertyOptional({
    description: 'Loại MIME của file',
    example: 'application/pdf',
    examples: ['application/pdf', 'image/jpeg', 'text/plain'],
  })
  @IsOptional()
  @IsString()
  contentType?: string;
}

/**
 * DTO đầu vào cho API gửi email hệ thống đơn giản
 */
export class SendSystemEmailDto {
  @ApiProperty({
    description: 'Địa chỉ email người nhận',
    example: '<EMAIL>',
  })
  @IsNotEmpty()
  @IsEmail()
  to: string;

  @ApiProperty({
    description: 'Tiêu đề email',
    example: 'Thông báo hệ thống quan trọng',
  })
  @IsNotEmpty()
  @IsString()
  @MaxLength(255)
  subject: string;

  @ApiProperty({
    description: 'Nội dung email (HTML)',
    example: '<p>Đây là <strong>nội dung</strong> email test</p>',
  })
  @IsNotEmpty()
  @IsString()
  content: string;

  @ApiPropertyOptional({
    description: 'Danh sách email CC',
    example: ['<EMAIL>', '<EMAIL>'],
    type: [String],
  })
  @IsOptional()
  @IsArray()
  @IsEmail({}, { each: true })
  cc?: string[];

  @ApiPropertyOptional({
    description: 'Danh sách email BCC',
    example: ['<EMAIL>'],
    type: [String],
  })
  @IsOptional()
  @IsArray()
  @IsEmail({}, { each: true })
  bcc?: string[];

  @ApiPropertyOptional({
    description:
      'Email người gửi (nếu không cung cấp sẽ dùng mặc định từ cấu hình)',
    example: '<EMAIL>',
  })
  @IsOptional()
  @IsEmail()
  from?: string;

  @ApiPropertyOptional({
    description: 'Danh sách file đính kèm',
    type: [EmailAttachmentDto],
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => EmailAttachmentDto)
  attachments?: EmailAttachmentDto[];
}

/**
 * DTO đầu vào cho API gửi email hệ thống theo mẫu
 */
export class SendSystemTemplateEmailDto {
  @ApiProperty({
    description: 'Địa chỉ email người nhận',
    example: '<EMAIL>',
  })
  @IsNotEmpty()
  @IsEmail()
  to: string;

  @ApiProperty({
    description: 'ID của mẫu email',
    example: 'system-alert-template',
  })
  @IsNotEmpty()
  @IsString()
  templateId: string;

  @ApiProperty({
    description: 'Dữ liệu cho mẫu email',
    example: {
      userName: 'Nguyễn Văn A',
      alertType: 'CRITICAL',
      message: 'Hệ thống gặp sự cố nghiêm trọng',
      time: '15:30 12/05/2023',
    },
  })
  @IsNotEmpty()
  @IsObject()
  data: Record<string, any>;

  @ApiPropertyOptional({
    description: 'Danh sách email CC',
    example: ['<EMAIL>', '<EMAIL>'],
    type: [String],
  })
  @IsOptional()
  @IsArray()
  @IsEmail({}, { each: true })
  cc?: string[];

  @ApiPropertyOptional({
    description: 'Danh sách email BCC',
    example: ['<EMAIL>'],
    type: [String],
  })
  @IsOptional()
  @IsArray()
  @IsEmail({}, { each: true })
  bcc?: string[];
}

/**
 * DTO đầu ra cho API gửi email hệ thống
 */
export class SystemEmailResponseDto {
  @ApiProperty({
    description: 'ID của job xử lý email',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  jobId: string;

  @ApiProperty({
    description: 'Trạng thái của job',
    example: 'queued',
    examples: ['queued', 'processing', 'completed'],
  })
  status: string;

  @ApiProperty({
    description: 'Thời gian gửi email (timestamp)',
    example: 1682584291000,
  })
  timestamp: number;

  @ApiProperty({
    description: 'Email của người thực hiện gửi',
    example: '<EMAIL>',
  })
  sentBy: string;
}
