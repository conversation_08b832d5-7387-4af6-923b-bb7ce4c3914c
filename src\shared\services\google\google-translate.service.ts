import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { v2 } from '@google-cloud/translate';

@Injectable()
export class GoogleTranslateService {
  private client: v2.Translate;
  private readonly logger = new Logger(GoogleTranslateService.name);

  constructor(private readonly configService: ConfigService) {
    try {
      // Lấy thông tin xác thực từ biến môi trường hoặc file
      const keyFilename = this.configService.get<string>(
        'GOOGLE_APPLICATION_CREDENTIALS',
      );
      const projectId = this.configService.get<string>(
        'GOOGLE_CLOUD_PROJECT_ID',
      );

      if (!keyFilename || !projectId) {
        this.logger.error('Missing Google Cloud Translate configuration');
        return;
      }

      // Khởi tạo Translate client
      this.client = new v2.Translate({
        keyFilename,
        projectId,
      });
      this.logger.log('Google Cloud Translate initialized');
    } catch (error) {
      this.logger.error(
        `Failed to initialize Google Cloud Translate: ${error.message}`,
      );
    }
  }

  /**
   * Dịch văn bản
   * @param text Văn bản cần dịch
   * @param targetLanguage Mã ngôn ngữ đích (ví dụ: 'vi', 'en', 'fr', ...)
   * @param sourceLanguage Mã ngôn ngữ nguồn (tùy chọn, tự động phát hiện nếu không cung cấp)
   * @returns Văn bản đã dịch
   */
  async translateText(
    text: string,
    targetLanguage: string,
    sourceLanguage?: string,
  ): Promise<string> {
    try {
      const options: any = {
        to: targetLanguage,
      };

      if (sourceLanguage) {
        options.from = sourceLanguage;
      }

      const [translation] = await this.client.translate(text, options);
      return translation;
    } catch (error) {
      this.logger.error(`Failed to translate text: ${error.message}`);
      throw error;
    }
  }

  /**
   * Dịch nhiều văn bản
   * @param texts Danh sách văn bản cần dịch
   * @param targetLanguage Mã ngôn ngữ đích (ví dụ: 'vi', 'en', 'fr', ...)
   * @param sourceLanguage Mã ngôn ngữ nguồn (tùy chọn, tự động phát hiện nếu không cung cấp)
   * @returns Danh sách văn bản đã dịch
   */
  async translateTexts(
    texts: string[],
    targetLanguage: string,
    sourceLanguage?: string,
  ): Promise<string[]> {
    try {
      const options: any = {
        to: targetLanguage,
      };

      if (sourceLanguage) {
        options.from = sourceLanguage;
      }

      const [translations] = await this.client.translate(texts, options);
      return translations;
    } catch (error) {
      this.logger.error(`Failed to translate texts: ${error.message}`);
      throw error;
    }
  }

  /**
   * Phát hiện ngôn ngữ của văn bản
   * @param text Văn bản cần phát hiện ngôn ngữ
   * @returns Thông tin về ngôn ngữ được phát hiện
   */
  async detectLanguage(
    text: string,
  ): Promise<{ language: string; confidence: number }> {
    try {
      const [detection] = await this.client.detect(text);
      return {
        language: detection.language,
        confidence: detection.confidence,
      };
    } catch (error) {
      this.logger.error(`Failed to detect language: ${error.message}`);
      throw error;
    }
  }

  /**
   * Lấy danh sách ngôn ngữ được hỗ trợ
   * @param targetLanguage Mã ngôn ngữ để hiển thị tên ngôn ngữ (ví dụ: 'vi', 'en', 'fr', ...)
   * @returns Danh sách ngôn ngữ được hỗ trợ
   */
  async getSupportedLanguages(
    targetLanguage: string = 'en',
  ): Promise<{ code: string; name: string }[]> {
    try {
      const [languages] = await this.client.getLanguages(targetLanguage);
      return languages.map((language) => ({
        code: language.code,
        name: language.name,
      }));
    } catch (error) {
      this.logger.error(`Failed to get supported languages: ${error.message}`);
      throw error;
    }
  }
}
