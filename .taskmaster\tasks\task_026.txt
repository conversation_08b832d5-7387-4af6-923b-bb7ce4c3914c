# Task ID: 26
# Title: Complete HRM Contracts Module
# Status: done
# Dependencies: 12
# Priority: high
# Description: Update ContractController, ContractService, and ContractRepository to implement manual tenantId injection pattern
# Details:
Update all methods in the Contracts module:
- ContractService: findAll, findById, findByEmployeeId, findActiveContractByEmployeeId, findContractsExpiringSoon, create, update, updateStatus, terminateContract, delete
- ContractController: Add @CurrentUser() decorator and pass Number(user.tenantId) to service methods
- ContractRepository: Ensure all queries include tenantId filtering

# Test Strategy:
Unit tests for service methods with tenantId isolation, integration tests for controller endpoints
