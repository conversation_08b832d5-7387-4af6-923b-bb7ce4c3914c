import { Entity, PrimaryGeneratedColumn, Column, Index } from 'typeorm';

export type AccessType =
  | 'view'
  | 'download'
  | 'search'
  | 'rag_query'
  | 'edit'
  | 'delete';

@Entity('document_access_logs')
@Index(['tenantId'])
@Index(['documentId'])
@Index(['userId'])
@Index(['accessType'])
@Index(['createdAt'])
@Index(['success'])
@Index(['sessionId'])
export class DocumentAccessLog {
  /**
   * Khóa chính
   */
  @PrimaryGeneratedColumn()
  id: number;

  /**
   * ID tài liệu (có thể null cho tìm kiếm tổng quát)
   */
  @Column({ name: 'document_id', type: 'integer', nullable: true })
  documentId: number | null;

  /**
   * ID người dùng thực hiện hành động
   */
  @Column({ name: 'user_id', type: 'integer' })
  userId: number;

  /**
   * <PERSON>ạ<PERSON> truy cập
   */
  @Column({ name: 'access_type', type: 'varchar', length: 50 })
  accessType: AccessType;

  /**
   * Văn bản truy vấn gốc cho tìm kiếm RAG
   */
  @Column({ name: 'query_text', type: 'text', nullable: true })
  queryText: string | null;

  /**
   * Kết quả tìm kiếm RAG dưới dạng JSON
   */
  @Column({ name: 'query_results', type: 'jsonb', nullable: true })
  queryResults: Record<string, any> | null;

  /**
   * Điểm tin cậy AI (0.00-1.00)
   */
  @Column({
    name: 'query_confidence',
    type: 'decimal',
    precision: 3,
    scale: 2,
    nullable: true,
  })
  queryConfidence: number | null;

  /**
   * Địa chỉ IP của client
   */
  @Column({ name: 'ip_address', type: 'inet', nullable: true })
  ipAddress: string | null;

  /**
   * Chuỗi user agent
   */
  @Column({ name: 'user_agent', type: 'text', nullable: true })
  userAgent: string | null;

  /**
   * ID phiên làm việc
   */
  @Column({ name: 'session_id', type: 'varchar', length: 100, nullable: true })
  sessionId: string | null;

  /**
   * Thời gian phản hồi tính bằng milliseconds
   */
  @Column({ name: 'response_time_ms', type: 'integer', nullable: true })
  responseTimeMs: number | null;

  /**
   * Thao tác có thành công không
   */
  @Column({ name: 'success', type: 'boolean', default: true })
  success: boolean;

  /**
   * Thông báo lỗi nếu thao tác thất bại
   */
  @Column({ name: 'error_message', type: 'text', nullable: true })
  errorMessage: string | null;

  /**
   * Metadata bổ sung
   */
  @Column({ name: 'metadata', type: 'jsonb', nullable: true })
  metadata: Record<string, any> | null;

  /**
   * Thời điểm tạo (timestamp tính bằng milliseconds)
   */
  @Column({ name: 'created_at', type: 'bigint' })
  createdAt: number;

  /**
   * ID tenant để phân tách dữ liệu multi-tenant
   */
  @Column({ name: 'tenant_id', type: 'integer' })
  tenantId: number;

  // =====================================================
  // THUỘC TÍNH TÍNH TOÁN
  // =====================================================

  /**
   * Kiểm tra có phải log truy vấn RAG không
   */
  get isRagQuery(): boolean {
    return this.accessType === 'rag_query';
  }

  /**
   * Kiểm tra có phải log tìm kiếm không
   */
  get isSearch(): boolean {
    return this.accessType === 'search' || this.accessType === 'rag_query';
  }

  /**
   * Kiểm tra có phải log truy cập tài liệu không
   */
  get isDocumentAccess(): boolean {
    return this.documentId !== null;
  }

  /**
   * Lấy thời gian phản hồi tính bằng giây
   */
  get responseTimeSeconds(): number | null {
    if (!this.responseTimeMs) return null;
    return Math.round((this.responseTimeMs / 1000) * 100) / 100;
  }

  /**
   * Lấy phần trăm tin cậy
   */
  get confidencePercentage(): number | null {
    if (!this.queryConfidence) return null;
    return Math.round(this.queryConfidence * 100);
  }

  /**
   * Lấy ngày tháng đã định dạng
   */
  get formattedDate(): string {
    return new Date(this.createdAt).toISOString();
  }

  /**
   * Kiểm tra thao tác có chậm không (> 3 giây)
   */
  get isSlow(): boolean {
    if (!this.responseTimeMs) return false;
    return this.responseTimeMs > 3000;
  }
}
