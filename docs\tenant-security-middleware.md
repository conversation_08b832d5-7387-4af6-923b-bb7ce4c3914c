# Hướng dẫn sử dụng Middleware bảo mật Tenant

Middleware bảo mật Tenant được thiết kế để đảm bảo rằng người dùng chỉ có thể truy cập dữ liệu thuộc về công ty của họ. Middleware này sẽ kiểm tra tenantId trong JWT token và đảm bảo rằng người dùng chỉ có thể truy cập dữ liệu thuộc về công ty của họ.

## Cách hoạt động

1. **TenantSecurityMiddleware**: Middleware này sẽ kiểm tra tenantId trong JWT token và đảm bảo rằng người dùng chỉ có thể truy cập dữ liệu thuộc về công ty của họ.
2. **TenantSubscriber**: Subscriber này sẽ kiểm tra và đảm bảo rằng tenantId không bị thay đổi trong quá trình cập nhật entity.
3. **CurrentTenant Decorator**: Decorator này giúp dễ dàng lấy tenantId từ request trong controller.

## Cách sử dụng

### 1. Áp dụng cho Controller

Sử dụng decorator `TenantSecurity` để áp dụng middleware cho controller:

```typescript
import { Controller, Get } from '@nestjs/common';
import { TenantSecurity, CurrentTenant } from '@/common';

@Controller('users')
@TenantSecurity() // Áp dụng cho tất cả các route trong controller
export class UserController {
  constructor(private readonly userService: UserService) {}

  @Get()
  findAll(@CurrentTenant() tenantId: number) {
    return this.userService.findAll(tenantId);
  }
}
```

### 2. Áp dụng cho Route

Sử dụng decorator `TenantSecurity` để áp dụng middleware cho route:

```typescript
import { Controller, Get } from '@nestjs/common';
import { TenantSecurity, CurrentTenant } from '@/common';

@Controller('users')
export class UserController {
  constructor(private readonly userService: UserService) {}

  @Get()
  @TenantSecurity() // Áp dụng cho route cụ thể
  findAll(@CurrentTenant() tenantId: number) {
    return this.userService.findAll(tenantId);
  }
}
```

### 3. Lấy tenantId trong Controller

Sử dụng decorator `CurrentTenant` để lấy tenantId từ request:

```typescript
import { Controller, Get } from '@nestjs/common';
import { TenantSecurity, CurrentTenant } from '@/common';

@Controller('users')
@TenantSecurity()
export class UserController {
  constructor(private readonly userService: UserService) {}

  @Get()
  findAll(@CurrentTenant() tenantId: number) {
    return this.userService.findAll(tenantId);
  }
}
```

### 4. Sử dụng trong Repository

Thêm điều kiện tenantId trong repository để đảm bảo rằng người dùng chỉ có thể truy cập dữ liệu thuộc về công ty của họ:

```typescript
import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { User } from '../entities/user.entity';

@Injectable()
export class UserRepository {
  constructor(
    @InjectRepository(User)
    private readonly repository: Repository<User>,
  ) {}

  async findAll(tenantId: number): Promise<User[]> {
    // Thêm điều kiện tenantId để đảm bảo rằng người dùng chỉ có thể truy cập dữ liệu thuộc về công ty của họ
    return this.repository.find({ where: { tenantId } });
  }

  async findById(id: number, tenantId: number): Promise<User | null> {
    // Thêm điều kiện tenantId để đảm bảo rằng người dùng chỉ có thể truy cập dữ liệu thuộc về công ty của họ
    return this.repository.findOne({ where: { id, tenantId } });
  }

  async create(data: Partial<User>): Promise<User> {
    // Đảm bảo rằng tenantId được thêm vào entity mới
    const user = this.repository.create(data);
    return this.repository.save(user);
  }

  async update(id: number, tenantId: number, data: Partial<User>): Promise<User | null> {
    // Thêm điều kiện tenantId để đảm bảo rằng người dùng chỉ có thể cập nhật dữ liệu thuộc về công ty của họ
    await this.repository.update({ id, tenantId }, data);
    return this.findById(id, tenantId);
  }

  async delete(id: number, tenantId: number): Promise<boolean> {
    // Thêm điều kiện tenantId để đảm bảo rằng người dùng chỉ có thể xóa dữ liệu thuộc về công ty của họ
    const result = await this.repository.delete({ id, tenantId });
    return result.affected > 0;
  }
}
```

### 5. Truy cập dữ liệu của công ty khác (chỉ dành cho SYSTEM_ADMIN)

Nếu là SYSTEM_ADMIN, có thể truy cập dữ liệu của bất kỳ công ty nào bằng cách thêm tenantId vào request:

```typescript
import { Controller, Get, Query } from '@nestjs/common';
import { TenantSecurity, CurrentTenant } from '@/common';

@Controller('users')
@TenantSecurity()
export class UserController {
  constructor(private readonly userService: UserService) {}

  @Get('admin')
  findAllForAdmin(@Query('tenantId') tenantId: number) {
    // Chỉ SYSTEM_ADMIN mới có thể truy cập dữ liệu của công ty khác
    return this.userService.findAll(tenantId);
  }
}
```

## Lưu ý

1. Middleware này chỉ hoạt động với các entity có trường `tenantId`.
2. Chỉ SYSTEM_ADMIN mới có thể truy cập dữ liệu của công ty khác.
3. Nếu không có tenantId trong JWT token, middleware sẽ ném lỗi.
4. Nếu tenantId trong request không khớp với tenantId trong JWT token, middleware sẽ ném lỗi (trừ khi là SYSTEM_ADMIN).
5. Luôn thêm điều kiện tenantId trong repository để đảm bảo rằng người dùng chỉ có thể truy cập dữ liệu thuộc về công ty của họ.
6. TenantSubscriber sẽ ngăn chặn việc thay đổi tenantId của entity trong quá trình cập nhật.
