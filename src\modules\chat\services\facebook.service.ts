import { Injectable, Logger } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { ConfigService } from '@nestjs/config';
import { firstValueFrom } from 'rxjs';
import { AppException, ErrorCode } from '@common/exceptions/app.exception';

/**
 * Interface cho Facebook Page Access Token
 */
interface FacebookPageToken {
  pageId: string;
  accessToken: string;
  expiresAt?: number;
}

/**
 * Interface cho Facebook User Profile
 */
interface FacebookUserProfile {
  id: string;
  name: string;
  firstName?: string;
  lastName?: string;
  profilePic?: string;
}

/**
 * Interface cho Facebook Message
 */
interface FacebookMessage {
  recipient: { id: string };
  message: {
    text?: string;
    attachment?: any;
    quick_replies?: any[];
  };
  messaging_type?: string;
}

/**
 * Service xử lý tích hợp với Facebook Messenger API
 */
@Injectable()
export class FacebookService {
  private readonly logger = new Logger(FacebookService.name);
  private readonly graphApiUrl = 'https://graph.facebook.com/v18.0';

  constructor(
    private readonly httpService: HttpService,
    private readonly configService: ConfigService,
  ) {}

  /**
   * Gửi tin nhắn text đến Facebook user
   */
  async sendTextMessage(
    pageAccessToken: string,
    recipientId: string,
    text: string,
  ): Promise<any> {
    try {
      const message: FacebookMessage = {
        recipient: { id: recipientId },
        message: { text },
        messaging_type: 'RESPONSE',
      };

      const response = await firstValueFrom(
        this.httpService.post(`${this.graphApiUrl}/me/messages`, message, {
          params: { access_token: pageAccessToken },
          headers: { 'Content-Type': 'application/json' },
        }),
      );

      this.logger.log(`Sent message to ${recipientId}: ${text}`);
      return response.data;
    } catch (error) {
      this.logger.error(`Failed to send message: ${error.message}`);
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        `Failed to send Facebook message: ${error.message}`,
      );
    }
  }

  /**
   * Gửi tin nhắn với quick replies
   */
  async sendQuickReplies(
    pageAccessToken: string,
    recipientId: string,
    text: string,
    quickReplies: Array<{ title: string; payload: string }>,
  ): Promise<any> {
    try {
      const message: FacebookMessage = {
        recipient: { id: recipientId },
        message: {
          text,
          quick_replies: quickReplies.map((reply) => ({
            content_type: 'text',
            title: reply.title,
            payload: reply.payload,
          })),
        },
        messaging_type: 'RESPONSE',
      };

      const response = await firstValueFrom(
        this.httpService.post(`${this.graphApiUrl}/me/messages`, message, {
          params: { access_token: pageAccessToken },
          headers: { 'Content-Type': 'application/json' },
        }),
      );

      this.logger.log(`Sent quick replies to ${recipientId}`);
      return response.data;
    } catch (error) {
      this.logger.error(`Failed to send quick replies: ${error.message}`);
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        `Failed to send Facebook quick replies: ${error.message}`,
      );
    }
  }

  /**
   * Lấy thông tin profile của Facebook user
   */
  async getUserProfile(
    pageAccessToken: string,
    userId: string,
  ): Promise<FacebookUserProfile> {
    try {
      const response = await firstValueFrom(
        this.httpService.get(`${this.graphApiUrl}/${userId}`, {
          params: {
            access_token: pageAccessToken,
            fields: 'id,name,first_name,last_name,profile_pic',
          },
        }),
      );

      return {
        id: response.data.id,
        name: response.data.name,
        firstName: response.data.first_name,
        lastName: response.data.last_name,
        profilePic: response.data.profile_pic,
      };
    } catch (error) {
      this.logger.error(`Failed to get user profile: ${error.message}`);
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        `Failed to get Facebook user profile: ${error.message}`,
      );
    }
  }

  // Phương thức verifyWebhookSignature đã được định nghĩa ở phía dưới

  /**
   * Set typing indicator
   */
  async setTypingIndicator(
    pageAccessToken: string,
    recipientId: string,
    action: 'typing_on' | 'typing_off',
  ): Promise<void> {
    try {
      await firstValueFrom(
        this.httpService.post(
          `${this.graphApiUrl}/me/messages`,
          {
            recipient: { id: recipientId },
            sender_action: action,
          },
          {
            params: { access_token: pageAccessToken },
            headers: { 'Content-Type': 'application/json' },
          },
        ),
      );
    } catch (error) {
      this.logger.error(`Failed to set typing indicator: ${error.message}`);
    }
  }

  /**
   * Xác minh webhook signature từ Facebook
   * @param rawBody Nội dung raw của request
   * @param signature Chữ ký X-Hub-Signature từ header
   * @param appSecret App Secret của Facebook App
   * @returns true nếu chữ ký hợp lệ, false nếu không
   */
  verifyWebhookSignature(
    rawBody: string,
    signature: string,
    appSecret: string,
  ): boolean {
    try {
      // Facebook sử dụng sha1 cho webhook signature
      if (!signature || !signature.startsWith('sha1=')) {
        return false;
      }

      const crypto = require('crypto');
      const expectedSignature =
        'sha1=' +
        crypto.createHmac('sha1', appSecret).update(rawBody).digest('hex');

      // Sử dụng so sánh an toàn về thời gian để tránh timing attacks
      return crypto.timingSafeEqual(
        Buffer.from(signature),
        Buffer.from(expectedSignature),
      );
    } catch (error) {
      this.logger.error(
        `Webhook signature verification failed: ${error.message}`,
      );
      return false;
    }
  }

  /**
   * Đổi mã code OAuth lấy về Long-lived Access Token
   * @param code Mã code từ OAuth flow
   * @param redirectUri URI redirect đã đăng ký với Facebook
   * @returns Object chứa access token và thông tin hạn sử dụng
   */
  async exchangeCodeForToken(code: string, redirectUri: string): Promise<any> {
    try {
      const appId = this.configService.get<string>('FACEBOOK_APP_ID');
      const appSecret = this.configService.get<string>('FACEBOOK_APP_SECRET');

      // Exchange code for short-lived token
      const tokenResponse = await firstValueFrom(
        this.httpService.get(
          'https://graph.facebook.com/v18.0/oauth/access_token',
          {
            params: {
              client_id: appId,
              client_secret: appSecret,
              redirect_uri: redirectUri,
              code: code,
            },
          },
        ),
      );

      // Convert to long-lived token
      const longLivedTokenResponse = await firstValueFrom(
        this.httpService.get(
          'https://graph.facebook.com/v18.0/oauth/access_token',
          {
            params: {
              grant_type: 'fb_exchange_token',
              client_id: appId,
              client_secret: appSecret,
              fb_exchange_token: tokenResponse.data.access_token,
            },
          },
        ),
      );

      return longLivedTokenResponse.data;
    } catch (error) {
      this.logger.error(`Failed to exchange code for token: ${error.message}`);
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        `Failed to exchange Facebook code: ${error.message}`,
      );
    }
  }

  /**
   * Lấy danh sách Page của người dùng đã được cấp quyền
   * @param userAccessToken Access token của người dùng
   * @returns Danh sách Page và token tương ứng
   */
  async getAuthorizedPages(userAccessToken: string): Promise<any[]> {
    try {
      const response = await firstValueFrom(
        this.httpService.get(`${this.graphApiUrl}/me/accounts`, {
          params: {
            access_token: userAccessToken,
            fields: 'id,name,access_token,category',
          },
        }),
      );

      return response.data.data;
    } catch (error) {
      this.logger.error(`Failed to get authorized pages: ${error.message}`);
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        `Failed to get Facebook pages: ${error.message}`,
      );
    }
  }

  /**
   * Đăng ký webhook cho Page
   * @param pageId ID của Facebook Page
   * @param pageAccessToken Access token của Page
   * @returns Kết quả đăng ký webhook
   */
  async subscribeAppToPageWebhook(
    pageId: string,
    pageAccessToken: string,
  ): Promise<any> {
    try {
      const response = await firstValueFrom(
        this.httpService.post(
          `${this.graphApiUrl}/${pageId}/subscribed_apps`,
          {},
          {
            params: {
              access_token: pageAccessToken,
              subscribed_fields:
                'messages,messaging_postbacks,messaging_optins',
            },
          },
        ),
      );

      return response.data;
    } catch (error) {
      this.logger.error(
        `Failed to subscribe app to page webhook: ${error.message}`,
      );
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        `Failed to subscribe to Facebook page webhook: ${error.message}`,
      );
    }
  }
}
