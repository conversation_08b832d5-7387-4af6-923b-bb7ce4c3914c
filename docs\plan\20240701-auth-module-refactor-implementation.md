# Triển khai kế hoạch chỉnh sửa module Auth

## C<PERSON><PERSON> thay đổi đã thực hiện

### 1. Thay đổi cấu trúc dữ liệu
1. **Xóa trường `password` trong CompanyAccount Entity**:
   - <PERSON><PERSON> cập nhật entity `CompanyAccount` để loại bỏ trường `password`
   - Đã tạo migration `*************-RemovePasswordFromCompanyAccounts.ts` để xóa cột `password` trong bảng `company_accounts`

2. **Điều chỉnh DTO**:
   - Đ<PERSON> cập nhật `CompanyRegisterDto` để bao gồm thông tin người dùng quản trị (admin)
   - <PERSON><PERSON> thêm `AdminUserDto` để chứa thông tin người dùng quản trị

### 2. Thay đổi quá trình đăng ký
1. **Tạo tài khoản công ty và tài khoản người dùng**:
   - <PERSON><PERSON> cập nhật phương thức `register` trong `CompanyAuthService` để tạo cả tài khoản công ty và tài khoản người dùng
   - Đã thiết lập `tenantId` của `User` là ID của `CompanyAccount`

2. **Cập nhật quá trình xác thực email**:
   - Đã cập nhật phương thức `verifyEmail` để kích hoạt cả tài khoản công ty và tài khoản người dùng

### 3. Thay đổi quá trình đăng nhập
1. **Điều chỉnh quá trình đăng nhập**:
   - Đã cập nhật phương thức `login` để tìm kiếm cả công ty và người dùng
   - Đã chuyển việc xác thực mật khẩu sang tài khoản người dùng

2. **Cập nhật JWT payload**:
   - Đã cập nhật payload để bao gồm thông tin cả công ty và người dùng
   - Đã giữ nguyên type 'COMPANY_ADMIN' để duy trì tính năng tự động cấp quyền
   - Đã thêm danh sách quyền (permissions) vào JWT token
   - Đã cập nhật DTO phản hồi để bao gồm danh sách quyền
   - Đã chuyển từ việc sử dụng `JwtService.sign()` sang `JwtUtilService.generateToken()` trong tất cả các phương thức

### 4. Cập nhật quá trình gửi lại OTP
1. **Điều chỉnh quá trình gửi lại OTP**:
   - Đã cập nhật phương thức `resendOtp` để kiểm tra cả tài khoản công ty và tài khoản người dùng

### 5. Gán quyền cho người dùng quản trị
1. **Tạo và gán quyền cho người dùng quản trị**:
   - Đã thêm phương thức `assignAdminPermissions` để gán tất cả quyền cho người dùng quản trị
   - Phương thức này được gọi sau khi xác thực email thành công
   - Tạo role "Admin" nếu chưa tồn tại và gán tất cả quyền cho role này
   - Gán role "Admin" cho người dùng quản trị

## Các vấn đề cần lưu ý

1. **Tương thích ngược**:
   - Các tài khoản công ty đã tồn tại sẽ không có tài khoản người dùng tương ứng
   - Cần xem xét việc tạo tài khoản người dùng cho các công ty đã tồn tại

2. **Bảo mật**:
   - Mật khẩu hiện được lưu trữ trong bảng `users` thay vì `company_accounts`
   - Cần đảm bảo rằng các quyền được gán đúng cho người dùng quản trị

3. **Kiểm thử**:
   - Cần kiểm tra kỹ các tính năng đăng ký, đăng nhập, xác thực email và gửi lại OTP
   - Cần kiểm tra việc tạo JWT token và xác thực quyền

## Các bước tiếp theo

1. **Tạo script migration dữ liệu**:
   - Tạo script để tạo tài khoản người dùng cho các công ty đã tồn tại
   - Đảm bảo rằng các tài khoản người dùng mới có đầy đủ quyền

2. **Cập nhật tài liệu API**:
   - Cập nhật tài liệu Swagger cho các API đăng ký và đăng nhập
   - Cập nhật hướng dẫn sử dụng cho người dùng

3. **Kiểm thử toàn diện**:
   - Kiểm tra tất cả các tính năng liên quan đến xác thực và phân quyền
   - Đảm bảo rằng các tính năng hiện có vẫn hoạt động bình thường
