# Kế hoạch phát triển tính năng chấm điểm và thống kê cho module Todolists

**Ng<PERSON><PERSON> tạo:** 05/09/2024
**Người tạo:** AI ERP Team
**Phi<PERSON><PERSON> bản:** 1.0

## 1. Tổng quan

### 1.1. <PERSON><PERSON><PERSON> tiêu
- <PERSON><PERSON><PERSON> triển cơ chế tính điểm cho công việ<PERSON> (Todo) dựa trên trường `expectedStars` và `awardedStars`
- Xây dựng các API thống kê và biểu đồ để theo dõi tiến độ công việc
- Tích hợp với hệ thống phòng ban và OKRs để cung cấp cái nhìn tổng quan về hiệu suất

### 1.2. Phạm vi
- Chỉ cấp trên của người đảm nhiệm nhiệm vụ có quyền chấm điểm
- Thống kê theo nhiều chiều: c<PERSON>, <PERSON><PERSON><PERSON><PERSON> ban, d<PERSON>, thờ<PERSON> gian
<PERSON> <PERSON><PERSON><PERSON><PERSON>, bi<PERSON><PERSON> đồ ti<PERSON> đ<PERSON>, bi<PERSON><PERSON> đồ so sánh hiệu suất

## 2. Phân tích hiện trạng

### 2.1. Cấu trúc dữ liệu hiện tại

#### 2.1.1. Todo Entity
- Đã có sẵn trường `expectedStars` (số sao kỳ vọng, 1-5)
- Đã có sẵn trường `awardedStars` (số sao thực tế được chấm, 1-5)
- Đã có sẵn trường `status` (trạng thái: pending, in_progress, completed, approved, rejected)
- Đã có sẵn trường `assigneeId` (người được giao nhiệm vụ)
- Đã có sẵn trường `createdBy` (người tạo nhiệm vụ)
- Đã có sẵn trường `categoryId` (dự án mà nhiệm vụ thuộc về)

#### 2.1.2. ProjectMember Entity
- Lưu thông tin về vai trò của thành viên trong dự án (admin, member, viewer)
- Liên kết giữa người dùng và dự án

#### 2.1.3. TaskKr Entity
- Liên kết giữa Todo và KeyResult trong hệ thống OKRs

### 2.2. Các API hiện tại
- API tạo, cập nhật, xóa Todo
- API cập nhật trạng thái Todo
- Chưa có API chấm điểm Todo
- Chưa có API thống kê và biểu đồ

## 3. Kế hoạch phát triển

### 3.1. Cơ chế chấm điểm Todo

#### 3.1.1. Xác định người có quyền chấm điểm
- Cấp trên trực tiếp của người được giao nhiệm vụ (quản lý phòng ban)
- Người tạo nhiệm vụ (nếu khác với người được giao)
- Admin của dự án (nếu nhiệm vụ thuộc về dự án)

#### 3.1.2. Quy trình chấm điểm
1. Người được giao nhiệm vụ hoàn thành công việc và cập nhật trạng thái thành `COMPLETED`
2. Hệ thống gửi thông báo cho người có quyền chấm điểm
3. Người có quyền chấm điểm đánh giá và chấm điểm (1-5 sao)
4. Hệ thống cập nhật trường `awardedStars` và lưu phản hồi
5. Trạng thái công việc được cập nhật thành `APPROVED`

#### 3.1.3. API chấm điểm Todo
- **Endpoint**: `PATCH /api/todos/:id/score`
- **Quyền truy cập**: Chỉ người có quyền chấm điểm
- **Request Body**:
  ```json
  {
    "awardedStars": 4,
    "feedback": "Hoàn thành tốt nhiệm vụ, nhưng còn chậm tiến độ"
  }
  ```
- **Response**: Thông tin Todo đã được cập nhật

#### 3.1.4. Cập nhật TodoService
- Thêm phương thức `scoreTodo` để xử lý logic chấm điểm
- Kiểm tra quyền chấm điểm dựa trên mối quan hệ giữa người chấm và người được giao
- Chỉ cho phép chấm điểm khi Todo ở trạng thái `COMPLETED`

#### 3.1.5. Cập nhật TodoRepository
- Thêm phương thức `updateScore` để cập nhật điểm và phản hồi

### 3.2. API thống kê và biểu đồ

#### 3.2.1. Thống kê hiệu suất cá nhân
- **Endpoint**: `GET /api/statistics/users/:userId/performance`
- **Quyền truy cập**: Người dùng tự xem hoặc quản lý
- **Query Parameters**: `startDate`, `endDate`, `projectId` (optional)
- **Response**: Thông tin hiệu suất (tổng số nhiệm vụ, hoàn thành, đang thực hiện, trễ hạn, điểm trung bình)

#### 3.2.2. Thống kê hiệu suất phòng ban
- **Endpoint**: `GET /api/statistics/departments/:departmentId/performance`
- **Quyền truy cập**: Quản lý phòng ban, Admin
- **Query Parameters**: `startDate`, `endDate`
- **Response**: Thông tin hiệu suất của phòng ban và từng thành viên

#### 3.2.3. Thống kê hiệu suất dự án
- **Endpoint**: `GET /api/statistics/projects/:projectId/performance`
- **Quyền truy cập**: Thành viên dự án
- **Query Parameters**: `startDate`, `endDate`
- **Response**: Thông tin hiệu suất dự án và từng thành viên

#### 3.2.4. Biểu đồ Gantt
- **Endpoint**: `GET /api/statistics/projects/:projectId/gantt`
- **Quyền truy cập**: Thành viên dự án
- **Query Parameters**: `startDate`, `endDate`
- **Response**: Dữ liệu cho biểu đồ Gantt (nhiệm vụ, thời gian bắt đầu, thời gian kết thúc, tiến độ)

#### 3.2.5. Biểu đồ tiến độ OKRs
- **Endpoint**: `GET /api/statistics/okrs/:cycleId/progress`
- **Quyền truy cập**: Tất cả người dùng
- **Response**: Dữ liệu tiến độ OKRs và các nhiệm vụ liên quan

### 3.3. Cập nhật cơ sở dữ liệu

#### 3.3.1. Thêm bảng TodoScore
```sql
CREATE TABLE todo_scores (
  id SERIAL PRIMARY KEY,
  todo_id INTEGER NOT NULL REFERENCES todos(id),
  scorer_id INTEGER NOT NULL,
  awarded_stars INTEGER NOT NULL CHECK (awarded_stars BETWEEN 1 AND 5),
  feedback TEXT,
  created_at BIGINT,
  tenant_id INTEGER
);
```

#### 3.3.2. Cập nhật bảng Todo
```sql
ALTER TABLE todos
ADD COLUMN deadline BIGINT NULL,
ADD COLUMN is_overdue BOOLEAN DEFAULT FALSE;
```

### 3.4. Tạo các DTO mới

#### 3.4.1. ScoreTodoDto
```typescript
export class ScoreTodoDto {
  @ApiProperty({
    description: 'Số sao đánh giá thực tế (1-5)',
    example: 4,
  })
  @IsInt()
  @Min(1)
  @Max(5)
  awardedStars: number;

  @ApiProperty({
    description: 'Phản hồi về công việc',
    example: 'Hoàn thành tốt nhiệm vụ, nhưng còn chậm tiến độ',
    required: false,
  })
  @IsOptional()
  @IsString()
  @MaxLength(500)
  feedback?: string;
}
```

#### 3.4.2. UserPerformanceResponseDto
```typescript
export class UserPerformanceResponseDto {
  @ApiProperty({
    description: 'Thông tin người dùng',
  })
  user: {
    id: number;
    fullName: string;
  };

  @ApiProperty({
    description: 'Tổng số nhiệm vụ',
    example: 15,
  })
  totalTasks: number;

  @ApiProperty({
    description: 'Số nhiệm vụ đã hoàn thành',
    example: 10,
  })
  completedTasks: number;

  @ApiProperty({
    description: 'Số nhiệm vụ đang thực hiện',
    example: 3,
  })
  inProgressTasks: number;

  @ApiProperty({
    description: 'Số nhiệm vụ trễ hạn',
    example: 2,
  })
  overdueTasks: number;

  @ApiProperty({
    description: 'Điểm trung bình',
    example: 4.2,
  })
  averageScore: number;
}
```

## 4. Lộ trình triển khai

### 4.1. Giai đoạn 1: Cơ chế chấm điểm
- Cập nhật cơ sở dữ liệu (thêm bảng TodoScore)
- Tạo các DTO liên quan đến chấm điểm
- Cập nhật TodoService và TodoRepository
- Tạo API chấm điểm

### 4.2. Giai đoạn 2: API thống kê cơ bản
- Tạo StatisticsService
- Tạo API thống kê hiệu suất cá nhân
- Tạo API thống kê hiệu suất phòng ban
- Tạo API thống kê hiệu suất dự án

### 4.3. Giai đoạn 3: Biểu đồ và thống kê nâng cao
- Tạo API biểu đồ Gantt
- Tạo API biểu đồ tiến độ OKRs
- Tích hợp với hệ thống phòng ban và OKRs

## 5. Các chỉ số thống kê và biểu đồ

### 5.1. Chỉ số hiệu suất cá nhân
- Tỷ lệ hoàn thành công việc = Số công việc hoàn thành / Tổng số công việc
- Điểm trung bình = Tổng điểm / Số công việc được chấm điểm
- Tỷ lệ đúng hạn = Số công việc hoàn thành đúng hạn / Tổng số công việc hoàn thành
- Thời gian trung bình hoàn thành công việc

### 5.2. Chỉ số hiệu suất phòng ban
- Tỷ lệ hoàn thành công việc của phòng ban
- Điểm trung bình của phòng ban
- Phân bố công việc giữa các thành viên
- So sánh hiệu suất giữa các thành viên

### 5.3. Chỉ số hiệu suất dự án
- Tiến độ dự án = Số công việc hoàn thành / Tổng số công việc
- Chất lượng dự án = Điểm trung bình của các công việc
- Tỷ lệ đúng hạn của dự án
- Phân bố công việc giữa các thành viên dự án

### 5.4. Biểu đồ và trực quan hóa
- **Biểu đồ Gantt**: Hiển thị tiến độ công việc theo thời gian
- **Biểu đồ Burndown**: Theo dõi tiến độ hoàn thành công việc
- **Biểu đồ cột**: So sánh hiệu suất giữa các thành viên/phòng ban
- **Biểu đồ tròn**: Phân bố trạng thái công việc
- **Biểu đồ nhiệt**: Hiển thị mức độ hoạt động theo thời gian

## 6. Kết luận

Kế hoạch phát triển tính năng chấm điểm và thống kê cho module Todolists sẽ giúp nâng cao khả năng quản lý và theo dõi hiệu suất công việc. Việc chỉ cho phép cấp trên chấm điểm sẽ đảm bảo tính khách quan và chính xác trong đánh giá. Các API thống kê và biểu đồ sẽ cung cấp cái nhìn tổng quan về tiến độ công việc, giúp quản lý và nhân viên có thể dễ dàng theo dõi và cải thiện hiệu suất.

Việc tích hợp với hệ thống OKRs sẽ giúp liên kết các công việc hàng ngày với mục tiêu chiến lược của tổ chức, tạo ra sự gắn kết và động lực cho nhân viên. Hệ thống thống kê và biểu đồ sẽ cung cấp dữ liệu quan trọng cho việc ra quyết định và cải thiện quy trình làm việc.
