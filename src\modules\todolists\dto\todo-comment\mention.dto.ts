import { ApiProperty } from '@nestjs/swagger';
import { IsInt, IsNotEmpty, IsString, MaxLength, Min } from 'class-validator';

/**
 * DTO cho người dùng được mention trong bình luận
 */
export class MentionDto {
  /**
   * ID của người dùng được mention
   * @example 123
   */
  @ApiProperty({
    description: 'ID của người dùng được mention',
    example: 123,
    required: true,
  })
  @IsNotEmpty({ message: 'ID người dùng không được để trống' })
  @IsInt({ message: 'ID người dùng phải là số nguyên' })
  @Min(1, { message: 'ID người dùng phải lớn hơn 0' })
  userId: number;

  /**
   * Tên người dùng được mention
   * @example "userA"
   */
  @ApiProperty({
    description: 'Tên người dùng được mention',
    example: 'userA',
    required: true,
  })
  @IsNotEmpty({ message: 'Tên người dùng không được để trống' })
  @IsString({ message: 'Tên người dùng phải là chuỗi' })
  @MaxLength(100, { message: 'Tên người dùng không được vượt quá 100 ký tự' })
  username: string;
}
