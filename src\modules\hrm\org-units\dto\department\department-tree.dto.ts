import { ApiProperty } from '@nestjs/swagger';

/**
 * DTO đại diện cho một nút trong cây phòng ban
 */
export class DepartmentTreeNodeDto {
  /**
   * ID của phòng ban
   */
  @ApiProperty({
    description: 'ID của phòng ban',
    example: 1,
  })
  id: number;

  /**
   * Tên phòng ban
   */
  @ApiProperty({
    description: 'Tên phòng ban',
    example: 'Ban Giám đốc',
  })
  name: string;

  /**
   * ID của phòng ban cha (cấp trên)
   */
  @ApiProperty({
    description: 'ID của phòng ban cha (cấp trên)',
    example: null,
    nullable: true,
  })
  parentId: number | null;

  /**
   * ID của người quản lý phòng ban
   */
  @ApiProperty({
    description: 'ID của người quản lý phòng ban',
    example: 1,
    nullable: true,
  })
  managerId: number | null;

  /**
   * Tên đầy đủ của người quản lý
   */
  @ApiProperty({
    description: 'Tên đầy đủ của người quản lý',
    example: 'Nguyễn Văn A',
    nullable: true,
  })
  managerName: string | null;

  /**
   * Số lượng nhân viên trong phòng ban
   */
  @ApiProperty({
    description: 'Số lượng nhân viên trong phòng ban',
    example: 5,
  })
  employeeCount: number;

  /**
   * Danh sách các phòng ban con trực thuộc
   */
  @ApiProperty({
    description: 'Danh sách các phòng ban con trực thuộc',
    type: [DepartmentTreeNodeDto],
  })
  children: DepartmentTreeNodeDto[];
}

/**
 * DTO đại diện cho toàn bộ cấu trúc cây phòng ban
 */
export class DepartmentTreeResponseDto {
  /**
   * Danh sách các phòng ban gốc (không có phòng ban cha)
   */
  @ApiProperty({
    description: 'Danh sách các phòng ban gốc (không có phòng ban cha)',
    type: [DepartmentTreeNodeDto],
  })
  departments: DepartmentTreeNodeDto[];

  /**
   * Tổng số phòng ban trong hệ thống
   */
  @ApiProperty({
    description: 'Tổng số phòng ban trong hệ thống',
    example: 10,
  })
  totalDepartments: number;
}
