import { Injectable, Logger } from '@nestjs/common';
import { DepartmentRepository } from '../repositories/department.repository';
import {
  CreateDepartmentDto,
  UpdateDepartmentDto,
  DepartmentQueryDto,
  DepartmentResponseDto,
  DepartmentManagerDto,
  ParentDepartmentDto,
  BulkDeleteDepartmentDto,
  BulkDeleteResponseDto,
} from '../dto/department';
import { Department } from '../entities/department.entity';
import { PaginatedResult } from '@/common/response/api-response-dto';
import { AppException } from '@/common';
import { HRM_ERROR_CODES } from '../../errors/hrm-error.code';

/**
 * Service for department operations
 */
@Injectable()
export class DepartmentService {
  private readonly logger = new Logger(DepartmentService.name);

  constructor(private readonly departmentRepository: DepartmentRepository) {}

  /**
   * Create a new department
   * @param tenantId ID tenant (required for tenant isolation)
   * @param dto Department creation DTO
   * @returns Created department
   */
  async create(
    tenantId: number,
    dto: CreateDepartmentDto,
  ): Promise<DepartmentResponseDto> {
    // Check if department with same name already exists
    const existingDepartment = await this.departmentRepository.findByName(
      tenantId,
      dto.name,
    );
    if (existingDepartment) {
      throw new AppException(
        HRM_ERROR_CODES.DEPARTMENT_NAME_ALREADY_EXISTS,
        `Phòng ban với tên "${dto.name}" đã tồn tại`,
      );
    }

    // Validate parent department if provided
    if (dto.parentId) {
      const parentDepartment = await this.departmentRepository.findById(
        tenantId,
        dto.parentId,
      );
      if (!parentDepartment) {
        throw new AppException(
          HRM_ERROR_CODES.DEPARTMENT_PARENT_NOT_FOUND,
          `Phòng ban cấp trên với ID ${dto.parentId} không tồn tại`,
        );
      }
    }

    // Create department
    const department = await this.departmentRepository.create(tenantId, {
      ...dto,
      createdAt: Date.now(),
    });

    return this.mapToResponseDto(department);
  }

  /**
   * Get all departments with pagination and filtering
   * @param tenantId ID tenant (required for tenant isolation)
   * @param query Query parameters
   * @returns Paginated list of departments
   */
  async findAll(
    tenantId: number,
    query: DepartmentQueryDto,
  ): Promise<PaginatedResult<DepartmentResponseDto>> {
    const result = await this.departmentRepository.findAllWithRelations(tenantId, query);

    return {
      items: result.items.map((rawData) =>
        this.mapRawDataToResponseDto(rawData),
      ),
      meta: result.meta,
    };
  }

  /**
   * Get department by ID
   * @param tenantId ID tenant (required for tenant isolation)
   * @param id Department ID
   * @returns Department or throws error if not found
   */
  async findById(tenantId: number, id: number): Promise<DepartmentResponseDto> {
    const department = await this.departmentRepository.findById(tenantId, id);
    if (!department) {
      throw new AppException(
        HRM_ERROR_CODES.DEPARTMENT_NOT_FOUND,
        `Không tìm thấy phòng ban với ID ${id}`,
      );
    }

    return this.mapToResponseDto(department);
  }

  /**
   * Update department
   * @param tenantId ID tenant (required for tenant isolation)
   * @param id Department ID
   * @param dto Update DTO
   * @returns Updated department
   */
  async update(
    tenantId: number,
    id: number,
    dto: UpdateDepartmentDto,
  ): Promise<DepartmentResponseDto> {
    // Check if department exists
    const existingDepartment = await this.departmentRepository.findById(
      tenantId,
      id,
    );
    if (!existingDepartment) {
      throw new AppException(
        HRM_ERROR_CODES.DEPARTMENT_NOT_FOUND,
        `Không tìm thấy phòng ban với ID ${id}`,
      );
    }

    // Check if new name is already taken
    if (dto.name && dto.name !== existingDepartment.name) {
      const departmentWithSameName = await this.departmentRepository.findByName(
        tenantId,
        dto.name,
      );
      if (departmentWithSameName && departmentWithSameName.id !== id) {
        throw new AppException(
          HRM_ERROR_CODES.DEPARTMENT_NAME_ALREADY_EXISTS,
          `Phòng ban với tên "${dto.name}" đã tồn tại`,
        );
      }
    }

    // Validate parent department if provided
    if (dto.parentId) {
      // Check for circular reference
      if (dto.parentId === id) {
        throw new AppException(
          HRM_ERROR_CODES.DEPARTMENT_CIRCULAR_REFERENCE,
          'Phòng ban không thể là phòng ban cấp trên của chính nó',
        );
      }

      // Check if parent exists
      const parentDepartment = await this.departmentRepository.findById(
        tenantId,
        dto.parentId,
      );
      if (!parentDepartment) {
        throw new AppException(
          HRM_ERROR_CODES.DEPARTMENT_PARENT_NOT_FOUND,
          `Phòng ban cấp trên với ID ${dto.parentId} không tồn tại`,
        );
      }

      // Check for indirect circular reference
      if (await this.wouldCreateCircularReference(tenantId, id, dto.parentId)) {
        throw new AppException(
          HRM_ERROR_CODES.DEPARTMENT_CIRCULAR_REFERENCE,
          'Thiết lập này sẽ tạo ra vòng tròn tham chiếu trong cấu trúc phòng ban',
        );
      }
    }

    // Update department
    const updatedDepartment = await this.departmentRepository.update(
      tenantId,
      id,
      {
        ...dto,
      },
    );

    if (!updatedDepartment) {
      throw new AppException(
        HRM_ERROR_CODES.DEPARTMENT_NOT_FOUND,
        `Không tìm thấy phòng ban với ID ${id}`,
      );
    }

    return this.mapToResponseDto(updatedDepartment);
  }

  /**
   * Xóa phòng ban
   * @param tenantId ID tenant (required for tenant isolation)
   * @param id Department ID
   * @returns True if deleted successfully
   */
  async delete(tenantId: number, id: number): Promise<boolean> {
    // Check if department exists
    const department = await this.departmentRepository.findById(tenantId, id);
    if (!department) {
      throw new AppException(
        HRM_ERROR_CODES.DEPARTMENT_NOT_FOUND,
        `Không tìm thấy phòng ban với ID ${id}`,
      );
    }

    // Check if department has children
    const hasChildren = await this.departmentRepository.hasChildren(
      tenantId,
      id,
    );
    if (hasChildren) {
      throw new AppException(
        HRM_ERROR_CODES.DEPARTMENT_HAS_CHILDREN,
        'Không thể xóa phòng ban này vì có phòng ban con trực thuộc. Hãy xóa các phòng ban con trước.',
      );
    }

    // Delete department
    const deleted = await this.departmentRepository.delete(tenantId, id);
    if (!deleted) {
      throw new AppException(
        HRM_ERROR_CODES.DEPARTMENT_NOT_FOUND,
        `Không tìm thấy phòng ban với ID ${id}`,
      );
    }

    return true;
  }

  /**
   * Check if setting a new parent would create a circular reference
   * @param tenantId ID tenant (required for tenant isolation)
   * @param departmentId Department ID
   * @param newParentId New parent ID
   * @returns True if it would create a circular reference
   */
  private async wouldCreateCircularReference(
    tenantId: number,
    departmentId: number,
    newParentId: number,
  ): Promise<boolean> {
    // Get all departments to build the hierarchy
    const allDepartments =
      await this.departmentRepository.getAllByTenantId(tenantId);

    // Create a map for quick lookup
    const departmentMap = new Map<number, number | null>();
    for (const dept of allDepartments) {
      departmentMap.set(dept.id, dept.parentId);
    }

    // Simulate the new parent relationship
    departmentMap.set(departmentId, newParentId);

    // Check for circular reference
    let currentId: number | null = newParentId;
    const visited = new Set<number>();

    while (currentId !== null && currentId !== undefined) {
      if (visited.has(currentId)) {
        return true; // Circular reference detected
      }

      visited.add(currentId);
      currentId = departmentMap.get(currentId) ?? null;

      // If we reached the department we're updating, it's a circular reference
      if (currentId === departmentId) {
        return true;
      }
    }

    return false;
  }

  /**
   * Map department entity to response DTO
   * @param department Department entity
   * @returns Department response DTO
   */
  private mapToResponseDto(department: Department): DepartmentResponseDto {
    const responseDto = new DepartmentResponseDto();

    responseDto.id = department.id;
    responseDto.name = department.name;
    responseDto.description = department.description;
    responseDto.managerId = department.managerId;
    responseDto.parentId = department.parentId;
    responseDto.createdAt = department.createdAt ?? Date.now();
    responseDto.manager = null;
    responseDto.parentDepartment = null;

    return responseDto;
  }

  /**
   * Map raw data from join query to response DTO
   * @param rawData Raw data from database join query
   * @returns Department response DTO with manager and parent department info
   */
  private mapRawDataToResponseDto(rawData: any): DepartmentResponseDto {
    const responseDto = new DepartmentResponseDto();

    responseDto.id = rawData.department_id;
    responseDto.name = rawData.department_name;
    responseDto.description = rawData.department_description;
    responseDto.managerId = rawData.department_managerId;
    responseDto.parentId = rawData.department_parentId;
    responseDto.createdAt = rawData.department_createdAt ?? Date.now();

    // Map manager information if exists
    if (rawData.manager_id) {
      responseDto.manager = {
        id: rawData.manager_id,
        fullName: rawData.manager_fullName || null,
        email: rawData.manager_email || null,
        position: rawData.manager_position || null,
      };
    } else {
      responseDto.manager = null;
    }

    // Map parent department information if exists
    if (rawData.parent_dept_id && rawData.parent_dept_name) {
      responseDto.parentDepartment = {
        id: rawData.parent_dept_id,
        name: rawData.parent_dept_name,
      };
    } else {
      responseDto.parentDepartment = null;
    }

    return responseDto;
  }

  /**
   * Xóa nhiều phòng ban
   * @param tenantId ID tenant (required for tenant isolation)
   * @param dto DTO chứa danh sách ID phòng ban cần xóa
   * @returns Kết quả xóa nhiều phòng ban
   */
  async bulkDelete(
    tenantId: number,
    dto: BulkDeleteDepartmentDto,
  ): Promise<BulkDeleteResponseDto> {
    const { ids } = dto;

    // Tìm tất cả phòng ban theo danh sách ID
    const departments = await this.departmentRepository.findByIds(
      tenantId,
      ids,
    );
    const foundIds = departments.map((dept) => dept.id);
    const notFoundIds = ids.filter((id) => !foundIds.includes(id));

    const result: BulkDeleteResponseDto = {
      totalRequested: ids.length,
      successCount: 0,
      failureCount: 0,
      deletedIds: [],
      failures: [],
    };

    // Thêm các ID không tìm thấy vào danh sách lỗi
    notFoundIds.forEach((id) => {
      result.failures.push({
        id,
        reason: `Không tìm thấy phòng ban với ID ${id}`,
      });
    });

    // Kiểm tra từng phòng ban có thể xóa được không
    const deletableIds: number[] = [];

    for (const department of departments) {
      // Kiểm tra phòng ban có phòng ban con không
      const hasChildren = await this.departmentRepository.hasChildren(
        tenantId,
        department.id,
      );
      if (hasChildren) {
        result.failures.push({
          id: department.id,
          reason:
            'Không thể xóa phòng ban này vì có phòng ban con trực thuộc. Hãy xóa các phòng ban con trước.',
        });
      } else {
        deletableIds.push(department.id);
      }
    }

    // Thực hiện xóa các phòng ban có thể xóa
    if (deletableIds.length > 0) {
      let deletedCount = 0;
      for (const id of deletableIds) {
        try {
          const deleted = await this.departmentRepository.delete(tenantId, id);
          if (deleted) {
            deletedCount++;
            result.deletedIds.push(id);
          }
        } catch (error) {
          result.failures.push({
            id,
            reason: `Lỗi khi xóa: ${error.message}`,
          });
        }
      }
      result.successCount = deletedCount;
    }

    result.failureCount = result.failures.length;

    this.logger.log(
      `Bulk delete departments: ${result.successCount} thành công, ${result.failureCount} thất bại`,
    );

    return result;
  }
}
