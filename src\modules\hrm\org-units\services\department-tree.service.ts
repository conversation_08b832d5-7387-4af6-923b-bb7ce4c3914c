import { Injectable, Logger } from '@nestjs/common';
import { DepartmentRepository } from '../repositories/department.repository';
import { UserRepository } from '@/modules/auth/repositories/user.repository';
import { Department } from '../entities/department.entity';
import {
  DepartmentTreeNodeDto,
  DepartmentTreeResponseDto,
} from '../dto/department/department-tree.dto';
import { User } from '@/modules/auth/entities/user.entity';

/**
 * Service quản lý cấu trúc cây phòng ban
 */
@Injectable()
export class DepartmentTreeService {
  private readonly logger = new Logger(DepartmentTreeService.name);

  constructor(
    private readonly departmentRepository: DepartmentRepository,
    private readonly userRepository: UserRepository,
  ) {}

  /**
   * Lấy cấu trúc cây phòng ban
   * @param tenantId ID tenant (required for tenant isolation)
   * @returns Cấu trúc cây phòng ban
   */
  async getDepartmentTree(
    tenantId: number,
  ): Promise<DepartmentTreeResponseDto> {
    // Lấy tất cả phòng ban
    const allDepartments =
      await this.departmentRepository.getAllByTenantId(tenantId);

    // Tìm tất cả các ID quản lý phòng ban
    const managerIds = allDepartments
      .filter((dept) => dept.managerId !== null)
      .map((dept) => dept.managerId) as number[];

    // Lấy thông tin của tất cả người quản lý
    const managers: User[] =
      managerIds.length > 0
        ? await this.userRepository.findByIds(managerIds)
        : [];

    // Tạo map người quản lý theo ID
    const managerMap = new Map<number, User>();
    managers.forEach((manager) => {
      managerMap.set(manager.id, manager);
    });

    // Tạo map số lượng nhân viên theo phòng ban
    const employeeCountMap = await this.getEmployeeCountByDepartment(tenantId);

    // Tạo cấu trúc cây từ danh sách phẳng
    const treeStructure = this.buildDepartmentTree(
      allDepartments,
      null,
      managerMap,
      employeeCountMap,
    );

    // Xây dựng response
    const response = new DepartmentTreeResponseDto();
    response.departments = treeStructure;
    response.totalDepartments = allDepartments.length;

    return response;
  }

  /**
   * Đếm số lượng nhân viên trong mỗi phòng ban
   * @param tenantId ID tenant (required for tenant isolation)
   * @returns Map số lượng nhân viên theo ID phòng ban
   */
  private async getEmployeeCountByDepartment(
    tenantId: number,
  ): Promise<Map<number, number>> {
    const counts = await this.userRepository.countByDepartments(tenantId);
    const countMap = new Map<number, number>();

    // Chuyển kết quả truy vấn thành Map
    counts.forEach((item) => {
      countMap.set(item.departmentId, item.count);
    });

    return countMap;
  }

  /**
   * Xây dựng cấu trúc cây phòng ban
   * @param departments Danh sách tất cả phòng ban
   * @param parentId ID phòng ban cha (hoặc null cho phòng ban gốc)
   * @param managerMap Map thông tin người quản lý
   * @param employeeCountMap Map số lượng nhân viên theo phòng ban
   * @returns Cấu trúc cây phòng ban
   */
  private buildDepartmentTree(
    departments: Department[],
    parentId: number | null,
    managerMap: Map<number, User>,
    employeeCountMap: Map<number, number>,
  ): DepartmentTreeNodeDto[] {
    return departments
      .filter((dept) => dept.parentId === parentId)
      .map((dept) => {
        const node = new DepartmentTreeNodeDto();
        node.id = dept.id;
        node.name = dept.name;
        node.parentId = dept.parentId;
        node.managerId = dept.managerId;

        // Thêm tên người quản lý nếu có
        node.managerName =
          dept.managerId && managerMap.has(dept.managerId)
            ? managerMap.get(dept.managerId)?.fullName || null
            : null;

        // Thêm số lượng nhân viên
        node.employeeCount = employeeCountMap.get(dept.id) || 0;

        // Đệ quy để lấy các phòng ban con
        node.children = this.buildDepartmentTree(
          departments,
          dept.id,
          managerMap,
          employeeCountMap,
        );

        return node;
      });
  }
}
