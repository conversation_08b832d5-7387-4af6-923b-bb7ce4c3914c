import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { CompanyAccount } from '../entities/company-account.entity';

/**
 * Repository cho CompanyAccount
 */
@Injectable()
export class CompanyAccountRepository {
  constructor(
    @InjectRepository(CompanyAccount)
    private readonly repository: Repository<CompanyAccount>,
  ) {}

  /**
   * Tìm công ty theo email
   * @param email Email cần tìm
   * @returns Thông tin công ty hoặc null nếu không tìm thấy
   */
  async findByEmail(email: string): Promise<CompanyAccount | null> {
    return this.repository.findOne({ where: { companyEmail: email } });
  }

  /**
   * Tìm công ty theo mã số thuế
   * @param taxCode Mã số thuế cần tìm
   * @returns Thông tin công ty hoặc null nếu không tìm thấy
   */
  async findByTaxCode(taxCode: string): Promise<CompanyAccount | null> {
    return this.repository.findOne({ where: { taxCode } });
  }

  /**
   * Tìm công ty theo subdomain
   * @param subdomain Subdomain cần tìm
   * @returns Thông tin công ty hoặc null nếu không tìm thấy
   */
  async findBySubdomain(subdomain: string): Promise<CompanyAccount | null> {
    return this.repository.findOne({ where: { subdomain } });
  }

  /**
   * Tìm công ty theo ID
   * @param id ID cần tìm
   * @returns Thông tin công ty hoặc null nếu không tìm thấy
   */
  async findById(id: number): Promise<CompanyAccount | null> {
    return this.repository.findOne({ where: { id } });
  }

  /**
   * Tạo mới công ty
   * @param data Dữ liệu công ty
   * @returns Thông tin công ty đã tạo
   */
  async create(data: Partial<CompanyAccount>): Promise<CompanyAccount> {
    const company = this.repository.create(data);
    return this.repository.save(company);
  }

  /**
   * Cập nhật thông tin công ty
   * @param id ID công ty cần cập nhật
   * @param data Dữ liệu cần cập nhật
   * @returns Thông tin công ty đã cập nhật hoặc null nếu không tìm thấy
   * @throws Error nếu không tìm thấy công ty sau khi cập nhật
   */
  async update(
    id: number,
    data: Partial<CompanyAccount>,
  ): Promise<CompanyAccount> {
    await this.repository.update(id, data);
    const updatedCompany = await this.findById(id);

    if (!updatedCompany) {
      throw new Error(`Company with ID ${id} not found after update`);
    }

    return updatedCompany;
  }
}
