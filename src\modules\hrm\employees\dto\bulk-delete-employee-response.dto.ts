import { ApiProperty } from '@nestjs/swagger';

/**
 * DTO cho kết quả xóa nhiều nhân viên
 */
export class BulkDeleteEmployeeResponseDto {
  /**
   * Tổng số nhân viên được yêu cầu xóa
   */
  @ApiProperty({
    description: 'Tổng số nhân viên được yêu cầu xóa',
    example: 5,
    type: Number,
  })
  totalRequested: number;

  /**
   * Số nhân viên đã xóa thành công
   */
  @ApiProperty({
    description: 'Số nhân viên đã xóa thành công',
    example: 3,
    type: Number,
  })
  successCount: number;

  /**
   * Số nhân viên không thể xóa
   */
  @ApiProperty({
    description: 'Số nhân viên không thể xóa',
    example: 2,
    type: Number,
  })
  failureCount: number;

  /**
   * Danh sách ID các nhân viên đã xóa thành công
   */
  @ApiProperty({
    description: 'Danh sách ID các nhân viên đã xóa thành công',
    example: [1, 3, 5],
    type: [Number],
  })
  deletedIds: number[];

  /**
   * Danh sách lỗi cho các nhân viên không thể xóa
   */
  @ApiProperty({
    description: 'Danh sách lỗi cho các nhân viên không thể xóa',
    example: [
      { id: 2, reason: 'Nhân viên không tồn tại' },
      { id: 4, reason: 'Nhân viên đang quản lý nhân viên khác' },
    ],
    type: 'array',
    items: {
      type: 'object',
      properties: {
        id: { type: 'number', description: 'ID nhân viên' },
        reason: { type: 'string', description: 'Lý do không thể xóa' },
      },
    },
  })
  failures: Array<{ id: number; reason: string }>;
}
