import { Injectable, Logger } from '@nestjs/common';
import { AttendanceRepository } from '../repositories/attendance.repository';
import { Attendance } from '../entities/attendance.entity';
import { CreateAttendanceDto } from '../dto/create-attendance.dto';
import { UpdateAttendanceDto } from '../dto/update-attendance.dto';
import { AttendanceQueryDto } from '../dto/attendance-query.dto';
import { PaginatedResult } from '@/common/response/api-response-dto';
import { AppException } from '@/common/exceptions/app.exception';
import { HRM_ERROR_CODES } from '../../errors/hrm-error.code';
import { AttendanceStatus } from '../enum/attendance-status.enum';

/**
 * Service for attendance management with tenant isolation
 */
@Injectable()
export class AttendanceService {
  private readonly logger = new Logger(AttendanceService.name);

  constructor(private readonly attendanceRepository: AttendanceRepository) {}

  /**
   * Find all attendance records with pagination and filtering
   * @param tenantId ID tenant (required for tenant isolation)
   * @param query Query parameters
   * @returns Paginated list of attendance records
   */
  async findAll(
    tenantId: number,
    query: AttendanceQueryDto,
  ): Promise<PaginatedResult<Attendance>> {
    try {
      return await this.attendanceRepository.findAll(tenantId, query);
    } catch (error) {
      this.logger.error(
        `Error finding attendance records: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        HRM_ERROR_CODES.ATTENDANCE_FIND_FAILED,
        `Failed to find attendance records: ${error.message}`,
      );
    }
  }

  /**
   * Find attendance record by ID
   * @param tenantId ID tenant (required for tenant isolation)
   * @param id Attendance ID
   * @returns Attendance record
   * @throws AppException if attendance record not found
   */
  async findById(tenantId: number, id: number): Promise<Attendance> {
    try {
      const attendance = await this.attendanceRepository.findById(tenantId, id);
      if (!attendance) {
        throw new AppException(
          HRM_ERROR_CODES.ATTENDANCE_NOT_FOUND,
          `Attendance record with ID ${id} not found`,
        );
      }
      return attendance;
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(
        `Error finding attendance record by ID: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        HRM_ERROR_CODES.ATTENDANCE_FIND_FAILED,
        `Failed to find attendance record: ${error.message}`,
      );
    }
  }

  /**
   * Find attendance records by employee ID
   * @param tenantId ID tenant (required for tenant isolation)
   * @param employeeId Employee ID
   * @returns List of attendance records
   */
  async findByEmployeeId(
    tenantId: number,
    employeeId: number,
  ): Promise<Attendance[]> {
    try {
      return await this.attendanceRepository.findByEmployeeId(
        tenantId,
        employeeId,
      );
    } catch (error) {
      this.logger.error(
        `Error finding attendance records by employee ID: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        HRM_ERROR_CODES.ATTENDANCE_FIND_FAILED,
        `Failed to find attendance records for employee: ${error.message}`,
      );
    }
  }

  /**
   * Find attendance record by employee ID and work date
   * @param tenantId ID tenant (required for tenant isolation)
   * @param employeeId Employee ID
   * @param workDate Work date
   * @returns Attendance record or null if not found
   */
  async findByEmployeeIdAndDate(
    tenantId: number,
    employeeId: number,
    workDate: Date,
  ): Promise<Attendance | null> {
    try {
      return await this.attendanceRepository.findByEmployeeIdAndDate(
        tenantId,
        employeeId,
        workDate,
      );
    } catch (error) {
      this.logger.error(
        `Error finding attendance record by employee and date: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        HRM_ERROR_CODES.ATTENDANCE_FIND_FAILED,
        `Failed to find attendance record: ${error.message}`,
      );
    }
  }

  /**
   * Find attendance records within date range
   * @param tenantId ID tenant (required for tenant isolation)
   * @param startDate Start date
   * @param endDate End date
   * @param employeeId Employee ID (optional)
   * @returns List of attendance records
   */
  async findByDateRange(
    tenantId: number,
    startDate: Date,
    endDate: Date,
    employeeId?: number,
  ): Promise<Attendance[]> {
    try {
      return await this.attendanceRepository.findByDateRange(
        tenantId,
        startDate,
        endDate,
        employeeId,
      );
    } catch (error) {
      this.logger.error(
        `Error finding attendance records by date range: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        HRM_ERROR_CODES.ATTENDANCE_FIND_FAILED,
        `Failed to find attendance records: ${error.message}`,
      );
    }
  }

  /**
   * Create a new attendance record
   * @param tenantId ID tenant (required for tenant isolation)
   * @param createAttendanceDto Attendance data
   * @param userId ID of the user creating the record
   * @returns Created attendance record
   * @throws AppException if attendance record already exists for the date
   */
  async create(
    tenantId: number,
    createAttendanceDto: CreateAttendanceDto,
    userId: number,
  ): Promise<Attendance> {
    try {
      const workDate = new Date(createAttendanceDto.workDate);

      // Check if attendance record already exists for this employee and date
      const existingAttendance =
        await this.attendanceRepository.existsByEmployeeIdAndDate(
          tenantId,
          createAttendanceDto.employeeId,
          workDate,
        );

      if (existingAttendance) {
        throw new AppException(
          HRM_ERROR_CODES.ATTENDANCE_ALREADY_EXISTS,
          `Attendance record already exists for employee ${createAttendanceDto.employeeId} on ${createAttendanceDto.workDate}`,
        );
      }

      // Calculate work hours if check-in and check-out times are provided
      let workHours = createAttendanceDto.workHours;
      if (
        createAttendanceDto.checkInTime &&
        createAttendanceDto.checkOutTime &&
        !workHours
      ) {
        const workDurationMs =
          createAttendanceDto.checkOutTime - createAttendanceDto.checkInTime;
        workHours = Math.floor(workDurationMs / (1000 * 60)); // Convert to minutes

        // Subtract break time if provided
        if (createAttendanceDto.breakTime) {
          workHours -= createAttendanceDto.breakTime;
        }
      }

      // Create attendance record
      const now = Date.now();
      const attendance = await this.attendanceRepository.create(tenantId, {
        ...createAttendanceDto,
        workDate,
        workHours,
        status: createAttendanceDto.status || AttendanceStatus.PRESENT,
        breakTime: createAttendanceDto.breakTime || 0,
        overtimeHours: createAttendanceDto.overtimeHours || 0,
        isManual: createAttendanceDto.isManual || false,
        createdAt: now,
        updatedAt: now,
        createdBy: userId,
        updatedBy: userId,
      });

      return attendance;
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(
        `Error creating attendance record: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        HRM_ERROR_CODES.ATTENDANCE_CREATE_FAILED,
        `Failed to create attendance record: ${error.message}`,
      );
    }
  }

  /**
   * Update attendance record
   * @param tenantId ID tenant (required for tenant isolation)
   * @param id Attendance ID
   * @param updateAttendanceDto Updated attendance data
   * @param userId ID of the user updating the record
   * @returns Updated attendance record
   * @throws AppException if attendance record not found
   */
  async update(
    tenantId: number,
    id: number,
    updateAttendanceDto: UpdateAttendanceDto,
    userId: number,
  ): Promise<Attendance> {
    try {
      // Check if attendance record exists
      const attendance = await this.findById(tenantId, id);

      // Calculate work hours if check-in and check-out times are updated
      let workHours = updateAttendanceDto.workHours;
      if (
        updateAttendanceDto.checkInTime &&
        updateAttendanceDto.checkOutTime &&
        !workHours
      ) {
        const workDurationMs =
          updateAttendanceDto.checkOutTime - updateAttendanceDto.checkInTime;
        workHours = Math.floor(workDurationMs / (1000 * 60)); // Convert to minutes

        // Subtract break time if provided
        if (updateAttendanceDto.breakTime !== undefined) {
          workHours -= updateAttendanceDto.breakTime;
        } else if (attendance.breakTime) {
          workHours -= attendance.breakTime;
        }
      }

      // Update attendance record
      const now = Date.now();
      const updateData: any = {
        ...updateAttendanceDto,
        workHours,
        isManual:
          updateAttendanceDto.isManual !== undefined
            ? updateAttendanceDto.isManual
            : true,
        adjustedBy: userId,
        adjustedAt: now,
        updatedAt: now,
        updatedBy: userId,
      };

      // Convert workDate from string to Date if provided
      if (updateAttendanceDto.workDate) {
        updateData.workDate = new Date(updateAttendanceDto.workDate);
      }

      const updatedAttendance = await this.attendanceRepository.update(
        tenantId,
        id,
        updateData,
      );

      if (!updatedAttendance) {
        throw new AppException(
          HRM_ERROR_CODES.ATTENDANCE_UPDATE_FAILED,
          `Failed to update attendance record with ID ${id}`,
        );
      }

      return updatedAttendance;
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(
        `Error updating attendance record: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        HRM_ERROR_CODES.ATTENDANCE_UPDATE_FAILED,
        `Failed to update attendance record: ${error.message}`,
      );
    }
  }

  /**
   * Delete attendance record
   * @param tenantId ID tenant (required for tenant isolation)
   * @param id Attendance ID
   * @returns True if deleted successfully
   * @throws AppException if attendance record not found or delete fails
   */
  async delete(tenantId: number, id: number): Promise<boolean> {
    try {
      // Check if attendance record exists
      await this.findById(tenantId, id);

      // Delete attendance record
      const deleted = await this.attendanceRepository.delete(tenantId, id);
      if (!deleted) {
        throw new AppException(
          HRM_ERROR_CODES.ATTENDANCE_DELETE_FAILED,
          `Failed to delete attendance record with ID ${id}`,
        );
      }

      return true;
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(
        `Error deleting attendance record: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        HRM_ERROR_CODES.ATTENDANCE_DELETE_FAILED,
        `Failed to delete attendance record: ${error.message}`,
      );
    }
  }

  /**
   * Check-in employee
   * @param tenantId ID tenant (required for tenant isolation)
   * @param employeeId Employee ID
   * @param checkInData Check-in data (location, IP, etc.)
   * @param userId ID of the user performing check-in
   * @returns Created or updated attendance record
   */
  async checkIn(
    tenantId: number,
    employeeId: number,
    checkInData: {
      location?: string;
      ip?: string;
      notes?: string;
    },
    userId: number,
  ): Promise<Attendance> {
    try {
      const today = new Date();
      today.setHours(0, 0, 0, 0); // Set to start of day

      // Check if attendance record already exists for today
      let attendance = await this.attendanceRepository.findByEmployeeIdAndDate(
        tenantId,
        employeeId,
        today,
      );

      const now = Date.now();

      if (attendance) {
        // Update existing record with check-in time
        if (attendance.checkInTime) {
          throw new AppException(
            HRM_ERROR_CODES.ATTENDANCE_ALREADY_CHECKED_IN,
            'Employee has already checked in today',
          );
        }

        attendance = await this.attendanceRepository.update(
          tenantId,
          attendance.id,
          {
            checkInTime: now,
            checkInLocation: checkInData.location,
            checkInIp: checkInData.ip,
            notes: checkInData.notes,
            status: AttendanceStatus.PRESENT,
            updatedAt: now,
            updatedBy: userId,
          },
        );
      } else {
        // Create new attendance record
        attendance = await this.attendanceRepository.create(tenantId, {
          employeeId,
          workDate: today,
          checkInTime: now,
          checkInLocation: checkInData.location,
          checkInIp: checkInData.ip,
          notes: checkInData.notes,
          status: AttendanceStatus.PRESENT,
          breakTime: 0,
          overtimeHours: 0,
          isManual: false,
          createdAt: now,
          updatedAt: now,
          createdBy: userId,
          updatedBy: userId,
        });
      }

      return attendance!;
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Error during check-in: ${error.message}`, error.stack);
      throw new AppException(
        HRM_ERROR_CODES.ATTENDANCE_CHECKIN_FAILED,
        `Failed to check in: ${error.message}`,
      );
    }
  }

  /**
   * Check-out employee
   * @param tenantId ID tenant (required for tenant isolation)
   * @param employeeId Employee ID
   * @param checkOutData Check-out data (location, IP, etc.)
   * @param userId ID of the user performing check-out
   * @returns Updated attendance record
   */
  async checkOut(
    tenantId: number,
    employeeId: number,
    checkOutData: {
      location?: string;
      ip?: string;
      notes?: string;
    },
    userId: number,
  ): Promise<Attendance> {
    try {
      const today = new Date();
      today.setHours(0, 0, 0, 0); // Set to start of day

      // Find attendance record for today
      const attendance =
        await this.attendanceRepository.findByEmployeeIdAndDate(
          tenantId,
          employeeId,
          today,
        );

      if (!attendance) {
        throw new AppException(
          HRM_ERROR_CODES.ATTENDANCE_NOT_FOUND,
          'No attendance record found for today. Please check in first.',
        );
      }

      if (!attendance.checkInTime) {
        throw new AppException(
          HRM_ERROR_CODES.ATTENDANCE_NOT_CHECKED_IN,
          'Employee has not checked in today',
        );
      }

      if (attendance.checkOutTime) {
        throw new AppException(
          HRM_ERROR_CODES.ATTENDANCE_ALREADY_CHECKED_OUT,
          'Employee has already checked out today',
        );
      }

      const now = Date.now();

      // Calculate work hours
      const workDurationMs = now - attendance.checkInTime;
      let workHours = Math.floor(workDurationMs / (1000 * 60)); // Convert to minutes

      // Subtract break time
      if (attendance.breakTime) {
        workHours -= attendance.breakTime;
      }

      // Update attendance record with check-out time
      const updatedAttendance = await this.attendanceRepository.update(
        tenantId,
        attendance.id,
        {
          checkOutTime: now,
          checkOutLocation: checkOutData.location,
          checkOutIp: checkOutData.ip,
          workHours,
          notes: checkOutData.notes || attendance.notes,
          updatedAt: now,
          updatedBy: userId,
        },
      );

      return updatedAttendance!;
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(
        `Error during check-out: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        HRM_ERROR_CODES.ATTENDANCE_CHECKOUT_FAILED,
        `Failed to check out: ${error.message}`,
      );
    }
  }

  /**
   * Get attendance statistics for employee within date range
   * @param tenantId ID tenant (required for tenant isolation)
   * @param employeeId Employee ID
   * @param startDate Start date
   * @param endDate End date
   * @returns Attendance statistics
   */
  async getAttendanceStats(
    tenantId: number,
    employeeId: number,
    startDate: Date,
    endDate: Date,
  ): Promise<{
    totalDays: number;
    presentDays: number;
    absentDays: number;
    lateDays: number;
    totalWorkHours: number;
    totalOvertimeHours: number;
    averageWorkHours: number;
  }> {
    try {
      const stats = await this.attendanceRepository.getAttendanceStats(
        tenantId,
        employeeId,
        startDate,
        endDate,
      );

      return {
        ...stats,
        averageWorkHours:
          stats.totalDays > 0
            ? Math.round(stats.totalWorkHours / stats.totalDays)
            : 0,
      };
    } catch (error) {
      this.logger.error(
        `Error getting attendance statistics: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        HRM_ERROR_CODES.ATTENDANCE_STATS_FAILED,
        `Failed to get attendance statistics: ${error.message}`,
      );
    }
  }
}
