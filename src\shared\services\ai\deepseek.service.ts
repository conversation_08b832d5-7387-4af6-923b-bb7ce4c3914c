import { Injectable, Logger } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { ConfigService, ConfigType, ServicesConfig } from '@config';
import { AppException, ErrorCode } from '@common/exceptions/app.exception';
import { firstValueFrom } from 'rxjs';

/**
 * Interface cho DeepSeek Model
 * Khớp với cấu trúc JSON mà API trả về
 */
interface DeepSeekModel {
  id: string;
  object: string;
  owned_by: string;
}

/**
 * Interface cho DeepSeek Models Response
 */
interface DeepSeekModelsResponse {
  object: string;
  data: DeepSeekModel[];
}

/**
 * Service tương tác với DeepSeek API
 */
@Injectable()
export class DeepSeekService {
  private readonly logger = new Logger(DeepSeekService.name);
  private readonly apiBaseUrl = 'https://api.deepseek.com/v1';

  constructor(
    private readonly configService: ConfigService,
    private readonly httpService: HttpService,
  ) {}

  /**
   * Lấy API key từ options hoặc từ biến môi trường
   * @returns API key hợp lệ hoặc ném ra lỗi nếu không tìm thấy
   * @throws Error nếu không tìm thấy API key hợp lệ
   */
  private getApiKey(): string {
    const servicesConfig = this.configService.getConfig<ServicesConfig>(
      ConfigType.Services,
    );
    return servicesConfig.deepseek?.apiKey || '';
  }

  /**
   * Lấy danh sách model từ DeepSeek API
   * @param options Tùy chọn bổ sung (apiKey, useEnvKey)
   * @returns Danh sách model từ DeepSeek
   * @throws AppException nếu có lỗi khi lấy danh sách model
   */
  async getModels(options?: { apiKey?: string }): Promise<DeepSeekModel[]> {
    try {
      // Thiết lập timeout cho request
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 30000); // 30 seconds timeout

      // Gọi REST API để lấy danh sách model
      const response = await firstValueFrom(
        this.httpService.get<DeepSeekModelsResponse>(
          `${this.apiBaseUrl}/models`,
          {
            headers: {
              'Content-Type': 'application/json',
              Authorization: `Bearer ${options?.apiKey || this.getApiKey()}`,
            },
            signal: controller.signal,
          },
        ),
      );

      clearTimeout(timeoutId);

      // Lấy danh sách model từ response
      const models = response.data.data || [];

      this.logger.log(`Retrieved ${models.length} models from DeepSeek`);
      return models;
    } catch (error: any) {
      this.logger.error(
        `Error retrieving models from DeepSeek: ${error.message}`,
        error.stack,
      );

      // Xử lý các lỗi khi kết nối DeepSeek API
      if (error.response) {
        // Lỗi từ API response
        const status = error.response.status;
        const data = error.response.data;

        if (status === 429) {
          throw new AppException(
            ErrorCode.OPENAI_QUOTA_EXCEEDED,
            'Đã vượt quá giới hạn sử dụng DeepSeek API',
          );
        }

        if (status === 401 || status === 403) {
          throw new AppException(
            ErrorCode.OPENAI_API_ERROR,
            'Lỗi xác thực API key: ' +
              (data?.error?.message || 'Không có quyền truy cập'),
          );
        }

        throw new AppException(
          ErrorCode.OPENAI_API_ERROR,
          `Lỗi API (${status}): ${data?.error?.message || 'Không xác định'}`,
        );
      }

      if (error.code === 'ECONNABORTED' || error.message.includes('timeout')) {
        throw new AppException(
          ErrorCode.OPENAI_TIMEOUT,
          'Kết nối đến DeepSeek API bị gián đoạn hoặc quá thời gian chờ',
        );
      }

      if (error.code === 'ENOTFOUND' || error.message.includes('network')) {
        throw new AppException(
          ErrorCode.OPENAI_TIMEOUT,
          'Lỗi kết nối đến DeepSeek API',
        );
      }

      // Các lỗi khác
      throw new AppException(
        ErrorCode.OPENAI_API_ERROR,
        'Lỗi khi lấy danh sách model: ' + error.message,
      );
    }
  }
}
