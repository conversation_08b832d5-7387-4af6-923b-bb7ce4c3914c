# Task ID: 32
# Title: Update DepartmentService with TenantId Parameters
# Status: done
# Dependencies: 31
# Priority: high
# Description: Modify all public methods in DepartmentService to accept tenantId as the first parameter and pass it to repository calls.
# Details:
Update src/modules/hrm/org-units/services/department.service.ts to add tenantId parameter to all public methods. Ensure all repository method calls include the tenantId parameter.

# Test Strategy:
Verify all service methods accept tenantId and pass it correctly to repository methods.
