import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsNotEmpty } from 'class-validator';
import { ProjectMemberRole } from '../../enum/project-member-role.enum';

/**
 * DTO cho cập nhật vai trò thành viên dự án
 */
export class UpdateProjectMemberDto {
  /**
   * Vai trò của thành viên trong dự án
   * @example "admin"
   */
  @ApiProperty({
    description: 'Vai trò của thành viên trong dự án',
    enum: ProjectMemberRole,
    example: ProjectMemberRole.ADMIN,
    required: true,
  })
  @IsNotEmpty({ message: 'Vai trò không được để trống' })
  @IsEnum(ProjectMemberRole, { message: 'Vai trò không hợp lệ' })
  role: ProjectMemberRole;
}
