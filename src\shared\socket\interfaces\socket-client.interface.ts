import { Socket } from 'socket.io';
import { JwtPayload } from '@/modules/auth/guards/jwt.util';

/**
 * Interface mở rộng từ Socket.IO Client
 * B<PERSON> sung thông tin người dùng đã xác thực
 */
export interface SocketClient extends Socket {
  /**
   * ID của client
   */
  id: string;
  /**
   * Thông tin người dùng đã xác thực từ JWT
   */
  user?: JwtPayload;

  /**
   * Danh sách phòng mà client đã tham gia
   * Override để đảm bảo tương thích với Socket interface
   */
  rooms: Set<string>;

  /**
   * Thời gian kết nối
   */
  connectedAt?: Date;

  /**
   * Thời gian hoạt động cuối cùng
   */
  lastActiveAt?: Date;

  /**
   * Trạng thái của client
   */
  status?: 'online' | 'away' | 'busy' | 'offline';

  /**
   * Thông tin thiết bị
   */
  device?: {
    type?: string;
    browser?: string;
    os?: string;
    version?: string;
  };
}
