import { Injectable } from '@nestjs/common';
import { VideoGenerationService } from '../interfaces';
import { RunwayMlService } from './runway-ml.service';
import { SynthesiaService } from './synthesia.service';
import { PikaLabsService } from './pika-labs.service';

/**
 * Enum for video generation provider types
 */
export enum VideoGenerationProviderType {
  RUNWAY_ML = 'RUNWAY_ML',
  SYNTHESIA = 'SYNTHESIA',
  PIKA_LABS = 'PIKA_LABS',
}

/**
 * Factory service for creating video generation services
 */
@Injectable()
export class VideoGenerationFactoryService {
  constructor(
    private readonly runwayMlService: RunwayMlService,
    private readonly synthesiaService: SynthesiaService,
    private readonly pikaLabsService: PikaLabsService,
  ) {}

  /**
   * Get a video generation service by provider type
   * @param providerType Provider type
   * @returns Video generation service
   * @throws Error if provider type is not supported
   */
  getProvider(
    providerType: VideoGenerationProviderType,
  ): VideoGenerationService {
    switch (providerType) {
      case VideoGenerationProviderType.RUNWAY_ML:
        return this.runwayMlService;
      case VideoGenerationProviderType.SYNTHESIA:
        return this.synthesiaService;
      case VideoGenerationProviderType.PIKA_LABS:
        return this.pikaLabsService;
      default:
        throw new Error(
          `Unsupported video generation provider type: ${providerType}`,
        );
    }
  }

  /**
   * Get all available video generation services
   * @returns Array of video generation services
   */
  getAllProviders(): VideoGenerationService[] {
    return [this.runwayMlService, this.synthesiaService, this.pikaLabsService];
  }

  /**
   * Get all available provider types
   * @returns Array of provider types
   */
  getAllProviderTypes(): VideoGenerationProviderType[] {
    return [
      VideoGenerationProviderType.RUNWAY_ML,
      VideoGenerationProviderType.SYNTHESIA,
      VideoGenerationProviderType.PIKA_LABS,
    ];
  }
}
