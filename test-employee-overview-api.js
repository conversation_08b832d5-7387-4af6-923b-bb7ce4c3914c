// Test script để kiểm tra API Employee Overview
const http = require('http');

// Cấu hình test
const BASE_URL = 'http://localhost:3001';

// Function để gọi API
function makeRequest(path, method = 'GET', data = null, token = null) {
  return new Promise((resolve, reject) => {
    const url = new URL(BASE_URL + path);
    const options = {
      hostname: url.hostname,
      port: url.port,
      path: url.pathname + url.search,
      method: method,
      headers: {
        'Content-Type': 'application/json',
      }
    };

    if (token) {
      options.headers['Authorization'] = `Bearer ${token}`;
    }

    if (data) {
      const postData = JSON.stringify(data);
      options.headers['Content-Length'] = Buffer.byteLength(postData);
    }

    const req = http.request(options, (res) => {
      let responseData = '';
      
      res.on('data', (chunk) => {
        responseData += chunk;
      });
      
      res.on('end', () => {
        try {
          const parsedData = JSON.parse(responseData);
          resolve({
            status: res.statusCode,
            headers: res.headers,
            data: parsedData
          });
        } catch (e) {
          resolve({
            status: res.statusCode,
            headers: res.headers,
            data: responseData
          });
        }
      });
    });

    req.on('error', (err) => {
      reject(err);
    });

    if (data) {
      req.write(JSON.stringify(data));
    }
    
    req.end();
  });
}

// Test cases
async function runTests() {
  console.log('🚀 Bắt đầu test API Employee Overview...\n');

  let authToken = null;

  // Test 1: Login để lấy token
  console.log('🔐 Test 1: Login để lấy token');
  try {
    const loginData = {
      username: '<EMAIL>',  // Sử dụng email làm username
      password: 'Admin@123'       // Password từ seed data
    };
    
    const response = await makeRequest('/v1/auth/user/login', 'POST', loginData);
    console.log(`Status: ${response.status}`);
    
    if (response.status === 200 && response.data.data?.accessToken) {
      authToken = response.data.data.accessToken;
      console.log('✅ Login thành công! Token đã được lấy.');
      console.log(`Token preview: ${authToken.substring(0, 50)}...`);
    } else {
      console.log('❌ Login failed:', response.data);
      console.log('💡 Hãy kiểm tra username/password hoặc tạo user test trước');
      return;
    }
  } catch (error) {
    console.log('❌ Login error:', error.message);
    return;
  }
  console.log('');

  if (!authToken) {
    console.log('❌ Không có token, dừng test');
    return;
  }

  // Test 2: GET Employee Overview
  console.log('📊 Test 2: GET /v1/api/hrm/employees/overview');
  try {
    const response = await makeRequest('/v1/api/hrm/employees/overview', 'GET', null, authToken);
    console.log(`Status: ${response.status}`);
    
    if (response.status === 200) {
      console.log('✅ Success! Employee Overview Data:');
      const overview = response.data.data;
      
      console.log('📈 Thống kê tổng quan nhân viên:');
      console.log(`   📋 Tổng số nhân viên: ${overview.totalEmployees}`);
      console.log(`   👥 Tổng số tài khoản: ${overview.totalUserAccounts}`);
      console.log(`   ✅ Nhân viên hoạt động: ${overview.activeEmployees}`);
      console.log(`   ❌ Nhân viên không hoạt động: ${overview.inactiveEmployees}`);
      console.log(`   🆕 Nhân viên mới tháng này: ${overview.newEmployeesThisMonth}`);
      console.log(`   🔄 Nhân viên thử việc: ${overview.probationEmployees}`);
      console.log(`   📊 Tỷ lệ nhân viên hoạt động: ${overview.activeEmployeePercentage}%`);
      console.log(`   📊 Tỷ lệ có tài khoản: ${overview.accountCoveragePercentage}%`);
      
      // Validation logic
      console.log('\n🔍 Validation:');
      const totalCalculated = overview.activeEmployees + overview.inactiveEmployees;
      if (totalCalculated <= overview.totalEmployees) {
        console.log('✅ Logic tính toán active + inactive <= total: OK');
      } else {
        console.log('❌ Logic tính toán active + inactive > total: ERROR');
      }
      
      if (overview.totalUserAccounts <= overview.totalEmployees) {
        console.log('✅ Logic tài khoản <= tổng nhân viên: OK');
      } else {
        console.log('❌ Logic tài khoản > tổng nhân viên: ERROR');
      }
      
      if (overview.probationEmployees <= overview.totalEmployees) {
        console.log('✅ Logic nhân viên thử việc <= tổng nhân viên: OK');
      } else {
        console.log('❌ Logic nhân viên thử việc > tổng nhân viên: ERROR');
      }
      
    } else {
      console.log('❌ Failed:', response.data);
    }
  } catch (error) {
    console.log('❌ Error:', error.message);
  }
  console.log('');

  // Test 3: Tạo một nhân viên test để kiểm tra thống kê thay đổi
  console.log('➕ Test 3: Tạo nhân viên test để kiểm tra thống kê');
  try {
    const employeeData = {
      employeeName: 'Test Employee Overview',
      status: 'probation', // Tạo nhân viên thử việc
      hireDate: new Date().toISOString().split('T')[0] // Ngày hôm nay
    };
    
    const response = await makeRequest('/v1/api/hrm/employees', 'POST', employeeData, authToken);
    console.log(`Status: ${response.status}`);
    
    if (response.status === 201) {
      console.log('✅ Employee created successfully!');
      console.log(`Created: ${response.data.data.employeeCode} - ${response.data.data.employeeName}`);
      
      // Test lại overview sau khi tạo nhân viên
      console.log('\n📊 Test 3.1: GET Overview sau khi tạo nhân viên');
      const overviewResponse = await makeRequest('/v1/api/hrm/employees/overview', 'GET', null, authToken);
      
      if (overviewResponse.status === 200) {
        const newOverview = overviewResponse.data.data;
        console.log('✅ Updated Overview:');
        console.log(`   📋 Tổng số nhân viên: ${newOverview.totalEmployees}`);
        console.log(`   🔄 Nhân viên thử việc: ${newOverview.probationEmployees}`);
        console.log(`   🆕 Nhân viên mới tháng này: ${newOverview.newEmployeesThisMonth}`);
      }
    } else {
      console.log('❌ Failed to create employee:', response.data);
    }
  } catch (error) {
    console.log('❌ Error:', error.message);
  }

  console.log('\n🏁 Test hoàn thành!');
}

// Chạy tests
runTests().catch(console.error);
