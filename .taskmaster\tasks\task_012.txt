# Task ID: 12
# Title: Update HRM Module - Services and Controllers
# Status: done
# Dependencies: 10, 11
# Priority: high
# Description: Update HRM services and controllers for tenantId handling
# Details:
Update service and controller layers:
- EmployeeService: Add tenantId parameter to all methods
- DepartmentService: Update to pass tenantId to repository
- PositionService: Add tenantId handling
- HRM Controllers: Add @CurrentTenant() to all endpoints
- Update organizational reporting with tenantId filtering

# Test Strategy:
Integration and API tests for HRM module
