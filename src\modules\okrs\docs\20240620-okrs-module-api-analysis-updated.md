# Phân tích Module OKRs (<PERSON>ậ<PERSON> nhật)

## 1. <PERSON><PERSON>ng quan

Module OKRs (Objectives and Key Results) là một phần quan trọng của hệ thống, cho phép quản lý mục tiêu và kết quả chính của tổ chức, phòng ban và cá nhân. Module này cung cấp các API để tạo, cậ<PERSON> nh<PERSON>, theo dõi và báo cáo về OKRs.

## 2. Cấu trúc dữ liệu

### 2.1. Các Entity

Module OKRs bao gồm các entity chính sau:

#### 2.1.1. Objective (Mục tiêu)

Entity `Objective` đại diện cho các mục tiêu trong hệ thống OKR:

```typescript
@Entity('objectives')
@Check(`"start_date" <= "end_date"`)
export class Objective {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ type: 'varchar', length: 255, nullable: false })
  title: string;

  @Column({ type: 'text', nullable: true })
  description: string | null;

  @Column({ name: 'owner_id', type: 'integer', nullable: false })
  ownerId: number;

  @Column({ name: 'department_id', type: 'integer', nullable: true })
  departmentId: number | null;

  @Column({ name: 'parent_id', type: 'integer', nullable: true })
  parentId: number | null;

  @Column({ name: 'cycle_id', type: 'integer', nullable: false })
  cycleId: number;

  @Column({ name: 'type', type: 'enum', enum: ObjectiveType, nullable: false })
  type: ObjectiveType;

  @Column({ type: 'integer', default: 0, nullable: true })
  progress: number | null;

  @Column({ type: 'varchar', length: 50, nullable: true })
  status: string | null;

  @Column({ name: 'start_date', type: 'date', nullable: false })
  startDate: Date;

  @Column({ name: 'end_date', type: 'date', nullable: false })
  endDate: Date;

  @Column({ name: 'created_by', type: 'integer', nullable: false })
  createdBy: number;

  @Column({ name: 'created_at', type: 'bigint', nullable: true })
  createdAt: number | null;

  @Column({ name: 'updated_at', type: 'bigint', nullable: true })
  updatedAt: number | null;

  @Column({ name: 'tenant_id', type: 'integer', nullable: true })
  tenantId: number | null;
}
```

#### 2.1.2. KeyResult (Kết quả chính)

Entity `KeyResult` đại diện cho các kết quả chính đo lường sự thành công của mục tiêu:

```typescript
@Entity('key_results')
@Check(`"target_value" <> "start_value"`)
export class KeyResult {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: 'objective_id', type: 'integer', nullable: true })
  objectiveId: number | null;

  @Column({ type: 'varchar', length: 255, nullable: false })
  title: string;

  @Column({ type: 'text', nullable: true })
  description: string | null;

  @Column({ name: 'target_value', type: 'numeric', precision: 18, scale: 2, nullable: false })
  targetValue: number;

  @Column({ name: 'current_value', type: 'numeric', precision: 18, scale: 2, default: 0, nullable: true })
  currentValue: number | null;

  @Column({ name: 'start_value', type: 'numeric', precision: 18, scale: 2, default: 0, nullable: true })
  startValue: number | null;

  @Column({ type: 'varchar', length: 50, nullable: true })
  unit: string | null;

  @Column({ type: 'varchar', length: 50, default: 'number', nullable: true })
  format: string | null;

  @Column({ type: 'integer', default: 0, nullable: true })
  progress: number | null;

  @Column({ type: 'enum', enum: KeyResultStatus, default: KeyResultStatus.ACTIVE, nullable: true })
  status: KeyResultStatus | null;

  @Column({ name: 'measurement_method', type: 'varchar', length: 100, nullable: true })
  measurementMethod: string | null;

  @Column({ type: 'integer', default: 100, nullable: true })
  weight: number | null;

  @Column({ name: 'check_in_frequency', type: 'enum', enum: CheckInFrequency, default: CheckInFrequency.WEEKLY, nullable: true })
  checkInFrequency: CheckInFrequency | null;

  @Column({ name: 'created_at', type: 'bigint', nullable: true })
  createdAt: number | null;

  @Column({ name: 'updated_at', type: 'bigint', nullable: true })
  updatedAt: number | null;

  @Column({ name: 'tenant_id', type: 'integer', nullable: true })
  tenantId: number | null;
}
```

#### 2.1.3. KeyResultUpdate (Cập nhật kết quả chính)

Entity `KeyResultUpdate` đại diện cho lịch sử cập nhật giá trị của kết quả chính:

```typescript
@Entity('key_result_updates')
export class KeyResultUpdate {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: 'key_result_id', type: 'integer', nullable: true })
  keyResultId: number | null;

  @Column({ name: 'previous_value', type: 'numeric', precision: 18, scale: 2, nullable: false })
  previousValue: number;

  @Column({ name: 'new_value', type: 'numeric', precision: 18, scale: 2, nullable: false })
  newValue: number;

  @Column({ name: 'update_by', type: 'integer', nullable: false })
  updateBy: number;

  @Column({ name: 'confidence_level', type: 'integer', nullable: true })
  confidenceLevel: number | null;

  @Column({ type: 'text', nullable: true })
  notes: string | null;

  @Column({ name: 'check_in_type', type: 'varchar', length: 50, default: 'manual', nullable: true })
  checkInType: string | null;

  @Column({ name: 'updated_date', type: 'bigint', nullable: true })
  updatedDate: number | null;

  @Column({ name: 'tenant_id', type: 'integer', nullable: true })
  tenantId: number | null;
}
```

#### 2.1.4. KeyResultSupport (Hỗ trợ kết quả chính)

Entity `KeyResultSupport` đại diện cho mối quan hệ nhiều-nhiều giữa các kết quả chính, cho biết kết quả chính nào hỗ trợ kết quả chính khác:

```typescript
@Entity({ name: 'key_result_supports' })
export class KeyResultSupport {
  @PrimaryColumn({ name: 'parent_id', type: 'integer', nullable: false })
  parentId: number;

  @PrimaryColumn({ name: 'child_id', type: 'integer', nullable: false })
  childId: number;
}
```

#### 2.1.5. OkrCycle (Chu kỳ OKR)

Entity `OkrCycle` đại diện cho các chu kỳ OKR (quý, năm):

```typescript
@Entity('okr_cycles')
export class OkrCycle {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ type: 'varchar', length: 255, nullable: false })
  name: string;

  @Column({ name: 'start_date', type: 'date', nullable: false })
  startDate: Date;

  @Column({ name: 'end_date', type: 'date', nullable: false })
  endDate: Date;

  @Column({ type: 'enum', enum: OkrCycleStatus, nullable: true })
  status: OkrCycleStatus | null;

  @Column({ type: 'text', nullable: true })
  description: string | null;

  @Column({ name: 'created_by', type: 'integer', nullable: true })
  createdBy: number | null;

  @Column({ name: 'created_at', type: 'bigint', nullable: true })
  createdAt: number | null;

  @Column({ name: 'tenant_id', type: 'integer', nullable: true })
  tenantId: number | null;
}
```

#### 2.1.6. OkrType (Loại OKR)

Entity `OkrType` đại diện cho các loại OKR (công ty, phòng ban, cá nhân):

```typescript
@Entity('okr_types')
export class OkrType {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ type: 'varchar', length: 100, nullable: false })
  name: string;

  @Column({ type: 'text', nullable: true })
  description: string | null;

  @Column({ type: 'integer', nullable: false })
  level: number;

  @Column({ name: 'tenant_id', type: 'integer', nullable: true })
  tenantId: number | null;
}
```

### 2.2. Các Enum

Module OKRs sử dụng các enum sau:

#### 2.2.1. ObjectiveType

```typescript
export enum ObjectiveType {
  COMPANY = 'COMPANY',
  DEPARTMENT = 'DEPARTMENT',
  INDIVIDUAL = 'INDIVIDUAL',
}
```

#### 2.2.2. OkrCycleStatus

```typescript
export enum OkrCycleStatus {
  ACTIVE = 'ACTIVE',
  CLOSED = 'CLOSED',
  PLANNING = 'PLANNING',
}
```

#### 2.2.3. KeyResultStatus

```typescript
export enum KeyResultStatus {
  ACTIVE = 'ACTIVE',    // Đang hoạt động
  COMPLETED = 'COMPLETED', // Đã hoàn thành
  PAUSED = 'PAUSED',    // Tạm dừng
  CANCELED = 'CANCELED', // Đã hủy
}
```

#### 2.2.4. CheckInFrequency

```typescript
export enum CheckInFrequency {
  DAILY = 'DAILY',
  WEEKLY = 'WEEKLY',
  MONTHLY = 'MONTHLY',
}
```

## 3. Mối quan hệ giữa các Entity

### 3.1. Quan hệ chính

1. **Objective - KeyResult**: Một Objective có nhiều KeyResult. Mối quan hệ này được thể hiện qua trường `objectiveId` trong entity KeyResult.

2. **Objective - Objective**: Một Objective có thể có nhiều Objective con. Mối quan hệ này được thể hiện qua trường `parentId` trong entity Objective.

3. **OkrCycle - Objective**: Một OkrCycle có nhiều Objective. Mối quan hệ này được thể hiện qua trường `cycleId` trong entity Objective.

4. **KeyResult - KeyResultUpdate**: Một KeyResult có nhiều KeyResultUpdate. Mối quan hệ này được thể hiện qua trường `keyResultId` trong entity KeyResultUpdate.

5. **KeyResult - KeyResultSupport**: Một KeyResult có thể hỗ trợ nhiều KeyResult khác và cũng có thể được hỗ trợ bởi nhiều KeyResult khác. Đây là mối quan hệ nhiều-nhiều được thể hiện qua entity KeyResultSupport.

### 3.2. Quan hệ với các Entity bên ngoài

1. **User - Objective**: Một User có thể sở hữu nhiều Objective. Mối quan hệ này được thể hiện qua trường `ownerId` trong entity Objective.

2. **Department - Objective**: Một Department có thể có nhiều Objective. Mối quan hệ này được thể hiện qua trường `departmentId` trong entity Objective.

3. **User - KeyResultUpdate**: Một User có thể tạo nhiều KeyResultUpdate. Mối quan hệ này được thể hiện qua trường `updateBy` trong entity KeyResultUpdate.

4. **Tenant - Tất cả các Entity**: Tất cả các entity đều có trường `tenantId` để phân biệt dữ liệu giữa các tenant khác nhau trong hệ thống multi-tenant.

## 4. Đề xuất API

Dựa trên cấu trúc dữ liệu, module OKRs nên cung cấp các API sau:

### 4.1. API Quản lý Chu kỳ OKR

#### 4.1.1. Tạo chu kỳ OKR mới
- **Endpoint**: `POST /api/okrs/cycles`
- **Quyền**: Admin, Manager
- **Request Body**: Thông tin chu kỳ (tên, ngày bắt đầu, ngày kết thúc, mô tả)
- **Response**: Thông tin chu kỳ đã tạo

#### 4.1.2. Lấy danh sách chu kỳ OKR
- **Endpoint**: `GET /api/okrs/cycles`
- **Quyền**: Tất cả người dùng
- **Query Params**: Phân trang, sắp xếp, lọc theo trạng thái
- **Response**: Danh sách chu kỳ OKR

#### 4.1.3. Lấy chi tiết chu kỳ OKR
- **Endpoint**: `GET /api/okrs/cycles/:id`
- **Quyền**: Tất cả người dùng
- **Response**: Chi tiết chu kỳ OKR

#### 4.1.4. Cập nhật chu kỳ OKR
- **Endpoint**: `PUT /api/okrs/cycles/:id`
- **Quyền**: Admin, Manager
- **Request Body**: Thông tin cập nhật
- **Response**: Thông tin chu kỳ đã cập nhật

#### 4.1.5. Xóa chu kỳ OKR
- **Endpoint**: `DELETE /api/okrs/cycles/:id`
- **Quyền**: Admin
- **Response**: Thông báo xóa thành công

### 4.2. API Quản lý Mục tiêu (Objectives)

#### 4.2.1. Tạo mục tiêu mới
- **Endpoint**: `POST /api/okrs/objectives`
- **Quyền**: Tùy theo loại mục tiêu (COMPANY, DEPARTMENT, INDIVIDUAL)
- **Request Body**: Thông tin mục tiêu
- **Response**: Thông tin mục tiêu đã tạo

#### 4.2.2. Lấy danh sách mục tiêu
- **Endpoint**: `GET /api/okrs/objectives`
- **Quyền**: Tất cả người dùng
- **Query Params**: Phân trang, sắp xếp, lọc theo chu kỳ, loại, phòng ban, người sở hữu
- **Response**: Danh sách mục tiêu

#### 4.2.3. Lấy chi tiết mục tiêu
- **Endpoint**: `GET /api/okrs/objectives/:id`
- **Quyền**: Tất cả người dùng
- **Response**: Chi tiết mục tiêu và danh sách kết quả chính

#### 4.2.4. Cập nhật mục tiêu
- **Endpoint**: `PUT /api/okrs/objectives/:id`
- **Quyền**: Người sở hữu, Admin, Manager
- **Request Body**: Thông tin cập nhật
- **Response**: Thông tin mục tiêu đã cập nhật

#### 4.2.5. Xóa mục tiêu
- **Endpoint**: `DELETE /api/okrs/objectives/:id`
- **Quyền**: Người sở hữu, Admin, Manager
- **Response**: Thông báo xóa thành công

### 4.3. API Quản lý Kết quả chính (Key Results)

#### 4.3.1. Tạo kết quả chính mới
- **Endpoint**: `POST /api/okrs/key-results`
- **Quyền**: Người sở hữu mục tiêu, Admin, Manager
- **Request Body**: Thông tin kết quả chính
- **Response**: Thông tin kết quả chính đã tạo

#### 4.3.2. Lấy danh sách kết quả chính theo mục tiêu
- **Endpoint**: `GET /api/okrs/objectives/:objectiveId/key-results`
- **Quyền**: Tất cả người dùng
- **Response**: Danh sách kết quả chính của mục tiêu

#### 4.3.3. Lấy chi tiết kết quả chính
- **Endpoint**: `GET /api/okrs/key-results/:id`
- **Quyền**: Tất cả người dùng
- **Response**: Chi tiết kết quả chính và lịch sử cập nhật

#### 4.3.4. Cập nhật kết quả chính
- **Endpoint**: `PUT /api/okrs/key-results/:id`
- **Quyền**: Người sở hữu mục tiêu, Admin, Manager
- **Request Body**: Thông tin cập nhật
- **Response**: Thông tin kết quả chính đã cập nhật

#### 4.3.5. Xóa kết quả chính
- **Endpoint**: `DELETE /api/okrs/key-results/:id`
- **Quyền**: Người sở hữu mục tiêu, Admin, Manager
- **Response**: Thông báo xóa thành công

### 4.4. API Cập nhật giá trị kết quả chính

#### 4.4.1. Tạo bản ghi cập nhật mới
- **Endpoint**: `POST /api/okrs/key-results/:id/updates`
- **Quyền**: Người sở hữu mục tiêu, Admin, Manager
- **Request Body**: Giá trị mới, mức độ tự tin, ghi chú
- **Response**: Thông tin cập nhật đã tạo

#### 4.4.2. Lấy lịch sử cập nhật của kết quả chính
- **Endpoint**: `GET /api/okrs/key-results/:id/updates`
- **Quyền**: Tất cả người dùng
- **Query Params**: Phân trang, sắp xếp theo thời gian
- **Response**: Danh sách cập nhật

### 4.5. API Quản lý Hỗ trợ Kết quả chính

#### 4.5.1. Thêm mối quan hệ hỗ trợ
- **Endpoint**: `POST /api/okrs/key-results/:id/supports`
- **Quyền**: Người sở hữu mục tiêu, Admin, Manager
- **Request Body**: Danh sách ID của các kết quả chính hỗ trợ
- **Response**: Thông báo thành công

#### 4.5.2. Lấy danh sách kết quả chính hỗ trợ
- **Endpoint**: `GET /api/okrs/key-results/:id/supports`
- **Quyền**: Tất cả người dùng
- **Response**: Danh sách kết quả chính hỗ trợ

#### 4.5.3. Xóa mối quan hệ hỗ trợ
- **Endpoint**: `DELETE /api/okrs/key-results/:id/supports/:supportId`
- **Quyền**: Người sở hữu mục tiêu, Admin, Manager
- **Response**: Thông báo xóa thành công

## 5. Đề xuất DTO

### 5.1. DTO cho Chu kỳ OKR

#### 5.1.1. CreateOkrCycleDto
```typescript
export class CreateOkrCycleDto {
  @ApiProperty({
    description: 'Tên chu kỳ OKR',
    example: 'Q1-2025',
  })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty({
    description: 'Ngày bắt đầu chu kỳ',
    example: '2025-01-01',
  })
  @IsDateString()
  startDate: string;

  @ApiProperty({
    description: 'Ngày kết thúc chu kỳ',
    example: '2025-03-31',
  })
  @IsDateString()
  endDate: string;

  @ApiProperty({
    description: 'Trạng thái chu kỳ',
    enum: OkrCycleStatus,
    example: OkrCycleStatus.PLANNING,
    required: false,
  })
  @IsEnum(OkrCycleStatus)
  @IsOptional()
  status?: OkrCycleStatus;

  @ApiProperty({
    description: 'Mô tả chu kỳ',
    example: 'Chu kỳ Q1 năm 2025',
    required: false,
  })
  @IsString()
  @IsOptional()
  description?: string;
}
```

#### 5.1.2. UpdateOkrCycleDto
```typescript
export class UpdateOkrCycleDto {
  @ApiProperty({
    description: 'Tên chu kỳ OKR',
    example: 'Q1-2025',
    required: false,
  })
  @IsString()
  @IsOptional()
  name?: string;

  @ApiProperty({
    description: 'Ngày bắt đầu chu kỳ',
    example: '2025-01-01',
    required: false,
  })
  @IsDateString()
  @IsOptional()
  startDate?: string;

  @ApiProperty({
    description: 'Ngày kết thúc chu kỳ',
    example: '2025-03-31',
    required: false,
  })
  @IsDateString()
  @IsOptional()
  endDate?: string;

  @ApiProperty({
    description: 'Trạng thái chu kỳ',
    enum: OkrCycleStatus,
    example: OkrCycleStatus.ACTIVE,
    required: false,
  })
  @IsEnum(OkrCycleStatus)
  @IsOptional()
  status?: OkrCycleStatus;

  @ApiProperty({
    description: 'Mô tả chu kỳ',
    example: 'Chu kỳ Q1 năm 2025 đã cập nhật',
    required: false,
  })
  @IsString()
  @IsOptional()
  description?: string;
}
```

### 5.2. DTO cho Mục tiêu (Objectives)

#### 5.2.1. CreateObjectiveDto
```typescript
export class CreateObjectiveDto {
  @ApiProperty({
    description: 'Tiêu đề mục tiêu',
    example: 'Tăng doanh thu 20%',
  })
  @IsString()
  @IsNotEmpty()
  title: string;

  @ApiProperty({
    description: 'Mô tả chi tiết mục tiêu',
    example: 'Tăng doanh thu 20% so với quý trước thông qua các chiến dịch marketing mới',
    required: false,
  })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiProperty({
    description: 'ID của người chịu trách nhiệm',
    example: 1,
  })
  @IsNumber()
  ownerId: number;

  @ApiProperty({
    description: 'ID của phòng ban (nếu có)',
    example: 2,
    required: false,
  })
  @IsNumber()
  @IsOptional()
  departmentId?: number;

  @ApiProperty({
    description: 'ID của mục tiêu cha (nếu là mục tiêu con)',
    example: 3,
    required: false,
  })
  @IsNumber()
  @IsOptional()
  parentId?: number;

  @ApiProperty({
    description: 'ID của chu kỳ OKR',
    example: 1,
  })
  @IsNumber()
  cycleId: number;

  @ApiProperty({
    description: 'Loại mục tiêu',
    enum: ObjectiveType,
    example: ObjectiveType.COMPANY,
  })
  @IsEnum(ObjectiveType)
  type: ObjectiveType;

  @ApiProperty({
    description: 'Ngày bắt đầu mục tiêu',
    example: '2025-01-01',
  })
  @IsDateString()
  startDate: string;

  @ApiProperty({
    description: 'Ngày kết thúc mục tiêu',
    example: '2025-03-31',
  })
  @IsDateString()
  endDate: string;
}
```

### 5.3. DTO cho Kết quả chính (Key Results)

#### 5.3.1. CreateKeyResultDto
```typescript
export class CreateKeyResultDto {
  @ApiProperty({
    description: 'ID của mục tiêu',
    example: 1,
  })
  @IsNumber()
  objectiveId: number;

  @ApiProperty({
    description: 'Tiêu đề kết quả chính',
    example: 'Tăng số lượng khách hàng mới lên 1000',
  })
  @IsString()
  @IsNotEmpty()
  title: string;

  @ApiProperty({
    description: 'Mô tả chi tiết kết quả chính',
    example: 'Tăng số lượng khách hàng mới thông qua các chiến dịch marketing trên mạng xã hội',
    required: false,
  })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiProperty({
    description: 'Giá trị mục tiêu',
    example: 1000,
  })
  @IsNumber()
  targetValue: number;

  @ApiProperty({
    description: 'Giá trị ban đầu',
    example: 500,
    required: false,
  })
  @IsNumber()
  @IsOptional()
  startValue?: number;

  @ApiProperty({
    description: 'Đơn vị đo lường',
    example: 'khách hàng',
    required: false,
  })
  @IsString()
  @IsOptional()
  unit?: string;

  @ApiProperty({
    description: 'Định dạng hiển thị',
    example: 'number',
    required: false,
  })
  @IsString()
  @IsOptional()
  format?: string;

  @ApiProperty({
    description: 'Phương pháp đo lường',
    example: 'Số lượng đăng ký mới',
    required: false,
  })
  @IsString()
  @IsOptional()
  measurementMethod?: string;

  @ApiProperty({
    description: 'Trọng số trong mục tiêu (0-100)',
    example: 30,
    required: false,
  })
  @IsNumber()
  @Min(0)
  @Max(100)
  @IsOptional()
  weight?: number;

  @ApiProperty({
    description: 'Tần suất cập nhật',
    enum: CheckInFrequency,
    example: CheckInFrequency.WEEKLY,
    required: false,
  })
  @IsEnum(CheckInFrequency)
  @IsOptional()
  checkInFrequency?: CheckInFrequency;
}
```

### 5.4. DTO cho Cập nhật Kết quả chính

#### 5.4.1. CreateKeyResultUpdateDto
```typescript
export class CreateKeyResultUpdateDto {
  @ApiProperty({
    description: 'Giá trị mới',
    example: 800,
  })
  @IsNumber()
  newValue: number;

  @ApiProperty({
    description: 'Mức độ tự tin (1-5)',
    example: 4,
    required: false,
  })
  @IsNumber()
  @Min(1)
  @Max(5)
  @IsOptional()
  confidenceLevel?: number;

  @ApiProperty({
    description: 'Ghi chú cập nhật',
    example: 'Đã hoàn thành chiến dịch marketing trên Facebook, tăng thêm 300 khách hàng mới',
    required: false,
  })
  @IsString()
  @IsOptional()
  notes?: string;

  @ApiProperty({
    description: 'Loại check-in',
    example: 'manual',
    required: false,
  })
  @IsString()
  @IsOptional()
  checkInType?: string;
}
```

## 6. Đề xuất Repositories

### 6.1. OkrCycleRepository
```typescript
@Injectable()
export class OkrCycleRepository {
  private readonly logger = new Logger(OkrCycleRepository.name);

  constructor(
    @InjectRepository(OkrCycle)
    private readonly repository: Repository<OkrCycle>,
  ) {}

  async findAll(tenantId: number): Promise<OkrCycle[]> {
    return this.repository.find({
      where: { tenantId },
      order: { startDate: 'DESC' },
    });
  }

  async findById(id: number, tenantId: number): Promise<OkrCycle | null> {
    return this.repository.findOne({
      where: { id, tenantId },
    });
  }

  async create(data: Partial<OkrCycle>): Promise<OkrCycle> {
    const cycle = this.repository.create(data);
    return this.repository.save(cycle);
  }

  async update(id: number, tenantId: number, data: Partial<OkrCycle>): Promise<OkrCycle | null> {
    await this.repository.update({ id, tenantId }, data);
    return this.findById(id, tenantId);
  }

  async delete(id: number, tenantId: number): Promise<boolean> {
    const result = await this.repository.delete({ id, tenantId });
    return result.affected > 0;
  }

  async findActive(tenantId: number): Promise<OkrCycle | null> {
    return this.repository.findOne({
      where: { tenantId, status: OkrCycleStatus.ACTIVE },
    });
  }
}
```

## 7. Kết luận

Module OKRs là một phần quan trọng của hệ thống, cho phép quản lý mục tiêu và kết quả chính của tổ chức, phòng ban và cá nhân. Dựa trên phân tích các entity và mối quan hệ giữa chúng, chúng tôi đã đề xuất một tập hợp các API và DTO để triển khai module này.

Các API được thiết kế để hỗ trợ đầy đủ các chức năng CRUD cho tất cả các entity chính, cũng như các chức năng đặc biệt như cập nhật giá trị kết quả chính và theo dõi tiến độ. Các DTO được thiết kế để đảm bảo tính nhất quán và dễ sử dụng cho cả frontend và backend.

Những điểm cần lưu ý khi triển khai module OKRs:

1. **Tính đa người dùng (Multi-tenant)**: Tất cả các entity đều có trường `tenantId` để hỗ trợ tính năng đa người dùng. Cần đảm bảo rằng mọi truy vấn đều có điều kiện lọc theo `tenantId`.

2. **Phân quyền**: Cần triển khai hệ thống phân quyền chi tiết để đảm bảo rằng người dùng chỉ có thể truy cập và chỉnh sửa dữ liệu mà họ được phép.

3. **Tính toán tiến độ**: Cần triển khai logic để tính toán tiến độ của các mục tiêu dựa trên tiến độ của các kết quả chính và trọng số của chúng.

4. **Quan hệ hỗ trợ**: Cần triển khai logic để quản lý mối quan hệ hỗ trợ giữa các kết quả chính, đảm bảo rằng không có vòng lặp trong mối quan hệ này.

5. **Xác thực dữ liệu**: Cần triển khai xác thực dữ liệu chi tiết để đảm bảo tính nhất quán của dữ liệu, ví dụ như đảm bảo rằng ngày bắt đầu không lớn hơn ngày kết thúc.

Để triển khai module này, cần thực hiện các bước sau:

1. Tạo các entity và enum đã được mô tả
2. Tạo các DTO cho các API
3. Tạo các repository để tương tác với database
4. Tạo các service để xử lý logic nghiệp vụ
5. Tạo các controller để xử lý các request từ client
6. Tạo các guard để kiểm soát quyền truy cập
7. Viết unit test và integration test

Với việc triển khai module OKRs, hệ thống sẽ có thể hỗ trợ đầy đủ quy trình quản lý mục tiêu và kết quả chính, giúp tổ chức theo dõi và đánh giá hiệu quả công việc một cách hiệu quả.