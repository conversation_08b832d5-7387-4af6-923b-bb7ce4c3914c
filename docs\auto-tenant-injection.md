# Hướng dẫn sử dụng chức năng tự động inject tenantId

Chức năng tự động inject tenantId được thiết kế để tự động thêm điều kiện tenantId vào các câu truy vấn đối với các bảng có trường tenantId. Điều này giúp đơn giản hóa code và tránh phải thêm điều kiện tenantId vào mỗi câu truy vấn.

## Cách hoạt động

1. **TenantEntitySubscriber**: Subscriber này sẽ tự động thêm điều kiện tenantId vào tất cả các câu truy vấn đối với các entity có trường tenantId.
2. **TenantContextMiddleware**: Middleware này sẽ lấy tenantId từ request và lưu vào AsyncLocalStorage để TenantEntitySubscriber c<PERSON> thể sử dụng.
3. **WithTenant Decorator**: Decorator này giúp thiết lập tenantId vào context trong các trường hợp đặc biệt (ví dụ: trong các service không có request).

## Cách sử dụng

### 1. Trong Controller

Khi sử dụng decorator `TenantSecurity`, tenantId sẽ tự động được thiết lập vào context và các câu truy vấn sẽ tự động thêm điều kiện tenantId:

```typescript
import { Controller, Get } from '@nestjs/common';
import { TenantSecurity } from '@/common';

@Controller('users')
@TenantSecurity() // Áp dụng bảo mật tenant và tự động inject tenantId
export class UserController {
  constructor(private readonly userService: UserService) {}

  @Get()
  findAll() {
    // Không cần truyền tenantId vào phương thức findAll
    // TenantEntitySubscriber sẽ tự động thêm điều kiện tenantId vào câu truy vấn
    return this.userService.findAll();
  }
}
```

### 2. Trong Repository

Không cần thêm điều kiện tenantId vào các câu truy vấn, TenantEntitySubscriber sẽ tự động thêm:

```typescript
import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { User } from '../entities/user.entity';

@Injectable()
export class UserRepository {
  constructor(
    @InjectRepository(User)
    private readonly repository: Repository<User>,
  ) {}

  async findAll(): Promise<User[]> {
    // Không cần thêm điều kiện tenantId
    // TenantEntitySubscriber sẽ tự động thêm điều kiện tenantId vào câu truy vấn
    return this.repository.find();
  }

  async findById(id: number): Promise<User | null> {
    // Không cần thêm điều kiện tenantId
    return this.repository.findOne({ where: { id } });
  }

  async create(data: Partial<User>): Promise<User> {
    // Không cần thêm tenantId vào data
    // TenantEntitySubscriber sẽ tự động thêm tenantId vào entity trước khi insert
    const user = this.repository.create(data);
    return this.repository.save(user);
  }

  async update(id: number, data: Partial<User>): Promise<User | null> {
    // Không cần thêm điều kiện tenantId
    await this.repository.update({ id }, data);
    return this.findById(id);
  }

  async delete(id: number): Promise<boolean> {
    // Không cần thêm điều kiện tenantId
    const result = await this.repository.delete({ id });
    return result.affected > 0;
  }
}
```

### 3. Trong Service không có request

Sử dụng decorator `WithTenant` để thiết lập tenantId vào context:

```typescript
import { Injectable } from '@nestjs/common';
import { WithTenant } from '@/common';

@Injectable()
export class BackgroundService {
  constructor(private readonly userRepository: UserRepository) {}

  // Sử dụng với tenantId cố định
  @WithTenant(1)
  async processDataForTenant1(): Promise<void> {
    // Các thao tác với database sẽ tự động thêm điều kiện tenantId = 1
    const users = await this.userRepository.findAll();
    // ...
  }

  // Sử dụng với tenantId từ tham số
  async processDataForTenant(tenantId: number): Promise<void> {
    await this.processDataForTenantInternal(tenantId);
  }

  @WithTenant((args) => args[0])
  private async processDataForTenantInternal(tenantId: number): Promise<void> {
    // Các thao tác với database sẽ tự động thêm điều kiện tenantId = args[0]
    const users = await this.userRepository.findAll();
    // ...
  }
}
```

### 4. Truy cập dữ liệu của tất cả các tenant (chỉ dành cho SYSTEM_ADMIN)

Trong một số trường hợp, bạn có thể muốn truy cập dữ liệu của tất cả các tenant (ví dụ: trong trang quản trị). Trong trường hợp này, bạn cần sử dụng QueryBuilder và tắt tính năng tự động inject tenantId:

```typescript
import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { User } from '../entities/user.entity';

@Injectable()
export class UserRepository {
  constructor(
    @InjectRepository(User)
    private readonly repository: Repository<User>,
  ) {}

  async findAllAcrossTenants(): Promise<User[]> {
    // Sử dụng QueryBuilder và tắt tính năng tự động inject tenantId
    return this.repository
      .createQueryBuilder('user')
      .where('1 = 1') // Điều kiện luôn đúng để tránh TenantEntitySubscriber thêm điều kiện tenantId
      .disableEscaping() // Tắt escaping để tránh lỗi với điều kiện '1 = 1'
      .getMany();
  }
}
```

## Lưu ý

1. Chức năng này chỉ hoạt động với các entity có trường tenantId.
2. Nếu bạn muốn truy cập dữ liệu của tất cả các tenant, bạn cần sử dụng QueryBuilder và tắt tính năng tự động inject tenantId.
3. Nếu bạn muốn truy cập dữ liệu của một tenant cụ thể khác với tenant hiện tại, bạn cần sử dụng decorator `WithTenant`.
4. TenantEntitySubscriber sẽ ngăn chặn việc thay đổi tenantId của entity trong quá trình cập nhật.
5. Chức năng này không thay thế hoàn toàn việc kiểm tra quyền truy cập, bạn vẫn cần kiểm tra quyền truy cập trong các controller và service.
6. Nếu bạn gặp vấn đề với chức năng này, bạn có thể quay lại cách truyền tenantId vào các phương thức repository.
