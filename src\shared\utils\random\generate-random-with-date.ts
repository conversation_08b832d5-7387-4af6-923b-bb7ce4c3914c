/**
 * <PERSON><PERSON><PERSON> một chuỗi ngẫu nhiên gồm 4 số kết hợp với ngày tháng năm hiện tại (YYMMDD)
 * @returns Chuỗi định dạng YYMMDDXXXX (XXXX là số ngẫu nhiên 4 chữ số)
 */
export function generateRandomWithDate(): number {
  const now = new Date();

  // Lấy 2 số cuối của năm
  const year = now.getFullYear() % 100; // 25

  // Lấy tháng và ngày, đảm bảo luôn 2 chữ số
  const month = (now.getMonth() + 1).toString().padStart(2, '0'); // '05'
  const day = now.getDate().toString().padStart(2, '0'); // '16'

  // Tạo số ngẫu nhiên 4 chữ số (0000 đến 9999), đảm bảo có 4 chữ số
  const random = Math.floor(Math.random() * 10000)
    .toString()
    .padStart(4, '0'); // '0123'

  // Ghép lại thành chuỗi: YYMMDDXXXX
  const resultStr = `${year.toString().padStart(2, '0')}${month}${day}${random}`;

  return Number(resultStr);
}
