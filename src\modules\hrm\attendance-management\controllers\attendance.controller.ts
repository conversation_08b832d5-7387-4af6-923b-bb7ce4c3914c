import {
  Controller,
  Get,
  Post,
  Patch,
  Delete,
  Body,
  Param,
  Query,
  ParseIntPipe,
  UseGuards,
  Req,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiQuery,
  ApiBearerAuth,
  ApiExtraModels,
} from '@nestjs/swagger';
import { AttendanceService } from '../services/attendance.service';
import { Attendance } from '../entities/attendance.entity';
import { CreateAttendanceDto } from '../dto/create-attendance.dto';
import { UpdateAttendanceDto } from '../dto/update-attendance.dto';
import { AttendanceQueryDto } from '../dto/attendance-query.dto';
import { AttendanceResponseDto } from '../dto/attendance-response.dto';
import { PaginatedResult } from '@/common/response/api-response-dto';
import { ApiResponseDto } from '@/common/response/api-response-dto';
import { JwtUserGuard } from '@/modules/auth/guards/jwt-user.guard';
import { CurrentUser } from '@/modules/auth/decorators/current-user.decorator';
import { JwtPayload } from '@/modules/auth/guards/jwt.util';
import { Request } from 'express';
import { SWAGGER_API_TAG } from '@/common/swagger';

/**
 * Controller for attendance management with tenant isolation
 */
@ApiTags(SWAGGER_API_TAG.HRM)
@ApiExtraModels(ApiResponseDto, AttendanceResponseDto)
@Controller('api/hrm/attendance')
@UseGuards(JwtUserGuard)
@ApiBearerAuth('JWT-auth')
export class AttendanceController {
  constructor(private readonly attendanceService: AttendanceService) {}

  /**
   * Get all attendance records with pagination and filtering
   */
  @Get()
  @ApiOperation({ summary: 'Get all attendance records' })
  @ApiResponse({
    status: 200,
    description: 'List of attendance records',
    schema: ApiResponseDto.getPaginatedSchema(AttendanceResponseDto),
  })
  async findAll(
    @Query() query: AttendanceQueryDto,
    @CurrentUser() user: JwtPayload,
  ): Promise<ApiResponseDto<PaginatedResult<Attendance>>> {
    const attendances = await this.attendanceService.findAll(
      Number(user.tenantId),
      query,
    );
    return ApiResponseDto.success(attendances);
  }

  /**
   * Get attendance record by ID
   */
  @Get(':id')
  @ApiOperation({ summary: 'Get attendance record by ID' })
  @ApiParam({ name: 'id', description: 'Attendance ID', type: 'number' })
  @ApiResponse({
    status: 200,
    description: 'Attendance record details',
    schema: ApiResponseDto.getSchema(AttendanceResponseDto),
  })
  async findOne(
    @Param('id', ParseIntPipe) id: number,
    @CurrentUser() user: JwtPayload,
  ): Promise<ApiResponseDto<Attendance>> {
    const attendance = await this.attendanceService.findById(
      Number(user.tenantId),
      id,
    );
    return ApiResponseDto.success(attendance);
  }

  /**
   * Get attendance records by employee ID
   */
  @Get('employee/:employeeId')
  @ApiOperation({ summary: 'Get attendance records by employee ID' })
  @ApiParam({ name: 'employeeId', description: 'Employee ID', type: 'number' })
  @ApiResponse({
    status: 200,
    description: 'List of attendance records for employee',
    schema: ApiResponseDto.getArraySchema(AttendanceResponseDto),
  })
  async findByEmployeeId(
    @Param('employeeId', ParseIntPipe) employeeId: number,
    @CurrentUser() user: JwtPayload,
  ): Promise<ApiResponseDto<Attendance[]>> {
    const attendances = await this.attendanceService.findByEmployeeId(
      Number(user.tenantId),
      employeeId,
    );
    return ApiResponseDto.success(attendances);
  }

  /**
   * Get attendance statistics for employee
   */
  @Get('employee/:employeeId/stats')
  @ApiOperation({ summary: 'Get attendance statistics for employee' })
  @ApiParam({ name: 'employeeId', description: 'Employee ID', type: 'number' })
  @ApiQuery({
    name: 'startDate',
    description: 'Start date (YYYY-MM-DD)',
    type: 'string',
  })
  @ApiQuery({
    name: 'endDate',
    description: 'End date (YYYY-MM-DD)',
    type: 'string',
  })
  @ApiResponse({
    status: 200,
    description: 'Attendance statistics for employee',
  })
  async getAttendanceStats(
    @Param('employeeId', ParseIntPipe) employeeId: number,
    @Query('startDate') startDate: string,
    @Query('endDate') endDate: string,
    @CurrentUser() user: JwtPayload,
  ): Promise<ApiResponseDto<any>> {
    const stats = await this.attendanceService.getAttendanceStats(
      Number(user.tenantId),
      employeeId,
      new Date(startDate),
      new Date(endDate),
    );
    return ApiResponseDto.success(stats);
  }

  /**
   * Create a new attendance record
   */
  @Post()
  @ApiOperation({ summary: 'Create a new attendance record' })
  @ApiResponse({
    status: 201,
    description: 'Attendance record created successfully',
    schema: ApiResponseDto.getSchema(AttendanceResponseDto),
  })
  async create(
    @Body() createAttendanceDto: CreateAttendanceDto,
    @CurrentUser() user: JwtPayload,
  ): Promise<ApiResponseDto<Attendance>> {
    const attendance = await this.attendanceService.create(
      Number(user.tenantId),
      createAttendanceDto,
      user.id,
    );
    return ApiResponseDto.created(attendance);
  }

  /**
   * Check-in employee
   */
  @Post('check-in')
  @ApiOperation({ summary: 'Check-in employee' })
  @ApiResponse({
    status: 201,
    description: 'Employee checked in successfully',
    schema: ApiResponseDto.getSchema(AttendanceResponseDto),
  })
  async checkIn(
    @Body()
    checkInData: { employeeId: number; location?: string; notes?: string },
    @CurrentUser() user: JwtPayload,
    @Req() request: Request,
  ): Promise<ApiResponseDto<Attendance>> {
    const clientIp =
      request.ip || request.connection.remoteAddress || 'unknown';

    const attendance = await this.attendanceService.checkIn(
      Number(user.tenantId),
      checkInData.employeeId,
      {
        location: checkInData.location,
        ip: clientIp,
        notes: checkInData.notes,
      },
      user.id,
    );
    return ApiResponseDto.created(
      attendance,
      'Employee checked in successfully',
    );
  }

  /**
   * Check-out employee
   */
  @Post('check-out')
  @ApiOperation({ summary: 'Check-out employee' })
  @ApiResponse({
    status: 200,
    description: 'Employee checked out successfully',
    schema: ApiResponseDto.getSchema(AttendanceResponseDto),
  })
  async checkOut(
    @Body()
    checkOutData: { employeeId: number; location?: string; notes?: string },
    @CurrentUser() user: JwtPayload,
    @Req() request: Request,
  ): Promise<ApiResponseDto<Attendance>> {
    const clientIp =
      request.ip || request.connection.remoteAddress || 'unknown';

    const attendance = await this.attendanceService.checkOut(
      Number(user.tenantId),
      checkOutData.employeeId,
      {
        location: checkOutData.location,
        ip: clientIp,
        notes: checkOutData.notes,
      },
      user.id,
    );
    return ApiResponseDto.success(
      attendance,
      'Employee checked out successfully',
    );
  }

  /**
   * Update attendance record
   */
  @Patch(':id')
  @ApiOperation({ summary: 'Update attendance record' })
  @ApiParam({ name: 'id', description: 'Attendance ID', type: 'number' })
  @ApiResponse({
    status: 200,
    description: 'Attendance record updated successfully',
    schema: ApiResponseDto.getSchema(AttendanceResponseDto),
  })
  async update(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateAttendanceDto: UpdateAttendanceDto,
    @CurrentUser() user: JwtPayload,
  ): Promise<ApiResponseDto<Attendance>> {
    const attendance = await this.attendanceService.update(
      Number(user.tenantId),
      id,
      updateAttendanceDto,
      user.id,
    );
    return ApiResponseDto.success(attendance);
  }

  /**
   * Delete attendance record
   */
  @Delete(':id')
  @ApiOperation({ summary: 'Delete attendance record' })
  @ApiParam({ name: 'id', description: 'Attendance ID', type: 'number' })
  @ApiResponse({
    status: 200,
    description: 'Attendance record deleted successfully',
    schema: ApiResponseDto.getSchema(Boolean),
  })
  async remove(
    @Param('id', ParseIntPipe) id: number,
    @CurrentUser() user: JwtPayload,
  ): Promise<ApiResponseDto<boolean>> {
    await this.attendanceService.delete(Number(user.tenantId), id);
    return ApiResponseDto.success(true);
  }
}
