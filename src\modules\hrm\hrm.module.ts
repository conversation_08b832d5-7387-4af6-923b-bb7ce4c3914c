import { Module, Global } from '@nestjs/common';
import { OrgUnitsModule } from './org-units/org-units.module';
import { EmployeesModule } from './employees/employees.module';
import { ContractsModule } from './contracts/contracts.module';
import { AttendanceModule } from './attendance-management/attendance.module';

/**
 * Module quản lý nhân sự
 */
@Global()
@Module({
  imports: [OrgUnitsModule, EmployeesModule, ContractsModule, AttendanceModule],
  controllers: [],
  providers: [],
  exports: [OrgUnitsModule, EmployeesModule, ContractsModule, AttendanceModule],
})
export class HrmModule {}
