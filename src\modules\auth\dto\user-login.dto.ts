import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsOptional, IsString, MinLength } from 'class-validator';

/**
 * DTO cho đăng nhập người dùng
 */
export class UserLoginDto {
  @ApiProperty({
    description: 'Username hoặc email của người dùng',
    example: 'johndoe',
  })
  @IsNotEmpty({ message: 'Username không được để trống' })
  @IsString({ message: 'Username phải là chuỗi' })
  username: string;

  @ApiProperty({
    description: 'Mật khẩu tài khoản',
    example: 'StrongPassword123!',
  })
  @IsNotEmpty({ message: 'Mật khẩu không được để trống' })
  @IsString({ message: 'Mật khẩu phải là chuỗi' })
  @MinLength(6, { message: 'Mật khẩu phải có ít nhất 6 ký tự' })
  password: string;

  @ApiProperty({
    description: 'Token reCAPTCHA',
    example: '03AGdBq24PBCbwiDRVu...',
  })
  @IsString({ message: 'Token reCAPTCHA phải là chuỗi' })
  @IsOptional({ message: 'Token reCAPTCHA không được để trống' })
  recaptchaToken: string;
}
