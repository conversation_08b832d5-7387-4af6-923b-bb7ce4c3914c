# Task ID: 15
# Title: Update Chat Module - Message and Conversation Repositories
# Status: done
# Dependencies: 1
# Priority: medium
# Description: Add tenantId filtering to Chat module repositories
# Details:
Update repository methods:
- MessageRepository: Add tenantId to all queries
- ConversationRepository: Add tenantId filtering
- Handle message history with tenantId
- Update chat search with tenantId filtering
- Ensure conversation participants are from same tenant

# Test Strategy:
Unit tests for Chat repositories with tenantId validation
