import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as crypto from 'crypto';

/**
 * Service cung cấp các phương thức mã hóa và giải mã dữ liệu
 * Sử dụng thuật toán AES-256-CBC để mã hóa/giải mã
 */
@Injectable()
export class EncryptionService {
  private readonly logger = new Logger(EncryptionService.name);
  private readonly encryptionKey: Buffer;
  private readonly algorithm = 'aes-256-cbc';
  private readonly ivLength = 16; // 16 bytes cho IV

  constructor(private readonly configService: ConfigService) {
    const secretKey = this.configService.get<string>('ENCRYPTION_SECRET_KEY');

    if (!secretKey) {
      this.logger.error(
        'ENCRYPTION_SECRET_KEY không được định nghĩa trong file .env',
      );
      throw new Error('ENCRYPTION_SECRET_KEY không được định nghĩa');
    }

    // Tạo key 32 bytes (256 bits) từ secret key bằng SHA-256
    this.encryptionKey = crypto
      .createHash('sha256')
      .update(String(secretKey))
      .digest();
  }

  /**
   * Mã hóa một chuỗi văn bản
   * @param text Chuỗi cần mã hóa
   * @returns Chuỗi đã mã hóa dạng base64
   */
  encrypt(text: string): string {
    try {
      // Tạo IV ngẫu nhiên
      const iv = crypto.randomBytes(this.ivLength);

      // Tạo cipher với key và iv
      const cipher = crypto.createCipheriv(
        this.algorithm,
        this.encryptionKey,
        iv,
      );

      // Mã hóa dữ liệu
      let encrypted = cipher.update(text, 'utf8', 'base64');
      encrypted += cipher.final('base64');

      // Kết hợp IV và dữ liệu đã mã hóa thành một chuỗi base64
      // IV được lưu cùng với dữ liệu mã hóa để sử dụng khi giải mã
      return Buffer.concat([iv, Buffer.from(encrypted, 'base64')]).toString(
        'base64',
      );
    } catch (error) {
      this.logger.error(`Lỗi khi mã hóa dữ liệu: ${error.message}`);
      throw new Error(`Không thể mã hóa dữ liệu: ${error.message}`);
    }
  }

  /**
   * Giải mã một chuỗi đã mã hóa
   * @param encryptedText Chuỗi đã mã hóa dạng base64
   * @returns Chuỗi văn bản gốc
   */
  decrypt(encryptedText: string): string {
    try {
      // Chuyển chuỗi base64 thành buffer
      const buffer = Buffer.from(encryptedText, 'base64');

      // Tách IV (16 bytes đầu tiên) và dữ liệu đã mã hóa
      const iv = buffer.subarray(0, this.ivLength);
      const encryptedData = buffer.subarray(this.ivLength).toString('base64');

      // Tạo decipher với key và iv
      const decipher = crypto.createDecipheriv(
        this.algorithm,
        this.encryptionKey,
        iv,
      );

      // Giải mã dữ liệu
      let decrypted = decipher.update(encryptedData, 'base64', 'utf8');
      decrypted += decipher.final('utf8');

      return decrypted;
    } catch (error) {
      this.logger.error(`Lỗi khi giải mã dữ liệu: ${error.message}`);
      throw new Error(`Không thể giải mã dữ liệu: ${error.message}`);
    }
  }

  /**
   * Mã hóa một đối tượng JSON
   * @param data Đối tượng cần mã hóa
   * @returns Chuỗi đã mã hóa dạng base64
   */
  encryptObject<T>(data: T): string {
    const jsonString = JSON.stringify(data);
    return this.encrypt(jsonString);
  }

  /**
   * Giải mã một chuỗi thành đối tượng JSON
   * @param encryptedText Chuỗi đã mã hóa dạng base64
   * @returns Đối tượng JSON gốc
   */
  decryptObject<T>(encryptedText: string): T {
    const jsonString = this.decrypt(encryptedText);
    return JSON.parse(jsonString) as T;
  }

  /**
   * Tạo hash một chiều cho mật khẩu sử dụng bcrypt
   * @param password Mật khẩu cần hash
   * @returns Chuỗi hash
   */
  hashPassword(password: string): string {
    // Sử dụng SHA-256 để hash mật khẩu
    // Lưu ý: Trong thực tế, nên sử dụng bcrypt hoặc argon2 thay vì SHA
    const salt = crypto.randomBytes(16).toString('hex');
    const hash = crypto
      .pbkdf2Sync(password, salt, 10000, 64, 'sha512')
      .toString('hex');

    // Lưu salt cùng với hash để sử dụng khi xác thực
    return `${salt}:${hash}`;
  }

  /**
   * Xác thực mật khẩu với chuỗi hash
   * @param password Mật khẩu cần xác thực
   * @param hashedPassword Chuỗi hash đã lưu
   * @returns true nếu mật khẩu khớp, false nếu không
   */
  verifyPassword(password: string, hashedPassword: string): boolean {
    try {
      const [salt, hash] = hashedPassword.split(':');
      const calculatedHash = crypto
        .pbkdf2Sync(password, salt, 10000, 64, 'sha512')
        .toString('hex');

      return hash === calculatedHash;
    } catch (error) {
      this.logger.error(`Lỗi khi xác thực mật khẩu: ${error.message}`);
      return false;
    }
  }

  /**
   * Tạo mã token ngẫu nhiên
   * @param length Độ dài của token (mặc định là 32)
   * @returns Chuỗi token ngẫu nhiên
   */
  generateRandomToken(length: number = 32): string {
    return crypto.randomBytes(length).toString('hex');
  }
}
