/**
 * Đ<PERSON><PERSON> nghĩa cấu trúc dữ liệu cho một vị trí cần chỉnh sửa trên PDF.
 */
export interface PdfPosition {
  pageIndex: number; // Chỉ số trang (bắt đầu từ 0)
  text?: string; // Văn bản cần chèn (tùy chọn nếu chèn chữ ký)
  xMm: number; // Tọa độ X (theo mm)
  yMm: number; // Tọa độ Y (theo mm)
  size?: number; // Kích thước chữ (mặc định: 12)
  signatureBase64?: string; // Chuỗi Base64 của chữ ký tay (nếu có)
  signatureWidthMm?: number; // Chiều rộng chữ ký tay (mm, mặc định 50)
  signatureHeightMm?: number; // Chiều cao chữ ký tay (mm, mặc định 20)
  fontWeight?: number; // Độ đậm phông chữ (mặc định 400)
  isCenter?: boolean; // Căn giữa text (mặc định false)
}

/**
 * <PERSON><PERSON><PERSON> nghĩa cấu trúc dữ liệu cho yêu cầu chỉnh sửa PDF gửi đến API.
 */
export interface PdfEditRequest {
  /**
   * Nội dung file PDF gốc dưới dạng chuỗi Base64.
   */
  pdfBytes: string;
  /**
   * Danh sách các vị trí và nội dung cần chỉnh sửa.
   */
  positions: PdfPosition[];
}
