/**
 * Base interface for all marketing AI services
 */
export interface BaseMarketingAiService {
  /**
   * Get the name of the service
   */
  readonly serviceName: string;

  /**
   * Test the connection to the service
   * @returns A promise that resolves to a boolean indicating if the connection was successful
   */
  testConnection(): Promise<boolean>;
}

/**
 * Common response interface for all marketing AI services
 */
export interface MarketingAiResponse<T> {
  /**
   * Whether the request was successful
   */
  success: boolean;

  /**
   * The result of the request
   */
  data?: T;

  /**
   * Error message if the request failed
   */
  error?: string;

  /**
   * Raw response from the service
   */
  rawResponse?: any;
}

/**
 * Common options interface for all marketing AI services
 */
export interface MarketingAiOptions {
  /**
   * Timeout for the request in milliseconds
   * @default 30000 (30 seconds)
   */
  timeout?: number;

  /**
   * Additional headers to send with the request
   */
  headers?: Record<string, string>;

  /**
   * Additional query parameters to send with the request
   */
  params?: Record<string, string>;
}
