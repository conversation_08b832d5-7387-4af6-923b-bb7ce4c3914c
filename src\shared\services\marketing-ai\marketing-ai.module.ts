import { Module, Global } from '@nestjs/common';
import { HttpModule } from '@nestjs/axios';
import { ConfigModule } from '@nestjs/config';

// Image Generation Services
import { OpenAiDalleService } from './image-generation/openai-dalle.service';
import { StabilityAiService } from './image-generation/stability-ai.service';
import { LeonardoAiService } from './image-generation/leonardo-ai.service';
import { ImageGenerationFactoryService } from './image-generation/image-generation-factory.service';

// Content Generation Services
import { OpenAiGptService } from './content-generation/openai-gpt.service';
import { AnthropicClaudeService } from './content-generation/anthropic-claude.service';
import { CohereService } from './content-generation/cohere.service';
import { ContentGenerationFactoryService } from './content-generation/content-generation-factory.service';

// Video Generation Services
import { RunwayMlService } from './video-generation/runway-ml.service';
import { SynthesiaService } from './video-generation/synthesia.service';
import { PikaLabsService } from './video-generation/pika-labs.service';
import { VideoGenerationFactoryService } from './video-generation/video-generation-factory.service';

/**
 * Module for marketing AI services
 */
@Global()
@Module({
  imports: [
    HttpModule.register({
      timeout: 60000, // 60 seconds
      maxRedirects: 5,
    }),
    ConfigModule,
  ],
  providers: [
    // Image Generation Services
    OpenAiDalleService,
    StabilityAiService,
    LeonardoAiService,
    ImageGenerationFactoryService,

    // Content Generation Services
    OpenAiGptService,
    AnthropicClaudeService,
    CohereService,
    ContentGenerationFactoryService,

    // Video Generation Services
    RunwayMlService,
    SynthesiaService,
    PikaLabsService,
    VideoGenerationFactoryService,
  ],
  exports: [
    // Image Generation Services
    OpenAiDalleService,
    StabilityAiService,
    LeonardoAiService,
    ImageGenerationFactoryService,

    // Content Generation Services
    OpenAiGptService,
    AnthropicClaudeService,
    CohereService,
    ContentGenerationFactoryService,

    // Video Generation Services
    RunwayMlService,
    SynthesiaService,
    PikaLabsService,
    VideoGenerationFactoryService,
  ],
})
export class MarketingAiModule {}
