# Config Module

## Giới thiệu

Module này cung cấp cấu hình cho toàn bộ ứng dụng, cho phép quản lý cấu hình theo môi trường và cung cấp các phương thức để truy cập cấu hình một cách type-safe.

## Cấu trúc

- `config.module.ts`: Mo<PERSON>le cấu hình
- `config.service.ts`: Service cung cấp các phương thức để truy cập cấu hình
- `validation.schema.ts`: Schema validation cho các biến môi trường
- `interfaces.ts`: Các interface định nghĩa cấu trúc của cấu hình
- `constants.ts`: Các hằng số liên quan đến cấu hình

## Cách sử dụng

### 1. Import module

```typescript
import { ConfigModule } from 'config';

@Module({
  imports: [ConfigModule],
  // ...
})
export class AppModule {}
```

### 2. Sử dụng ConfigService

```typescript
import { Injectable } from '@nestjs/common';
import { ConfigService, ConfigType } from 'config';

@Injectable()
export class YourService {
  constructor(private readonly configService: ConfigService) {}

  someMethod() {
    // Lấy toàn bộ cấu hình
    const appConfig = this.configService.appConfig;
    
    // Lấy cấu hình theo loại
    const databaseConfig = this.configService.getConfig<DatabaseConfig>(ConfigType.Database);
    
    // Kiểm tra môi trường
    if (this.configService.isDevelopment()) {
      // Do something in development environment
    }
  }
}
```

## Cấu hình môi trường

Module hỗ trợ các file cấu hình cho các môi trường khác nhau:

- `.env.development`: Môi trường development
- `.env.production`: Môi trường production
- `.env.test`: Môi trường test
- `.env.staging`: Môi trường staging
- `.env`: File cấu hình mặc định

Để chỉ định môi trường, sử dụng biến môi trường `NODE_ENV`:

```bash
NODE_ENV=production npm run start
```

## Các biến môi trường

Xem file `validation.schema.ts` để biết danh sách đầy đủ các biến môi trường được hỗ trợ.
