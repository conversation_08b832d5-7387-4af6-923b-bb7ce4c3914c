# Hướng dẫn sử dụng chức năng tự động inject tenantId

Chức năng tự động inject tenantId được thiết kế để tự động thêm điều kiện tenantId vào các câu truy vấn đối với các bảng có trường tenantId. Điều này giúp đơn giản hóa code và tránh phải thêm điều kiện tenantId vào mỗi câu truy vấn.

## Cách hoạt động

1. **TenantEntitySubscriber**: Subscriber này sẽ tự động thêm điều kiện tenantId vào tất cả các câu truy vấn đối với các entity có trường tenantId.
2. **TenantContextMiddleware**: Middleware này sẽ lấy tenantId từ request và lưu vào AsyncLocalStorage để TenantEntitySubscriber c<PERSON> thể sử dụng.
3. **WithTenant Decorator**: Decorator này giúp thiết lập tenantId vào context trong các trường hợp đặc biệt (ví dụ: trong các service không có request).

## Cách sử dụng

### 1. Trong Controller

Khi sử dụng decorator `TenantSecurity`, tenantId sẽ tự động được thiết lập vào context và các câu truy vấn sẽ tự động thêm điều kiện tenantId:

```typescript
import { Controller, Get } from '@nestjs/common';
import { TenantSecurity } from '@/common';

@Controller('users')
@TenantSecurity() // Áp dụng bảo mật tenant và tự động inject tenantId
export class UserController {
  constructor(private readonly userService: UserService) {}

  @Get()
  findAll() {
    // Không cần truyền tenantId vào phương thức findAll
    // TenantEntitySubscriber sẽ tự động thêm điều kiện tenantId vào câu truy vấn
    return this.userService.findAll();
  }
}
```

### 2. Trong Repository

Không cần thêm điều kiện tenantId vào các câu truy vấn, TenantEntitySubscriber sẽ tự động thêm:

```typescript
import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { User } from '../entities/user.entity';

@Injectable()
export class UserRepository {
  constructor(
    @InjectRepository(User)
    private readonly repository: Repository<User>,
  ) {}

  async findAll(): Promise<User[]> {
    // Không cần thêm điều kiện tenantId
    // TenantEntitySubscriber sẽ tự động thêm điều kiện tenantId vào câu truy vấn
    return this.repository.find();
  }

  async findById(id: number): Promise<User | null> {
    // Không cần thêm điều kiện tenantId
    return this.repository.findOne({ where: { id } });
  }

  async create(data: Partial<User>): Promise<User> {
    // Không cần thêm tenantId vào data
    // TenantEntitySubscriber sẽ tự động thêm tenantId vào entity trước khi insert
    const user = this.repository.create(data);
    return this.repository.save(user);
  }

  async update(id: number, data: Partial<User>): Promise<User | null> {
    // Không cần thêm điều kiện tenantId
    await this.repository.update({ id }, data);
    return this.findById(id);
  }

  async delete(id: number): Promise<boolean> {
    // Không cần thêm điều kiện tenantId
    const result = await this.repository.delete({ id });
    return result.affected > 0;
  }
}
```

### 3. Trong Service không có request

Sử dụng decorator `WithTenant` để thiết lập tenantId vào context:

```typescript
import { Injectable } from '@nestjs/common';
import { WithTenant } from '@/common';

@Injectable()
export class BackgroundService {
  constructor(private readonly userRepository: UserRepository) {}

  // Sử dụng với tenantId cố định
  @WithTenant(1)
  async processDataForTenant1(): Promise<void> {
    // Các thao tác với database sẽ tự động thêm điều kiện tenantId = 1
    const users = await this.userRepository.findAll();
    // ...
  }

  // Sử dụng với tenantId từ tham số
  async processDataForTenant(tenantId: number): Promise<void> {
    await this.processDataForTenantInternal(tenantId);
  }

  @WithTenant((args) => args[0])
  private async processDataForTenantInternal(tenantId: number): Promise<void> {
    // Các thao tác với database sẽ tự động thêm điều kiện tenantId = args[0]
    const users = await this.userRepository.findAll();
    // ...
  }
}
```

### 4. Truy cập dữ liệu của tất cả các tenant (chỉ dành cho SYSTEM_ADMIN)

Để truy cập dữ liệu của tất cả các tenant, chúng ta cần bổ sung chức năng tắt tạm thời tenant filtering. Dưới đây là cách triển khai:

#### 4.1. Cập nhật TenantEntitySubscriber

Cập nhật `src/common/subscribers/tenant-entity.subscriber.ts` để thêm flag `disableTenantFilter`:

```typescript
// Cập nhật AsyncLocalStorage để bao gồm flag disableTenantFilter
export const tenantContext = new AsyncLocalStorage<{ 
  tenantId: number;
  disableTenantFilter?: boolean; 
}>();

// Thêm phương thức để tắt tenant filtering
export function withoutTenantFilter<T>(callback: () => Promise<T>): Promise<T> {
  const currentStore = tenantContext.getStore();
  const tenantId = currentStore?.tenantId;
  
  // Tạo store mới với disableTenantFilter = true
  return tenantContext.run({ tenantId, disableTenantFilter: true }, callback);
}

// Cập nhật phương thức getCurrentTenantId
private getCurrentTenantId(): number | undefined {
  const store = tenantContext.getStore();
  
  // Nếu disableTenantFilter = true, trả về undefined để không thêm điều kiện tenantId
  if (store?.disableTenantFilter) {
    return undefined;
  }
  
  return store?.tenantId;
}
```

#### 4.2. Sử dụng trong service

```typescript
import { Injectable } from '@nestjs/common';
import { withoutTenantFilter } from '@/common/subscribers/tenant-entity.subscriber';

@Injectable()
export class AdminService {
  constructor(private readonly userRepository: UserRepository) {}

  // Phương thức để lấy dữ liệu của tất cả các tenant
  async findAllAcrossTenants(): Promise<User[]> {
    // Sử dụng withoutTenantFilter để tạm thời tắt tenant filtering
    return withoutTenantFilter(async () => {
      return this.userRepository.findAll();
    });
  }
}
```

#### 4.3. Tạo decorator cho admin API

```typescript
// src/common/decorators/admin-access.decorator.ts
import { createParamDecorator, ExecutionContext } from '@nestjs/common';
import { tenantContext } from '../subscribers/tenant-entity.subscriber';

export const AdminAccess = createParamDecorator(
  (data: unknown, ctx: ExecutionContext) => {
    const request = ctx.switchToHttp().getRequest();
    const user = request.user;
    
    // Kiểm tra xem người dùng có phải là SYSTEM_ADMIN không
    if (user?.type !== 'SYSTEM_ADMIN') {
      throw new Error('Chỉ SYSTEM_ADMIN mới có thể truy cập API này');
    }
    
    // Tạm thời tắt tenant filtering
    const currentStore = tenantContext.getStore();
    const tenantId = currentStore?.tenantId;
    
    tenantContext.enterWith({ tenantId, disableTenantFilter: true });
    
    return user;
  },
);
```

#### 4.4. Sử dụng trong controller

```typescript
import { Controller, Get } from '@nestjs/common';
import { AdminAccess } from '@/common/decorators/admin-access.decorator';

@Controller('admin/users')
export class AdminUserController {
  constructor(private readonly adminService: AdminService) {}

  @Get()
  findAll(@AdminAccess() admin: any) {
    // Tenant filtering đã bị tắt, có thể truy cập dữ liệu của tất cả các tenant
    return this.adminService.findAllAcrossTenants();
  }
}
```

## Lưu ý

1. Chức năng này chỉ hoạt động với các entity có trường tenantId.
2. Nếu bạn muốn truy cập dữ liệu của tất cả các tenant, bạn cần sử dụng `withoutTenantFilter` hoặc decorator `AdminAccess`.
3. Nếu bạn muốn truy cập dữ liệu của một tenant cụ thể khác với tenant hiện tại, bạn cần sử dụng decorator `WithTenant`.
4. TenantEntitySubscriber sẽ ngăn chặn việc thay đổi tenantId của entity trong quá trình cập nhật.
5. Chức năng này không thay thế hoàn toàn việc kiểm tra quyền truy cập, bạn vẫn cần kiểm tra quyền truy cập trong các controller và service.
6. Nếu bạn gặp vấn đề với chức năng này, bạn có thể quay lại cách truyền tenantId vào các phương thức repository.

## Ví dụ thực tế

### Ví dụ 1: Controller thông thường

```typescript
import { Controller, Get, Post, Body, Param, ParseIntPipe } from '@nestjs/common';
import { TenantSecurity } from '@/common';
import { UserService } from './user.service';
import { CreateUserDto } from './dto/create-user.dto';

@Controller('users')
@TenantSecurity()
export class UserController {
  constructor(private readonly userService: UserService) {}

  @Get()
  findAll() {
    // Không cần truyền tenantId, TenantEntitySubscriber sẽ tự động thêm
    return this.userService.findAll();
  }

  @Get(':id')
  findById(@Param('id', ParseIntPipe) id: number) {
    // Không cần truyền tenantId, TenantEntitySubscriber sẽ tự động thêm
    return this.userService.findById(id);
  }

  @Post()
  create(@Body() dto: CreateUserDto) {
    // Không cần truyền tenantId, TenantEntitySubscriber sẽ tự động thêm
    return this.userService.create(dto);
  }
}
```

### Ví dụ 2: Admin controller

```typescript
import { Controller, Get, Param, ParseIntPipe } from '@nestjs/common';
import { AdminAccess } from '@/common/decorators/admin-access.decorator';
import { AdminService } from './admin.service';

@Controller('admin/users')
export class AdminUserController {
  constructor(private readonly adminService: AdminService) {}

  @Get()
  findAll(@AdminAccess() admin: any) {
    // Tenant filtering đã bị tắt, có thể truy cập dữ liệu của tất cả các tenant
    return this.adminService.findAllAcrossTenants();
  }

  @Get(':id')
  findById(@Param('id', ParseIntPipe) id: number, @AdminAccess() admin: any) {
    // Tenant filtering đã bị tắt, có thể truy cập dữ liệu của tất cả các tenant
    return this.adminService.findByIdAcrossTenants(id);
  }
}
```
