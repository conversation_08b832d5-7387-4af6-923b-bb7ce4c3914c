import { Injectable, Logger } from '@nestjs/common';
import { InjectEntityManager } from '@nestjs/typeorm';
import { EntityManager } from 'typeorm';
import { TodoCollaboratorRepository } from '../repositories/todo-collaborator.repository';
import { TodoRepository } from '../repositories/todo.repository';
import { UserRepository } from '@/modules/auth/repositories/user.repository';
import { TodoCollaborator } from '../entities/todo-collaborator.entity';
import { CreateTodoCollaboratorDto } from '../dto/todo-collaborator/create-todo-collaborator.dto';
import { TodoCollaboratorQueryDto } from '../dto/todo-collaborator/todo-collaborator-query.dto';
import { TodoCollaboratorResponseDto } from '../dto/todo-collaborator/todo-collaborator-response.dto';
import { PaginatedResult } from '@/common/response/api-response-dto';
import { AppException } from '@/common/exceptions/app.exception';
import { TODOLISTS_ERROR_CODES } from '../errors/todolists-error.code';
import { AUTH_ERROR_CODE } from '@/modules/auth/errors/auth-error.code';
import { EventService } from './event.service';

/**
 * Service xử lý logic nghiệp vụ cho cộng tác viên công việc
 */
@Injectable()
export class TodoCollaboratorService {
  private readonly logger = new Logger(TodoCollaboratorService.name);

  constructor(
    private readonly todoCollaboratorRepository: TodoCollaboratorRepository,
    private readonly todoRepository: TodoRepository,
    private readonly userRepository: UserRepository,
    private readonly eventService: EventService,
    @InjectEntityManager() private readonly entityManager: EntityManager,
  ) {}

  /**
   * Chuyển đổi entity sang DTO
   * @param collaborator Entity cộng tác viên
   * @returns DTO cộng tác viên
   */
  private mapToDto(
    collaborator: TodoCollaborator,
  ): TodoCollaboratorResponseDto {
    return {
      id: collaborator.id,
      todoId: collaborator.todoId,
      userId: collaborator.userId,
      createdAt: collaborator.createdAt,
    };
  }

  /**
   * Thêm cộng tác viên cho công việc
   * @param tenantId ID tenant (required for tenant isolation)
   * @param currentUserId ID người dùng hiện tại
   * @param dto Dữ liệu cộng tác viên
   * @returns Thông tin cộng tác viên đã thêm
   */
  async addCollaborator(
    tenantId: number,
    currentUserId: number,
    dto: CreateTodoCollaboratorDto,
  ): Promise<TodoCollaboratorResponseDto> {
    try {
      // Kiểm tra công việc tồn tại
      const todo = await this.todoRepository.findById(tenantId, dto.todoId);
      if (!todo) {
        throw new AppException(
          TODOLISTS_ERROR_CODES.TODO_NOT_FOUND,
          `Không tìm thấy công việc với ID ${dto.todoId}`,
        );
      }

      // Kiểm tra người dùng tồn tại
      const user = await this.userRepository.findById(dto.userId);
      if (!user) {
        throw new AppException(
          AUTH_ERROR_CODE.USER_NOT_FOUND,
          `Không tìm thấy người dùng với ID ${dto.userId}`,
        );
      }

      // Kiểm tra người dùng đã là cộng tác viên chưa
      const isCollaborator =
        await this.todoCollaboratorRepository.isCollaborator(
          tenantId,
          dto.todoId,
          dto.userId,
        );
      if (isCollaborator) {
        throw new AppException(
          TODOLISTS_ERROR_CODES.COLLABORATOR_ALREADY_EXISTS,
          'Người dùng đã là cộng tác viên của công việc này',
        );
      }

      // Sử dụng transaction để đảm bảo tính toàn vẹn dữ liệu
      return await this.entityManager.transaction(
        async (transactionManager) => {
          // Tạo cộng tác viên mới
          const now = Date.now();
          const collaborator = await this.todoCollaboratorRepository.create(
            tenantId,
            {
              todoId: dto.todoId,
              userId: dto.userId,
              createdAt: now,
            },
            transactionManager,
          );

          // Tạo sự kiện hệ thống
          await this.eventService.createCollaboratorChangedEvent(
            tenantId,
            dto.todoId,
            dto.userId,
            currentUserId,
            'added',
            transactionManager,
          );

          return this.mapToDto(collaborator);
        },
      );
    } catch (error) {
      this.logger.error(
        `Lỗi khi thêm cộng tác viên: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        TODOLISTS_ERROR_CODES.COLLABORATOR_CREATION_FAILED,
        'Không thể thêm cộng tác viên',
      );
    }
  }

  /**
   * Lấy danh sách cộng tác viên của công việc
   * @param tenantId ID tenant (required for tenant isolation)
   * @param query Tham số truy vấn
   * @returns Danh sách cộng tác viên đã phân trang
   */
  async findAll(
    tenantId: number,
    query: TodoCollaboratorQueryDto,
  ): Promise<PaginatedResult<TodoCollaboratorResponseDto>> {
    try {
      const paginatedResult = await this.todoCollaboratorRepository.findAll(
        tenantId,
        query,
      );

      return {
        items: paginatedResult.items.map((collaborator) =>
          this.mapToDto(collaborator),
        ),
        meta: paginatedResult.meta,
      };
    } catch (error) {
      this.logger.error(
        `Lỗi khi lấy danh sách cộng tác viên: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        TODOLISTS_ERROR_CODES.COLLABORATOR_FETCH_FAILED,
        'Không thể lấy danh sách cộng tác viên',
      );
    }
  }

  /**
   * Lấy danh sách cộng tác viên của một công việc
   * @param tenantId ID tenant (required for tenant isolation)
   * @param todoId ID công việc
   * @returns Danh sách cộng tác viên
   */
  async findByTodoId(
    tenantId: number,
    todoId: number,
  ): Promise<TodoCollaboratorResponseDto[]> {
    try {
      const collaborators = await this.todoCollaboratorRepository.findByTodoId(
        tenantId,
        todoId,
      );
      return collaborators.map((collaborator) => this.mapToDto(collaborator));
    } catch (error) {
      this.logger.error(
        `Lỗi khi lấy danh sách cộng tác viên của công việc: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        TODOLISTS_ERROR_CODES.COLLABORATOR_FETCH_FAILED,
        'Không thể lấy danh sách cộng tác viên của công việc',
      );
    }
  }

  /**
   * Xóa cộng tác viên
   * @param tenantId ID tenant (required for tenant isolation)
   * @param currentUserId ID người dùng hiện tại
   * @param todoId ID công việc
   * @param userId ID người dùng cần xóa khỏi cộng tác viên
   */
  async removeCollaborator(
    tenantId: number,
    currentUserId: number,
    todoId: number,
    userId: number,
  ): Promise<void> {
    try {
      // Kiểm tra công việc tồn tại
      const todo = await this.todoRepository.findById(tenantId, todoId);
      if (!todo) {
        throw new AppException(
          TODOLISTS_ERROR_CODES.TODO_NOT_FOUND,
          `Không tìm thấy công việc với ID ${todoId}`,
        );
      }

      // Kiểm tra người dùng có phải là cộng tác viên không
      const isCollaborator =
        await this.todoCollaboratorRepository.isCollaborator(
          tenantId,
          todoId,
          userId,
        );
      if (!isCollaborator) {
        throw new AppException(
          TODOLISTS_ERROR_CODES.COLLABORATOR_NOT_FOUND,
          'Người dùng không phải là cộng tác viên của công việc này',
        );
      }

      // Sử dụng transaction để đảm bảo tính toàn vẹn dữ liệu
      await this.entityManager.transaction(async (transactionManager) => {
        // Xóa cộng tác viên
        await this.todoCollaboratorRepository.removeByTodoIdAndUserId(
          tenantId,
          todoId,
          userId,
          transactionManager,
        );

        // Tạo sự kiện hệ thống
        await this.eventService.createCollaboratorChangedEvent(
          tenantId,
          todoId,
          userId,
          currentUserId,
          'removed',
          transactionManager,
        );
      });
    } catch (error) {
      this.logger.error(
        `Lỗi khi xóa cộng tác viên: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        TODOLISTS_ERROR_CODES.COLLABORATOR_DELETION_FAILED,
        'Không thể xóa cộng tác viên',
      );
    }
  }
}
