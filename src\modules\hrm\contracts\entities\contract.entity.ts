import { Entity, PrimaryGeneratedColumn, Column } from 'typeorm';
import { ContractStatus } from '../enum/contract-status.enum';
import { ContractType } from '../enum/contract-type.enum';

/**
 * Entity representing employee contracts
 */
@Entity('contracts')
export class Contract {
  /**
   * Unique identifier for the contract
   */
  @PrimaryGeneratedColumn()
  id: number;

  /**
   * Contract code (unique identifier)
   */
  @Column({
    name: 'contract_code',
    type: 'varchar',
    length: 50,
    nullable: false,
  })
  contractCode: string;

  /**
   * ID of the employee associated with this contract
   */
  @Column({ name: 'employee_id', type: 'integer', nullable: false })
  employeeId: number;

  /**
   * Contract type (probation, definite, indefinite, etc.)
   */
  @Column({
    name: 'contract_type',
    type: 'enum',
    enum: ContractType,
    enumName: 'contract_type_enum',
    nullable: false,
  })
  contractType: ContractType;

  /**
   * Contract title
   */
  @Column({ name: 'title', type: 'varchar', length: 255, nullable: false })
  title: string;

  /**
   * Contract description
   */
  @Column({ name: 'description', type: 'text', nullable: true })
  description: string | null;

  /**
   * Contract start date
   */
  @Column({ name: 'start_date', type: 'date', nullable: false })
  startDate: Date;

  /**
   * Contract end date (null for indefinite contracts)
   */
  @Column({ name: 'end_date', type: 'date', nullable: true })
  endDate: Date | null;

  /**
   * Contract signing date
   */
  @Column({ name: 'signing_date', type: 'date', nullable: true })
  signingDate: Date | null;

  /**
   * Contract status
   */
  @Column({
    name: 'status',
    type: 'enum',
    enum: ContractStatus,
    enumName: 'contract_status_enum',
    nullable: false,
    default: ContractStatus.DRAFT,
  })
  status: ContractStatus;

  /**
   * Base salary amount
   */
  @Column({
    name: 'base_salary',
    type: 'decimal',
    precision: 15,
    scale: 2,
    nullable: false,
  })
  baseSalary: number;

  /**
   * Salary currency
   */
  @Column({
    name: 'currency',
    type: 'varchar',
    length: 10,
    nullable: false,
    default: 'VND',
  })
  currency: string;

  /**
   * Working hours per week
   */
  @Column({ name: 'working_hours_per_week', type: 'integer', nullable: true })
  workingHoursPerWeek: number | null;

  /**
   * Probation period in days (if applicable)
   */
  @Column({ name: 'probation_period_days', type: 'integer', nullable: true })
  probationPeriodDays: number | null;

  /**
   * Notice period in days
   */
  @Column({ name: 'notice_period_days', type: 'integer', nullable: true })
  noticePeriodDays: number | null;

  /**
   * Contract termination date (if applicable)
   */
  @Column({ name: 'termination_date', type: 'date', nullable: true })
  terminationDate: Date | null;

  /**
   * Reason for termination (if applicable)
   */
  @Column({
    name: 'termination_reason',
    type: 'varchar',
    length: 500,
    nullable: true,
  })
  terminationReason: string | null;

  /**
   * Path to contract document file
   */
  @Column({
    name: 'document_path',
    type: 'varchar',
    length: 500,
    nullable: true,
  })
  documentPath: string | null;

  /**
   * Notes about the contract
   */
  @Column({ name: 'notes', type: 'text', nullable: true })
  notes: string | null;

  /**
   * Creation timestamp (in milliseconds)
   */
  @Column({ name: 'created_at', type: 'bigint', nullable: true })
  createdAt: number | null;

  /**
   * Last update timestamp (in milliseconds)
   */
  @Column({ name: 'updated_at', type: 'bigint', nullable: true })
  updatedAt: number | null;

  /**
   * ID of the user who created this record
   */
  @Column({ name: 'created_by', type: 'integer', nullable: true })
  createdBy: number | null;

  /**
   * ID of the user who last updated this record
   */
  @Column({ name: 'updated_by', type: 'integer', nullable: true })
  updatedBy: number | null;

  /**
   * ID of the company/organization that owns this record
   */
  @Column({ name: 'tenant_id', type: 'integer', nullable: true })
  tenantId: number | null;
}
