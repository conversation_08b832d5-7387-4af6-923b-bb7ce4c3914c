import { FileTypeEnum } from '@shared/utils';
import { ImageTypeEnum } from '@shared/utils';
import { VideoTypeEnum } from '@shared/utils';

/**
 * Union type cho tất cả các loại media type được hỗ trợ
 */
export type MediaType = ImageTypeEnum | FileTypeEnum | VideoTypeEnum;

/**
 * Object tiện ích để làm việc với tất cả các loại media type
 */
export const MediaTypeUtil = {
  /**
   * Lấy giá trị MIME type từ bất kỳ loại media type nào
   * @param type Loại media (ImageTypeEnum, FileTypeEnum, VideoTypeEnum)
   * @returns Giá trị MIME type tương ứng
   */
  getValue(type: MediaType): string {
    return type;
  },

  /**
   * Kiểm tra xem một giá trị có phải là ImageTypeEnum không
   * @param type Giá trị cần kiểm tra
   * @returns true nếu là ImageTypeEnum, false nếu không phải
   */
  isImageType(type: MediaType): type is ImageTypeEnum {
    return Object.values(ImageTypeEnum).includes(type as ImageTypeEnum);
  },

  /**
   * Kiểm tra xem một giá trị có phải là FileTypeEnum không
   * @param type Giá trị cần kiểm tra
   * @returns true nếu là FileTypeEnum, false nếu không phải
   */
  isFileType(type: MediaType): type is FileTypeEnum {
    return Object.values(FileTypeEnum).includes(type as FileTypeEnum);
  },

  /**
   * Kiểm tra xem một giá trị có phải là VideoTypeEnum không
   * @param type Giá trị cần kiểm tra
   * @returns true nếu là VideoTypeEnum, false nếu không phải
   */
  isVideoType(type: MediaType): type is VideoTypeEnum {
    return Object.values(VideoTypeEnum).includes(type as VideoTypeEnum);
  },
};
