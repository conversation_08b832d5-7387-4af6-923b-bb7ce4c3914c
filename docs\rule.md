# Tóm tắt Quy tắc phát triển API cho RedAI Backend

## C<PERSON>u trúc phản hồi API

### Ph<PERSON>n hồi thành công
Tất cả API sử dụng `ApiResponseDto` từ `@src/common/response/api-response-dto.ts`:
```json
{
  "code": 200,
  "message": "Success",
  "result": {}
}
```
- Sử dụng phương thức tĩnh như `ApiResponseDto.success`, `ApiResponseDto.created`, `ApiResponseDto.deleted` trong controller.

### Tham số truy vấn danh sách
API lấy danh sách sử dụng `QueryDto` từ `@src/common/dto/query.dto.ts`:
- C<PERSON><PERSON> tham số: `page`, `limit`, `search`, `sortBy`, `sortDirection` (enum `SortDirection: ASC, DESC`).
- Mở rộng `QueryDto` khi cần thêm tham số:
```typescript
export class UserQueryDto extends QueryDto {
  @ApiProperty({ enum: UserStatus, required: false })
  @IsOptional()
  @IsEnum(UserStatus)
  status?: UserStatus;
}
```

### Dữ liệu phân trang
API phân trang sử dụng `PaginatedResult` và `ApiResponseDto.paginated`:
```json
{
  "code": 200,
  "message": "Success",
  "result": {
    "items": [],
    "meta": {
      "totalItems": 100,
      "itemCount": 10,
      "itemsPerPage": 10,
      "totalPages": 10,
      "currentPage": 1
    }
  }
}
```

## Xử lý lỗi

### Tổ chức mã lỗi
- Mỗi module có thư mục `errors` với file định nghĩa mã lỗi (ví dụ: `module-name-error.code.ts`).
- Phạm vi mã lỗi riêng (ví dụ: User: 10000-10099, R-Point: 12000-12099).
- Ví dụ:
```typescript
export const RPOINT_ERROR_CODES = {
  POINT_PACKAGE_NOT_FOUND: new ErrorCode(12000, 'Không tìm thấy gói point', HttpStatus.NOT_FOUND),
};
```

### Phản hồi lỗi
Sử dụng `AppException` từ `@src/common/exceptions/app.exception.ts`:
```typescript
throw new AppException(RPOINT_ERROR_CODES.POINT_PACKAGE_NOT_FOUND, `Không tìm thấy gói point với ID ${id}`);
```
Phản hồi lỗi:
```json
{
  "code": 12000,
  "message": "Không tìm thấy gói point với ID 123",
  "result": null
}
```

### Quy tắc xử lý lỗi
- Định nghĩa mã lỗi trong module, sử dụng constant `MODULE_NAME_ERROR_CODES`.
- Không dùng trực tiếp exception của NestJS (`NotFoundException`, ...).
- Xử lý lỗi trong `try/catch`, wrap lỗi chưa xác định vào `AppException`.
- Tên mã lỗi rõ ràng, chữ hoa, phân tách bằng gạch dưới (ví dụ: `USER_NOT_FOUND`).

## Authentication và Authorization
- **Tất cả API có bảo mật**: Đặt `@UseGuards(JwtUserGuard)` và `@ApiBearerAuth('JWT-auth')` trên mỗi controller có bảo mật.
- Sử dụng `@CurrentUser() user: JwtPayload` để lấy thông tin người dùng hiện tại, trong đó `JwtPayload` là interface từ `src/modules/auth/guards/jwt.util.ts`.
- Không sử dụng decorator `TenantSecurity` để áp dụng bảo mật, thay vào đó sử dụng trực tiếp `@UseGuards` và `@ApiBearerAuth`.
- Đảm bảo mỗi controller có bảo mật đều có đầy đủ cả hai decorator `@UseGuards` và `@ApiBearerAuth`.

### Cấu trúc JwtPayload
```typescript
export interface JwtPayload {
  id: number;
  sub: number; // Subject (usually user ID or employee ID)
  username?: string; // Username (optional)
  permissions?: string[]; // User permissions (optional)
  typeToken?: TokenType; // Token type (optional for backward compatibility)
  tenantId?: number;
  domain?: string | null;
  type: 'SYSTEM_ADMIN' | 'COMPANY_ADMIN' | 'EMPLOYEE'; // Loại người dùng
}
```

### Ví dụ
```typescript
import { Controller, Get, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { JwtUserGuard } from '@/modules/auth/guards/jwt-user.guard';
import { CurrentUser } from '@/modules/auth/decorators/current-user.decorator';
import { JwtPayload } from '@/modules/auth/guards/jwt.util';
import { SWAGGER_API_TAG } from '@/common/swagger/swagger.tags';

@ApiTags(SWAGGER_API_TAG.USERS)
@Controller('api/users')
@UseGuards(JwtUserGuard)
@ApiBearerAuth('JWT-auth')
export class UserController {
  constructor(private readonly userService: UserService) {}

  @Get('profile')
  getProfile(@CurrentUser() user: JwtPayload) {
    return this.userService.getProfile(user.id);
  }
}
```

## Swagger Documentation
- **Controller**:
  - Sử dụng `@ApiTags(SWAGGER_API_TAG.XXX)` với XXX là tag tương ứng từ `SWAGGER_API_TAG` trong `src/common/swagger/swagger.tags.ts`.
  - Không sử dụng `@ApiTags` trực tiếp với chuỗi, luôn sử dụng các hằng số từ `SWAGGER_API_TAG`.
  - Sử dụng `@ApiBearerAuth('JWT-auth')` cho các controller có bảo mật.
  - Sử dụng `@ApiExtraModels` khi cần thiết.
- **Endpoint**: Mô tả bằng `@ApiOperation`, `@ApiParam`, `@ApiQuery`, `@ApiBody`, `@ApiResponse`.
- **DTO**: Mô tả đầy đủ với `@ApiProperty` (bao gồm `description`, `example`, `required`).
- **Phân trang**: Mô tả cấu trúc `items` và `meta` trong `@ApiResponse`.
- Quy tắc: Nhất quán, đầy đủ, rõ ràng, cung cấp ví dụ, cập nhật thường xuyên.

### Ví dụ
```typescript
import { Controller, Get, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { JwtUserGuard } from '@/modules/auth/guards/jwt-user.guard';
import { CurrentUser } from '@/modules/auth/decorators/current-user.decorator';
import { JwtPayload } from '@/modules/auth/guards/jwt.util';
import { SWAGGER_API_TAG } from '@/common/swagger/swagger.tags';
import { ApiResponseDto } from '@/common/response/api-response-dto';

@ApiTags(SWAGGER_API_TAG.USERS)
@Controller('api/users')
@UseGuards(JwtUserGuard)
@ApiBearerAuth('JWT-auth')
export class UserController {
  constructor(private readonly userService: UserService) {}

  @Get('profile')
  @ApiOperation({ summary: 'Lấy thông tin profile của người dùng' })
  @ApiResponse({
    status: 200,
    description: 'Thông tin profile của người dùng.',
    type: ApiResponseDto,
  })
  getProfile(@CurrentUser() user: JwtPayload) {
    return ApiResponseDto.success(this.userService.getProfile(user.id));
  }
}
```

## Xử lý URL Media
### Hiển thị tài nguyên
- Sử dụng `CdnService` để tạo URL có chữ ký:
```typescript
avatarUrl = this.cdnService.generateUrlView(user.avatarKey, TimeIntervalEnum.ONE_HOUR);
```
- Không lưu URL vào database, chỉ lưu key. Chọn thời hạn phù hợp từ `TimeIntervalEnum`.

### Upload tài nguyên
- Sử dụng `S3Service` để tạo presigned URL:
```typescript
const presignedUrl = await this.s3Service.createPresignedWithID(key, TimeIntervalEnum.FIFTEEN_MINUTES, MediaType.IMAGE_JPEG, FileSizeEnum.ONE_MB);
```
- Frontend gọi API lấy URL, upload file bằng PUT, rồi cập nhật key vào database.

## Tuân thủ Entity và Database
- Không tự ý thêm/sửa entity, mọi thay đổi cần migration và đồng thuận.
- Kiểu dữ liệu entity khớp với database, sử dụng decorator TypeORM phù hợp.
- Tránh relationship mapping (`@OneToMany`, ...), ưu tiên tham chiếu trực tiếp (`userId`).
- Đánh dấu rõ trường nullable, xử lý null/undefined phù hợp.
- Trước khi triển khai chức năng, phải xem kỹ cấu trúc các entity và các trường dữ liệu liên quan.
- Không thêm trường mới vào entity mà không có sự đồng ý của team.
- Khi sử dụng entity, phải đảm bảo sử dụng đúng tên trường và kiểu dữ liệu.
- Sau mỗi thay đổi, chạy `npm run build` để kiểm tra lỗi TypeScript và sửa ngay lập tức.

### Quy trình kiểm tra code
1. **Trước khi bắt đầu**: Xem kỹ cấu trúc entity và các trường dữ liệu liên quan.
2. **Trong quá trình phát triển**: Chạy `npm run build` thường xuyên để phát hiện lỗi TypeScript sớm.
3. **Sau mỗi chức năng**: Chạy `npm run build` và sửa tất cả các lỗi trước khi chuyển sang chức năng tiếp theo.
4. **Trước khi commit**: Chạy `npm run lint` và `npm run build` để đảm bảo không có lỗi.

### Ví dụ quy trình làm việc
```bash
# 1. Xem cấu trúc entity trước khi bắt đầu
cat src/modules/okrs/entities/objective.entity.ts

# 2. Phát triển chức năng

# 3. Kiểm tra lỗi TypeScript
npm run build

# 4. Sửa lỗi nếu có

# 5. Kiểm tra lại
npm run build

# 6. Kiểm tra lint trước khi commit
npm run lint

# 7. Commit code
git add .
git commit -m "feat: Thêm chức năng XYZ"
```

## Tuân thủ TypeScript
- Khai báo kiểu dữ liệu rõ ràng, tránh `any`.
- Sử dụng optional chaining (`?.`) và nullish coalescing (`??`).
- Bật `strict: true` trong `tsconfig.json`, không dùng `@ts-ignore`.
- Chạy `npm run build` hoặc `npm run check-types` trước khi commit.

## Quản lý Tenant và TenantId

### Cơ chế hoạt động

Hệ thống quản lý TenantId được thiết kế để đảm bảo tính bảo mật dữ liệu giữa các công ty/tổ chức trong hệ thống đa tenant. Mỗi tenant (công ty/tổ chức) sẽ chỉ có thể truy cập dữ liệu của chính mình, không thể truy cập dữ liệu của tenant khác.

Hệ thống sử dụng kết hợp các thành phần sau để quản lý TenantId:

1. **TenantSecurityMiddleware**: Trích xuất tenantId từ JWT token và gắn vào request
2. **TenantContextMiddleware**: Lưu tenantId vào AsyncLocalStorage để sử dụng trong toàn bộ request lifecycle
3. **TenantEntitySubscriber**: Tự động thêm điều kiện tenantId vào các câu truy vấn database
4. **WithTenant Decorator**: Cho phép thiết lập tenantId trong các context không có request

### Quy tắc sử dụng TenantId

#### 1. Trong Controller

##### Cách đúng:

```typescript
import { Controller, Get, UseGuards } from '@nestjs/common';
import { JwtUserGuard } from '@/modules/auth/guards/jwt-user.guard';
import { ApiBearerAuth } from '@nestjs/swagger';

@Controller('users')
@UseGuards(JwtUserGuard)
@ApiBearerAuth('JWT-auth')
export class UserController {
  constructor(private readonly userService: UserService) {}

  @Get()
  findAll() {
    // TenantId được tự động thêm vào các câu truy vấn
    return this.userService.findAll();
  }
}
```

##### Cách sai:

```typescript
import { Controller, Get } from '@nestjs/common';
import { TenantSecurity, CurrentTenant } from '@/common';

@Controller('users')
@TenantSecurity() // KHÔNG sử dụng decorator này nữa
export class UserController {
  constructor(private readonly userService: UserService) {}

  @Get()
  findAll(@CurrentTenant() tenantId: number) { // KHÔNG sử dụng decorator này nữa
    return this.userService.findAll(tenantId); // KHÔNG truyền tenantId vào service
  }
}
```

#### 2. Trong Repository

##### Cách đúng:

```typescript
@Injectable()
export class UserRepository {
  constructor(
    @InjectRepository(User)
    private readonly repository: Repository<User>,
  ) {}

  async findAll(): Promise<User[]> {
    // TenantId được tự động thêm vào câu truy vấn
    return this.repository.find();
  }

  async findById(id: number): Promise<User | null> {
    // TenantId được tự động thêm vào câu truy vấn
    return this.repository.findOne({ where: { id } });
  }
}
```

##### Cách sai:

```typescript
@Injectable()
export class UserRepository {
  constructor(
    @InjectRepository(User)
    private readonly repository: Repository<User>,
  ) {}

  async findAll(tenantId: number): Promise<User[]> { // KHÔNG nhận tenantId làm tham số
    // KHÔNG thêm điều kiện tenantId thủ công
    return this.repository.find({ where: { tenantId } });
  }

  async findById(id: number, tenantId: number): Promise<User | null> { // KHÔNG nhận tenantId làm tham số
    // KHÔNG thêm điều kiện tenantId thủ công
    return this.repository.findOne({ where: { id, tenantId } });
  }
}
```

#### 3. Trong Service không có request context

Khi cần thực hiện các thao tác với database trong một context không có request (ví dụ: trong các job, cronjob, hoặc các service được gọi từ bên ngoài request lifecycle), sử dụng decorator `WithTenant`:

```typescript
import { Injectable } from '@nestjs/common';
import { WithTenant } from '@/common/decorators/with-tenant.decorator';

@Injectable()
export class BackgroundService {
  constructor(private readonly userRepository: UserRepository) {}

  @WithTenant(1) // Thiết lập tenantId cố định
  async processDataForTenant1() {
    // TenantId = 1 được tự động thêm vào các câu truy vấn
    return this.userRepository.findAll();
  }

  async processDataForTenant(tenantId: number) {
    await this.processDataForSpecificTenant(tenantId);
  }

  @WithTenant((args) => args[0]) // Lấy tenantId từ tham số đầu tiên
  private async processDataForSpecificTenant(tenantId: number) {
    // TenantId từ tham số được tự động thêm vào các câu truy vấn
    return this.userRepository.findAll();
  }
}
```

#### 4. Truy cập dữ liệu của tất cả các tenant (Admin)

Khi cần truy cập dữ liệu của tất cả các tenant (ví dụ: cho admin), sử dụng phương thức `withoutTenantFilter`:

```typescript
import { Injectable } from '@nestjs/common';
import { tenantContext } from '@/common/subscribers/tenant-entity.subscriber';

@Injectable()
export class AdminService {
  constructor(private readonly userRepository: UserRepository) {}

  async findAllUsersAcrossTenants() {
    // Tạm thời tắt tenant filtering
    return tenantContext.run({ tenantId: 0, disableTenantFilter: true }, () => {
      return this.userRepository.findAll();
    });
  }
}
```

### Quy tắc triển khai API mới

1. **Không sử dụng** decorator `@TenantSecurity()` và `@CurrentTenant()` trong controller mới
2. **Luôn sử dụng** `@UseGuards(JwtUserGuard)` và `@ApiBearerAuth('JWT-auth')` cho các API cần xác thực
3. **Không truyền** tenantId từ controller xuống service hoặc repository
4. **Không thêm** điều kiện tenantId thủ công vào câu truy vấn
5. **Không lưu** tenantId thủ công khi tạo entity mới (tenantId sẽ được tự động thêm)
6. **Sử dụng** decorator `@WithTenant` cho các service không có request context
7. **Sử dụng** `disableTenantFilter: true` khi cần truy cập dữ liệu của tất cả các tenant

### Lưu ý quan trọng

1. Middleware bảo mật tenant đã được áp dụng toàn cục trong `CommonModule`, không cần áp dụng thêm
2. TenantId được tự động trích xuất từ JWT token và lưu vào AsyncLocalStorage
3. TenantEntitySubscriber tự động thêm điều kiện tenantId vào tất cả các câu truy vấn
4. Không thể thay đổi tenantId của entity sau khi đã tạo
5. Chỉ SYSTEM_ADMIN mới có thể truy cập dữ liệu của tenant khác

### Ví dụ triển khai API đăng ký tài khoản nhân viên

```typescript
// Controller
@Post('register-employee')
@UseGuards(JwtUserGuard)
@ApiBearerAuth('JWT-auth')
async registerEmployee(
  @Body() registerDto: RegisterEmployeeDto,
): Promise<ApiResponseDto<RegisterEmployeeResponseDto>> {
  return this.userAuthService.registerEmployee(registerDto);
}

// Service
async registerEmployee(
  registerDto: RegisterEmployeeDto,
): Promise<ApiResponseDto<RegisterEmployeeResponseDto>> {
  // Tạo tài khoản người dùng mới
  await this.userRepository.create({
    username: registerDto.username,
    email: registerDto.email,
    password: hashedPassword,
    fullName: registerDto.fullName,
    departmentId: registerDto.departmentId || null,
    status: UserStatus.ACTIVE,
    createdAt: Date.now(),
    // tenantId được tự động thêm bởi middleware bảo mật tenant
  });

  return ApiResponseDto.created({ message: 'Đăng ký thành công' });
}
```

## Quy trình phát triển
1. **Lập kế hoạch**: Tạo file markdown trong `@docs/plan` (format: `YYYYMMDD-module-name-plan.md`).
2. **Kiểm tra code**: Chạy `npm run lint`, `npm run build`, `npm test` trước khi push.
3. **Commit**: Commit thường xuyên, tên rõ ràng, mô tả chi tiết (ví dụ: "User API: Thêm endpoint đăng ký").
4. **Code review**: PR phải tuân thủ quy tắc, không có lỗi TypeScript.

## Unit Test
- Bắt buộc viết test cho mỗi tính năng, đạt độ bao phủ tối thiểu 80%.
- Test gồm unit, integration, E2E; tập trung vào luồng chính và ngoại lệ.
- Tổ chức test theo cấu trúc code, sử dụng mock/stub cho dependency.
- Chạy `npm run test` trước khi commit, đảm bảo CI chạy test tự động.
- Test API kiểm tra cấu trúc response, xử lý lỗi, và validation.