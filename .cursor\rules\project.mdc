---
description: 
globs: 
alwaysApply: true
---
# Tóm tắt Quy tắc phát triển API cho RedAI Backend

## Cấu trúc phản hồi API

### Phản hồi thành công
Tất cả API sử dụng `ApiResponseDto` từ `@src/common/response/api-response-dto.ts`:
```json
{
  "code": 200,
  "message": "Success",
  "result": {}
}
```
- Sử dụng phương thức tĩnh như `ApiResponseDto.success`, `ApiResponseDto.created`, `ApiResponseDto.deleted` trong controller.

### Tham số truy vấn danh sách
API lấy danh sách sử dụng `QueryDto` từ `@src/common/dto/query.dto.ts`:
- Các tham số: `page`, `limit`, `search`, `sortBy`, `sortDirection` (enum `SortDirection: ASC, DESC`).
- Mở rộng `QueryDto` khi cần thêm tham số:
```typescript
export class UserQueryDto extends QueryDto {
  @ApiProperty({ enum: UserStatus, required: false })
  @IsOptional()
  @IsEnum(UserStatus)
  status?: UserStatus;
}
```

### Dữ liệu phân trang
API phân trang sử dụng `PaginatedResult` và `ApiResponseDto.paginated`:
```json
{
  "code": 200,
  "message": "Success",
  "result": {
    "items": [],
    "meta": {
      "totalItems": 100,
      "itemCount": 10,
      "itemsPerPage": 10,
      "totalPages": 10,
      "currentPage": 1
    }
  }
}
```

## Xử lý lỗi

### Tổ chức mã lỗi
- Mỗi module có thư mục `errors` với file định nghĩa mã lỗi (ví dụ: `module-name-error.code.ts`).
- Phạm vi mã lỗi riêng (ví dụ: User: 10000-10099, R-Point: 12000-12099).
- Ví dụ:
```typescript
export const RPOINT_ERROR_CODES = {
  POINT_PACKAGE_NOT_FOUND: new ErrorCode(12000, 'Không tìm thấy gói point', HttpStatus.NOT_FOUND),
};
```

### Phản hồi lỗi
Sử dụng `AppException` từ `@src/common/exceptions/app.exception.ts`:
```typescript
throw new AppException(RPOINT_ERROR_CODES.POINT_PACKAGE_NOT_FOUND, `Không tìm thấy gói point với ID ${id}`);
```
Phản hồi lỗi:
```json
{
  "code": 12000,
  "message": "Không tìm thấy gói point với ID 123",
  "result": null
}
```

### Quy tắc xử lý lỗi
- Định nghĩa mã lỗi trong module, sử dụng constant `MODULE_NAME_ERROR_CODES`.
- Không dùng trực tiếp exception của NestJS (`NotFoundException`, ...).
- Xử lý lỗi trong `try/catch`, wrap lỗi chưa xác định vào `AppException`.
- Tên mã lỗi rõ ràng, chữ hoa, phân tách bằng gạch dưới (ví dụ: `USER_NOT_FOUND`).

## Authentication và Authorization
- **Tất cả API có bảo mật**: Đặt `@UseGuards(JwtUserGuard)` và `@ApiBearerAuth('JWT-auth')` trên mỗi controller có bảo mật.
- Sử dụng `@CurrentUser() user: JwtPayload` để lấy thông tin người dùng hiện tại, trong đó `JwtPayload` là interface từ `src/modules/auth/guards/jwt.util.ts`.
- Cấu trúc `JwtPayload` bao gồm: `id`, `sub`, `username?`, `permissions?`, `typeToken?`, `tenantId?`, `domain?`, `type` (SYSTEM_ADMIN | COMPANY_ADMIN | EMPLOYEE).
- Không sử dụng decorator `TenantSecurity` để áp dụng bảo mật, thay vào đó sử dụng trực tiếp `@UseGuards` và `@ApiBearerAuth`.
- Đảm bảo mỗi controller có bảo mật đều có đầy đủ cả hai decorator `@UseGuards` và `@ApiBearerAuth`.

## Swagger Documentation
- **Controller**:
  - Sử dụng `@ApiTags(SWAGGER_API_TAG.XXX)` với XXX là tag tương ứng từ `SWAGGER_API_TAG` trong `src/common/swagger/swagger.tags.ts`.
  - Không sử dụng `@ApiTags` trực tiếp với chuỗi, luôn sử dụng các hằng số từ `SWAGGER_API_TAG`.
  - Sử dụng `@ApiBearerAuth('JWT-auth')` cho các controller có bảo mật.
  - Sử dụng `@ApiExtraModels` khi cần thiết.
- **Endpoint**: Mô tả bằng `@ApiOperation`, `@ApiParam`, `@ApiQuery`, `@ApiBody`, `@ApiResponse`.
- **DTO**: Mô tả đầy đủ với `@ApiProperty` (bao gồm `description`, `example`, `required`).
- **Phân trang**: Mô tả cấu trúc `items` và `meta` trong `@ApiResponse`.
- Quy tắc: Nhất quán, đầy đủ, rõ ràng, cung cấp ví dụ, cập nhật thường xuyên.

## Xử lý URL Media
### Hiển thị tài nguyên
- Sử dụng `CdnService` để tạo URL có chữ ký:
```typescript
avatarUrl = this.cdnService.generateUrlView(user.avatarKey, TimeIntervalEnum.ONE_HOUR);
```
- Không lưu URL vào database, chỉ lưu key. Chọn thời hạn phù hợp từ `TimeIntervalEnum`.

### Upload tài nguyên
- Sử dụng `S3Service` để tạo presigned URL:
```typescript
const presignedUrl = await this.s3Service.createPresignedWithID(key, TimeIntervalEnum.FIFTEEN_MINUTES, MediaType.IMAGE_JPEG, FileSizeEnum.ONE_MB);
```
- Frontend gọi API lấy URL, upload file bằng PUT, rồi cập nhật key vào database.

## Tuân thủ Entity và Database
- Không tự ý thêm/sửa entity, mọi thay đổi cần migration và đồng thuận.
- Kiểu dữ liệu entity khớp với database, sử dụng decorator TypeORM phù hợp.
- Tránh relationship mapping (`@OneToMany`, ...), ưu tiên tham chiếu trực tiếp (`userId`).
- Đánh dấu rõ trường nullable, xử lý null/undefined phù hợp.
- Trước khi triển khai chức năng, phải xem kỹ cấu trúc các entity và các trường dữ liệu liên quan.
- Không thêm trường mới vào entity mà không có sự đồng ý của team.
- Khi sử dụng entity, phải đảm bảo sử dụng đúng tên trường và kiểu dữ liệu.
- Sau mỗi thay đổi, chạy `npm run build` để kiểm tra lỗi TypeScript và sửa ngay lập tức.

## Tuân thủ TypeScript
- Khai báo kiểu dữ liệu rõ ràng, tránh `any`.
- Sử dụng optional chaining (`?.`) và nullish coalescing (`??`).
- Bật `strict: true` trong `tsconfig.json`, không dùng `@ts-ignore`.
- Chạy `npm run build` hoặc `npm run check-types` trước khi commit.

## Quản lý Tenant và TenantId
- **Cơ chế hoạt động**: Hệ thống sử dụng TenantSecurityMiddleware, TenantContextMiddleware, TenantEntitySubscriber và WithTenant Decorator để quản lý tenantId.
- **Trong Controller**: Sử dụng `@UseGuards(JwtUserGuard)` và `@ApiBearerAuth('JWT-auth')`. KHÔNG sử dụng `@TenantSecurity()` và `@CurrentTenant()`.
- **Trong Repository**: KHÔNG thêm điều kiện tenantId thủ công vào câu truy vấn và KHÔNG nhận tenantId làm tham số.
- **Trong Service**: KHÔNG truyền tenantId từ controller xuống service và KHÔNG lưu tenantId thủ công khi tạo entity mới.
- **Trong Service không có request**: Sử dụng decorator `@WithTenant` để thiết lập tenantId vào context.
- **Truy cập dữ liệu của tất cả các tenant (Admin)**: Sử dụng `tenantContext.run({ tenantId: 0, disableTenantFilter: true }, () => {...})`.
- **Lưu ý**: Middleware bảo mật tenant đã được áp dụng toàn cục, tenantId được tự động trích xuất từ JWT token, và chỉ SYSTEM_ADMIN mới có thể truy cập dữ liệu của tenant khác.
- **Tham khảo**: Xem file `docs/tenant-id-implementation-guide.md` để biết thêm chi tiết và ví dụ cụ thể.

## Quy trình phát triển
1. **Lập kế hoạch**: Tạo file markdown trong `@docs/plan` (format: `YYYYMMDD-module-name-plan.md`).
2. **Kiểm tra code**: Chạy `npm run lint`, `npm run build`, `npm test` trước khi push.
3. **Commit**: Commit thường xuyên, tên rõ ràng, mô tả chi tiết (ví dụ: "User API: Thêm endpoint đăng ký").
4. **Code review**: PR phải tuân thủ quy tắc, không có lỗi TypeScript.

## Unit Test
- Bắt buộc viết test cho mỗi tính năng, đạt độ bao phủ tối thiểu 80%.
- Test gồm unit, integration, E2E; tập trung vào luồng chính và ngoại lệ.
- Tổ chức test theo cấu trúc code, sử dụng mock/stub cho dependency.
- Chạy `npm run test` trước khi commit, đảm bảo CI chạy test tự động.
- Test API kiểm tra cấu trúc response, xử lý lỗi, và validation.