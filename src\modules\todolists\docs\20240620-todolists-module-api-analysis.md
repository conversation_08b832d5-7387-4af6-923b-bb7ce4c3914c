# Phân tích Module Todolists

## 1. T<PERSON>ng quan

Module Todolists l<PERSON> một phần quan trọng c<PERSON><PERSON> hệ thống, cho <PERSON>h<PERSON><PERSON> quản lý các công việ<PERSON> (todos), d<PERSON> (projects), và các tương tác liên quan như bình luậ<PERSON>, đ<PERSON><PERSON> kèm file, và cộng tác. Module này cung cấp các API để tạo, c<PERSON><PERSON> n<PERSON>, theo dõi và quản lý công việc và dự án.

## 2. Cấu trúc dữ liệu

### 2.1. Các Entity

Module Todolists bao gồm các entity chính sau:

#### 2.1.1. To<PERSON> (Công việc)

Entity `Todo` đại diện cho các công việc cần thực hiện:

```typescript
@Entity('todos')
export class Todo {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ type: 'varchar', length: 255, nullable: false })
  title: string;

  @Column({ type: 'text', nullable: true })
  description: string | null;

  @Column({ name: 'assignee_id', type: 'integer', nullable: false })
  assigneeId: number;

  @Column({
    type: 'varchar',
    length: 50,
    default: TodoStatus.PENDING,
    nullable: true,
  })
  status: TodoStatus | null;

  @Column({
    type: 'varchar',
    length: 20,
    default: TodoPriority.MEDIUM,
    nullable: true,
  })
  priority: TodoPriority | null;

  @Column({
    name: 'expected_stars',
    type: 'integer',
    default: 3,
    nullable: true,
  })
  expectedStars: number | null;

  @Column({ name: 'awarded_stars', type: 'integer', nullable: true })
  awardedStars: number | null;

  @Column({ name: 'created_by', type: 'integer', nullable: false })
  createdBy: number;

  @Column({ name: 'created_at', type: 'bigint', nullable: true })
  createdAt: number | null;

  @Column({ name: 'updated_at', type: 'bigint', nullable: true })
  updatedAt: number | null;

  @Column({ name: 'completed_at', type: 'bigint', nullable: true })
  completedAt: number | null;

  @Column({ name: 'category_id', type: 'integer', nullable: true })
  categoryId: number | null;

  @Column({ name: 'parent_id', type: 'integer', nullable: true })
  parentId: number | null;

  @Column({ name: 'tenant_id', type: 'integer', nullable: true })
  tenantId: number | null;
}
```

#### 2.1.2. Project (Dự án)

Entity `Project` đại diện cho các dự án:

```typescript
@Entity('projects')
export class Project {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ type: 'varchar', length: 255, nullable: false })
  title: string;

  @Column({ type: 'text', nullable: true })
  description: string | null;

  @Column({ name: 'owner_id', type: 'integer', nullable: false })
  ownerId: number;

  @Column({ name: 'created_by', type: 'integer', nullable: false })
  createdBy: number;

  @Column({ name: 'created_at', type: 'bigint', nullable: true })
  createdAt: number | null;

  @Column({ name: 'updated_at', type: 'bigint', nullable: true })
  updatedAt: number | null;

  @Column({ name: 'is_active', type: 'boolean', default: true, nullable: true })
  isActive: boolean | null;

  @Column({ name: 'tenant_id', type: 'integer', nullable: true })
  tenantId: number | null;
}
```

#### 2.1.3. ProjectMember (Thành viên dự án)

Entity `ProjectMember` đại diện cho các thành viên của dự án:

```typescript
@Entity('project_members')
export class ProjectMember {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: 'project_id', type: 'integer', nullable: true })
  projectId: number | null;

  @Column({ name: 'user_id', type: 'integer', nullable: true })
  userId: number | null;

  @Column({ type: 'varchar', length: 50, nullable: false })
  role: ProjectMemberRole;

  @Column({ name: 'created_at', type: 'bigint', nullable: true })
  createdAt: number | null;

  @Column({ name: 'updated_at', type: 'bigint', nullable: true })
  updatedAt: number | null;

  @Column({ name: 'tenant_id', type: 'integer', nullable: true })
  tenantId: number | null;
}
```

#### 2.1.4. TodoComment (Bình luận công việc)

Entity `TodoComment` đại diện cho các bình luận và sự kiện hệ thống trên công việc:

```typescript
@Entity('todo_comments')
export class TodoComment {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: 'todo_id', type: 'integer', nullable: true })
  todoId: number | null;

  @Column({ name: 'user_id', type: 'integer', nullable: true })
  userId: number | null;

  @Column({ name: 'content_html', type: 'text', nullable: false })
  contentHtml: string;

  @Column({ name: 'created_at', type: 'bigint', nullable: true })
  createdAt: number | null;

  @Column({ name: 'parent_id', type: 'integer', nullable: true })
  parentId: number | null;

  @Column({
    name: 'comment_type',
    type: 'varchar',
    length: 50,
    default: CommentType.NOTE,
    nullable: true,
  })
  commentType: CommentType | null;

  @Column({ name: 'tenant_id', type: 'integer', nullable: true })
  tenantId: number | null;

  @Column({ name: 'is_system_event', type: 'boolean', default: false })
  isSystemEvent: boolean;

  @Column({ name: 'event_data', type: 'jsonb', nullable: true })
  eventData: SystemEventData | null;

  @Column({ name: 'resources', type: 'jsonb', nullable: true })
  resources: Array<{ type: string; url: string; name?: string }> | null;

  @Column({ name: 'mentions', type: 'jsonb', nullable: true })
  mentions: Array<{ userId: number; username: string }> | null;
}
```

#### 2.1.5. TodoAttachment (Đính kèm công việc)

Entity `TodoAttachment` đại diện cho các file đính kèm của công việc:

```typescript
@Entity('todo_attachments')
export class TodoAttachment {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: 'todo_id', type: 'integer', nullable: false })
  todoId: number;

  @Column({ type: 'varchar', length: 255, nullable: false })
  filename: string;

  @Column({ type: 'varchar', length: 500, nullable: false })
  url: string;

  @Column({ name: 'content_type', type: 'varchar', length: 100, nullable: true })
  contentType: string | null;

  @Column({ type: 'bigint', nullable: true })
  size: number | null;

  @Column({ name: 'created_at', type: 'bigint', nullable: true })
  createdAt: number | null;

  @Column({ name: 'created_by', type: 'integer', nullable: true })
  createdBy: number | null;

  @Column({ name: 'tenant_id', type: 'integer', nullable: true })
  tenantId: number | null;
}
```

#### 2.1.6. TodoCollaborator (Cộng tác viên công việc)

Entity `TodoCollaborator` đại diện cho các cộng tác viên của công việc:

```typescript
@Entity('todo_collaborators')
export class TodoCollaborator {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: 'todo_id', type: 'integer', nullable: true })
  todoId: number | null;

  @Column({ name: 'user_id', type: 'integer', nullable: true })
  userId: number | null;

  @Column({ name: 'created_at', type: 'bigint', nullable: true })
  createdAt: number | null;

  @Column({ name: 'tenant_id', type: 'integer', nullable: true })
  tenantId: number | null;
}
```

#### 2.1.7. TodoTag (Gắn thẻ công việc)

Entity `TodoTag` đại diện cho các thẻ gắn với công việc:

```typescript
@Entity('todo_tags')
export class TodoTag {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: 'todo_id', type: 'integer', nullable: true })
  todoId: number | null;

  @Column({ name: 'labels_id', type: 'integer', nullable: true })
  labelsId: number | null;

  @Column({ name: 'created_at', type: 'bigint', nullable: true })
  createdAt: number | null;

  @Column({ name: 'tenant_id', type: 'integer', nullable: true })
  tenantId: number | null;
}
```

#### 2.1.8. TodoAppreciation (Đánh giá công việc)

Entity `TodoAppreciation` đại diện cho các đánh giá của công việc đã hoàn thành:

```typescript
@Entity('todo_appreciations')
export class TodoAppreciation {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: 'todo_id', type: 'integer', nullable: true })
  todoId: number | null;

  @Column({ name: 'user_id', type: 'integer', nullable: true })
  userId: number | null;

  @Column({ type: 'text', nullable: true })
  note: string | null;

  @Column({ name: 'created_at', type: 'bigint', nullable: true })
  createdAt: number | null;

  @Column({ name: 'tenant_id', type: 'integer', nullable: true })
  tenantId: number | null;
}
```

#### 2.1.9. TaskKr (Liên kết công việc với kết quả chính)

Entity `TaskKr` đại diện cho mối quan hệ giữa công việc và kết quả chính trong OKR:

```typescript
@Entity('task_kr')
export class TaskKr {
  @Column({ name: 'task_id', type: 'integer', primary: true, nullable: false })
  taskId: number;

  @Column({ name: 'kr_id', type: 'integer', primary: true, nullable: false })
  krId: number;

  @Column({ name: 'tenant_id', type: 'integer', nullable: true })
  tenantId: number | null;
}
```

### 2.2. Các Enum

Module Todolists sử dụng các enum sau:

#### 2.2.1. TodoStatus

```typescript
export enum TodoStatus {
  PENDING = 'pending',
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed',
  APPROVED = 'approved',
  REJECTED = 'rejected',
}
```

#### 2.2.2. TodoPriority

```typescript
export enum TodoPriority {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  URGENT = 'urgent',
}
```

#### 2.2.3. CommentType

```typescript
export enum CommentType {
  NOTE = 'note',
  QUESTION = 'question',
  SUGGESTION = 'suggestion',
  ISSUE = 'issue',
}
```

#### 2.2.4. SystemEventType

```typescript
export enum SystemEventType {
  STATUS_CHANGED = 'status_changed',
  COLLABORATOR_CHANGED = 'collaborator_changed',
  ASSIGNEE_CHANGED = 'assignee_changed',
}
```

#### 2.2.5. ProjectMemberRole

```typescript
export enum ProjectMemberRole {
  ADMIN = 'admin',
  MEMBER = 'member',
  VIEWER = 'viewer',
}
```

### 2.3. Các Interface

#### 2.3.1. SystemEventData

```typescript
export type SystemEventData =
  | {
      eventType: SystemEventType.STATUS_CHANGED;
      oldValue: string;
      newValue: string;
      actorId: number;
      changedAt: number;
    }
  | {
      eventType: SystemEventType.COLLABORATOR_CHANGED;
      action: 'added' | 'removed';
      userId: number;
      actorId: number;
      changedAt: number;
    }
  | {
      eventType: SystemEventType.ASSIGNEE_CHANGED;
      oldUserId: number | null;
      newUserId: number | null;
      actorId: number;
      changedAt: number;
    };
```

## 3. Mối quan hệ giữa các Entity

### 3.1. Quan hệ chính

1. **Todo - Project**: Một Project có nhiều Todo. Mối quan hệ này được thể hiện qua trường `categoryId` trong entity Todo.

2. **Todo - Todo**: Một Todo có thể có nhiều Todo con (subtasks). Mối quan hệ này được thể hiện qua trường `parentId` trong entity Todo.

3. **Project - ProjectMember**: Một Project có nhiều ProjectMember. Mối quan hệ này được thể hiện qua trường `projectId` trong entity ProjectMember.

4. **Todo - TodoComment**: Một Todo có nhiều TodoComment. Mối quan hệ này được thể hiện qua trường `todoId` trong entity TodoComment.

5. **Todo - TodoAttachment**: Một Todo có nhiều TodoAttachment. Mối quan hệ này được thể hiện qua trường `todoId` trong entity TodoAttachment.

6. **Todo - TodoCollaborator**: Một Todo có nhiều TodoCollaborator. Mối quan hệ này được thể hiện qua trường `todoId` trong entity TodoCollaborator.

7. **Todo - TodoTag**: Một Todo có nhiều TodoTag. Mối quan hệ này được thể hiện qua trường `todoId` trong entity TodoTag.

8. **Todo - TodoAppreciation**: Một Todo có nhiều TodoAppreciation. Mối quan hệ này được thể hiện qua trường `todoId` trong entity TodoAppreciation.

9. **Todo - TaskKr**: Một Todo có thể liên kết với nhiều KeyResult trong OKR. Mối quan hệ này được thể hiện qua entity TaskKr.

10. **TodoComment - TodoComment**: Một TodoComment có thể có nhiều TodoComment con (replies). Mối quan hệ này được thể hiện qua trường `parentId` trong entity TodoComment.

### 3.2. Quan hệ với các Entity bên ngoài

1. **User - Todo**: Một User có thể được giao nhiều Todo. Mối quan hệ này được thể hiện qua trường `assigneeId` trong entity Todo.

2. **User - Project**: Một User có thể sở hữu nhiều Project. Mối quan hệ này được thể hiện qua trường `ownerId` trong entity Project.

3. **User - ProjectMember**: Một User có thể là thành viên của nhiều Project. Mối quan hệ này được thể hiện qua trường `userId` trong entity ProjectMember.

4. **User - TodoComment**: Một User có thể tạo nhiều TodoComment. Mối quan hệ này được thể hiện qua trường `userId` trong entity TodoComment.

5. **User - TodoCollaborator**: Một User có thể cộng tác trên nhiều Todo. Mối quan hệ này được thể hiện qua trường `userId` trong entity TodoCollaborator.

6. **User - TodoAppreciation**: Một User có thể đánh giá nhiều Todo. Mối quan hệ này được thể hiện qua trường `userId` trong entity TodoAppreciation.

7. **KeyResult - TaskKr**: Một KeyResult trong OKR có thể liên kết với nhiều Todo. Mối quan hệ này được thể hiện qua entity TaskKr.

8. **Tenant - Tất cả các Entity**: Tất cả các entity đều có trường `tenantId` để phân biệt dữ liệu giữa các tenant khác nhau trong hệ thống multi-tenant.

## 4. Đề xuất API

Dựa trên cấu trúc dữ liệu, module Todolists nên cung cấp các API sau:

### 4.1. API Quản lý Dự án (Projects)

#### 4.1.1. Tạo dự án mới
- **Endpoint**: `POST /api/projects`
- **Quyền**: Người dùng đã xác thực
- **Request Body**: Thông tin dự án (tiêu đề, mô tả)
- **Response**: Thông tin dự án đã tạo

#### 4.1.2. Lấy danh sách dự án
- **Endpoint**: `GET /api/projects`
- **Quyền**: Người dùng đã xác thực
- **Query Params**: Phân trang, sắp xếp, lọc theo trạng thái, người sở hữu
- **Response**: Danh sách dự án

#### 4.1.3. Lấy chi tiết dự án
- **Endpoint**: `GET /api/projects/:id`
- **Quyền**: Thành viên dự án
- **Response**: Chi tiết dự án và danh sách thành viên

#### 4.1.4. Cập nhật dự án
- **Endpoint**: `PUT /api/projects/:id`
- **Quyền**: Chủ sở hữu dự án, Admin
- **Request Body**: Thông tin cập nhật
- **Response**: Thông tin dự án đã cập nhật

#### 4.1.5. Xóa dự án
- **Endpoint**: `DELETE /api/projects/:id`
- **Quyền**: Chủ sở hữu dự án, Admin
- **Response**: Thông báo xóa thành công

### 4.2. API Quản lý Thành viên Dự án (Project Members)

#### 4.2.1. Thêm thành viên vào dự án
- **Endpoint**: `POST /api/projects/:id/members`
- **Quyền**: Chủ sở hữu dự án, Admin
- **Request Body**: Thông tin thành viên (userId, role)
- **Response**: Thông tin thành viên đã thêm

#### 4.2.2. Lấy danh sách thành viên dự án
- **Endpoint**: `GET /api/projects/:id/members`
- **Quyền**: Thành viên dự án
- **Response**: Danh sách thành viên dự án

#### 4.2.3. Cập nhật vai trò thành viên
- **Endpoint**: `PUT /api/projects/:id/members/:memberId`
- **Quyền**: Chủ sở hữu dự án, Admin
- **Request Body**: Thông tin cập nhật (role)
- **Response**: Thông tin thành viên đã cập nhật

#### 4.2.4. Xóa thành viên khỏi dự án
- **Endpoint**: `DELETE /api/projects/:id/members/:memberId`
- **Quyền**: Chủ sở hữu dự án, Admin
- **Response**: Thông báo xóa thành công

### 4.3. API Quản lý Công việc (Todos)

#### 4.3.1. Tạo công việc mới
- **Endpoint**: `POST /api/todos`
- **Quyền**: Thành viên dự án
- **Request Body**: Thông tin công việc (tiêu đề, mô tả, người được giao, dự án, ưu tiên, v.v.)
- **Response**: Thông tin công việc đã tạo

#### 4.3.2. Lấy danh sách công việc
- **Endpoint**: `GET /api/todos`
- **Quyền**: Người dùng đã xác thực
- **Query Params**: Phân trang, sắp xếp, lọc theo trạng thái, dự án, người được giao, ưu tiên
- **Response**: Danh sách công việc

#### 4.3.3. Lấy chi tiết công việc
- **Endpoint**: `GET /api/todos/:id`
- **Quyền**: Người được giao, Cộng tác viên, Chủ sở hữu dự án
- **Response**: Chi tiết công việc, danh sách bình luận, đính kèm, cộng tác viên

#### 4.3.4. Cập nhật công việc
- **Endpoint**: `PUT /api/todos/:id`
- **Quyền**: Người được giao, Chủ sở hữu dự án, Admin
- **Request Body**: Thông tin cập nhật
- **Response**: Thông tin công việc đã cập nhật

#### 4.3.5. Xóa công việc
- **Endpoint**: `DELETE /api/todos/:id`
- **Quyền**: Chủ sở hữu dự án, Admin
- **Response**: Thông báo xóa thành công

#### 4.3.6. Cập nhật trạng thái công việc
- **Endpoint**: `PATCH /api/todos/:id/status`
- **Quyền**: Người được giao, Chủ sở hữu dự án, Admin
- **Request Body**: Trạng thái mới
- **Response**: Thông tin công việc đã cập nhật

#### 4.3.7. Tạo công việc con
- **Endpoint**: `POST /api/todos/:id/subtasks`
- **Quyền**: Người được giao, Chủ sở hữu dự án
- **Request Body**: Thông tin công việc con
- **Response**: Thông tin công việc con đã tạo

#### 4.3.8. Lấy danh sách công việc con
- **Endpoint**: `GET /api/todos/:id/subtasks`
- **Quyền**: Người được giao, Cộng tác viên, Chủ sở hữu dự án
- **Response**: Danh sách công việc con

### 4.4. API Quản lý Bình luận (Comments)

#### 4.4.1. Tạo bình luận mới
- **Endpoint**: `POST /api/todos/:id/comments`
- **Quyền**: Người được giao, Cộng tác viên, Chủ sở hữu dự án
- **Request Body**: Nội dung bình luận, loại bình luận, tài nguyên, mentions
- **Response**: Thông tin bình luận đã tạo

#### 4.4.2. Lấy danh sách bình luận của công việc
- **Endpoint**: `GET /api/todos/:id/comments`
- **Quyền**: Người được giao, Cộng tác viên, Chủ sở hữu dự án
- **Query Params**: Phân trang, sắp xếp theo thời gian
- **Response**: Danh sách bình luận

#### 4.4.3. Cập nhật bình luận
- **Endpoint**: `PUT /api/comments/:id`
- **Quyền**: Người tạo bình luận
- **Request Body**: Nội dung cập nhật
- **Response**: Thông tin bình luận đã cập nhật

#### 4.4.4. Xóa bình luận
- **Endpoint**: `DELETE /api/comments/:id`
- **Quyền**: Người tạo bình luận, Chủ sở hữu dự án, Admin
- **Response**: Thông báo xóa thành công

#### 4.4.5. Trả lời bình luận
- **Endpoint**: `POST /api/comments/:id/replies`
- **Quyền**: Người được giao, Cộng tác viên, Chủ sở hữu dự án
- **Request Body**: Nội dung trả lời
- **Response**: Thông tin trả lời đã tạo

### 4.5. API Quản lý Đính kèm (Attachments)

#### 4.5.1. Tải lên đính kèm mới
- **Endpoint**: `POST /api/todos/:id/attachments`
- **Quyền**: Người được giao, Cộng tác viên, Chủ sở hữu dự án
- **Request Body**: File đính kèm (multipart/form-data)
- **Response**: Thông tin đính kèm đã tải lên

#### 4.5.2. Lấy danh sách đính kèm của công việc
- **Endpoint**: `GET /api/todos/:id/attachments`
- **Quyền**: Người được giao, Cộng tác viên, Chủ sở hữu dự án
- **Response**: Danh sách đính kèm

#### 4.5.3. Xóa đính kèm
- **Endpoint**: `DELETE /api/attachments/:id`
- **Quyền**: Người tải lên, Người được giao, Chủ sở hữu dự án, Admin
- **Response**: Thông báo xóa thành công

### 4.6. API Quản lý Cộng tác viên (Collaborators)

#### 4.6.1. Thêm cộng tác viên vào công việc
- **Endpoint**: `POST /api/todos/:id/collaborators`
- **Quyền**: Người được giao, Chủ sở hữu dự án
- **Request Body**: Thông tin cộng tác viên (userId)
- **Response**: Thông tin cộng tác viên đã thêm

#### 4.6.2. Lấy danh sách cộng tác viên của công việc
- **Endpoint**: `GET /api/todos/:id/collaborators`
- **Quyền**: Người được giao, Cộng tác viên, Chủ sở hữu dự án
- **Response**: Danh sách cộng tác viên

#### 4.6.3. Xóa cộng tác viên khỏi công việc
- **Endpoint**: `DELETE /api/todos/:id/collaborators/:collaboratorId`
- **Quyền**: Người được giao, Chủ sở hữu dự án, Admin
- **Response**: Thông báo xóa thành công

### 4.7. API Quản lý Thẻ (Tags)

#### 4.7.1. Gắn thẻ vào công việc
- **Endpoint**: `POST /api/todos/:id/tags`
- **Quyền**: Người được giao, Cộng tác viên, Chủ sở hữu dự án
- **Request Body**: Thông tin thẻ (labelsId)
- **Response**: Thông tin thẻ đã gắn

#### 4.7.2. Lấy danh sách thẻ của công việc
- **Endpoint**: `GET /api/todos/:id/tags`
- **Quyền**: Người được giao, Cộng tác viên, Chủ sở hữu dự án
- **Response**: Danh sách thẻ

#### 4.7.3. Xóa thẻ khỏi công việc
- **Endpoint**: `DELETE /api/todos/:id/tags/:tagId`
- **Quyền**: Người được giao, Cộng tác viên, Chủ sở hữu dự án
- **Response**: Thông báo xóa thành công

### 4.8. API Quản lý Đánh giá (Appreciations)

#### 4.8.1. Tạo đánh giá cho công việc
- **Endpoint**: `POST /api/todos/:id/appreciations`
- **Quyền**: Người tạo công việc, Chủ sở hữu dự án
- **Request Body**: Thông tin đánh giá (note, stars)
- **Response**: Thông tin đánh giá đã tạo

#### 4.8.2. Lấy đánh giá của công việc
- **Endpoint**: `GET /api/todos/:id/appreciations`
- **Quyền**: Người được giao, Người tạo công việc, Chủ sở hữu dự án
- **Response**: Thông tin đánh giá

#### 4.8.3. Cập nhật đánh giá
- **Endpoint**: `PUT /api/appreciations/:id`
- **Quyền**: Người tạo đánh giá
- **Request Body**: Thông tin cập nhật
- **Response**: Thông tin đánh giá đã cập nhật

### 4.9. API Liên kết với OKR

#### 4.9.1. Liên kết công việc với kết quả chính
- **Endpoint**: `POST /api/todos/:id/key-results`
- **Quyền**: Người được giao, Chủ sở hữu dự án, Admin
- **Request Body**: Thông tin kết quả chính (krId)
- **Response**: Thông tin liên kết đã tạo

#### 4.9.2. Lấy danh sách kết quả chính liên kết với công việc
- **Endpoint**: `GET /api/todos/:id/key-results`
- **Quyền**: Người được giao, Cộng tác viên, Chủ sở hữu dự án
- **Response**: Danh sách kết quả chính

#### 4.9.3. Xóa liên kết với kết quả chính
- **Endpoint**: `DELETE /api/todos/:id/key-results/:krId`
- **Quyền**: Người được giao, Chủ sở hữu dự án, Admin
- **Response**: Thông báo xóa thành công

## 5. Đề xuất DTO

### 5.1. DTO cho Dự án (Projects)

#### 5.1.1. CreateProjectDto
```typescript
export class CreateProjectDto {
  @ApiProperty({
    description: 'Tiêu đề dự án',
    example: 'Dự án phát triển website',
  })
  @IsString()
  @IsNotEmpty()
  title: string;

  @ApiProperty({
    description: 'Mô tả chi tiết dự án',
    example: 'Dự án phát triển website cho công ty ABC',
    required: false,
  })
  @IsString()
  @IsOptional()
  description?: string;
}
```

#### 5.1.2. UpdateProjectDto
```typescript
export class UpdateProjectDto {
  @ApiProperty({
    description: 'Tiêu đề dự án',
    example: 'Dự án phát triển website (cập nhật)',
    required: false,
  })
  @IsString()
  @IsOptional()
  title?: string;

  @ApiProperty({
    description: 'Mô tả chi tiết dự án',
    example: 'Dự án phát triển website cho công ty ABC (cập nhật)',
    required: false,
  })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiProperty({
    description: 'Trạng thái hoạt động của dự án',
    example: true,
    required: false,
  })
  @IsBoolean()
  @IsOptional()
  isActive?: boolean;
}
```

### 5.2. DTO cho Thành viên Dự án (Project Members)

#### 5.2.1. AddProjectMemberDto
```typescript
export class AddProjectMemberDto {
  @ApiProperty({
    description: 'ID của người dùng',
    example: 1,
  })
  @IsNumber()
  userId: number;

  @ApiProperty({
    description: 'Vai trò trong dự án',
    enum: ProjectMemberRole,
    example: ProjectMemberRole.MEMBER,
  })
  @IsEnum(ProjectMemberRole)
  role: ProjectMemberRole;
}
```

#### 5.2.2. UpdateProjectMemberDto
```typescript
export class UpdateProjectMemberDto {
  @ApiProperty({
    description: 'Vai trò trong dự án',
    enum: ProjectMemberRole,
    example: ProjectMemberRole.ADMIN,
  })
  @IsEnum(ProjectMemberRole)
  role: ProjectMemberRole;
}
```

### 5.3. DTO cho Công việc (Todos)

#### 5.3.1. CreateTodoDto
```typescript
export class CreateTodoDto {
  @ApiProperty({
    description: 'Tiêu đề công việc',
    example: 'Thiết kế giao diện người dùng',
  })
  @IsString()
  @IsNotEmpty()
  title: string;

  @ApiProperty({
    description: 'Mô tả chi tiết công việc',
    example: 'Thiết kế giao diện người dùng cho trang chủ và trang sản phẩm',
    required: false,
  })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiProperty({
    description: 'ID của người được giao công việc',
    example: 1,
  })
  @IsNumber()
  assigneeId: number;

  @ApiProperty({
    description: 'ID của dự án',
    example: 1,
    required: false,
  })
  @IsNumber()
  @IsOptional()
  categoryId?: number;

  @ApiProperty({
    description: 'ID của công việc cha (nếu là công việc con)',
    example: 2,
    required: false,
  })
  @IsNumber()
  @IsOptional()
  parentId?: number;

  @ApiProperty({
    description: 'Mức độ ưu tiên của công việc',
    enum: TodoPriority,
    example: TodoPriority.MEDIUM,
    required: false,
  })
  @IsEnum(TodoPriority)
  @IsOptional()
  priority?: TodoPriority;

  @ApiProperty({
    description: 'Số sao kỳ vọng (1-5)',
    example: 3,
    required: false,
  })
  @IsNumber()
  @Min(1)
  @Max(5)
  @IsOptional()
  expectedStars?: number;
}
```

## 6. Kết luận

Module Todolists là một phần quan trọng của hệ thống, cho phép quản lý các công việc, dự án, và các tương tác liên quan. Dựa trên phân tích các entity và mối quan hệ giữa chúng, chúng tôi đã đề xuất một tập hợp các API và DTO để triển khai module này.

Các API được thiết kế để hỗ trợ đầy đủ các chức năng CRUD cho tất cả các entity chính, cũng như các chức năng đặc biệt như quản lý thành viên dự án, cộng tác viên, bình luận, đính kèm, và liên kết với OKR. Các DTO được thiết kế để đảm bảo tính nhất quán và dễ sử dụng cho cả frontend và backend.

Những điểm cần lưu ý khi triển khai module Todolists:

1. **Tính đa người dùng (Multi-tenant)**: Tất cả các entity đều có trường `tenantId` để hỗ trợ tính năng đa người dùng. Cần đảm bảo rằng mọi truy vấn đều có điều kiện lọc theo `tenantId`.

2. **Phân quyền**: Cần triển khai hệ thống phân quyền chi tiết để đảm bảo rằng người dùng chỉ có thể truy cập và chỉnh sửa dữ liệu mà họ được phép.

3. **Sự kiện hệ thống**: Cần triển khai logic để tạo các sự kiện hệ thống khi có thay đổi trạng thái, cộng tác viên, hoặc người được giao công việc.

4. **Liên kết với OKR**: Cần triển khai logic để liên kết công việc với kết quả chính trong OKR, đảm bảo rằng tiến độ công việc có thể ảnh hưởng đến tiến độ của kết quả chính.

5. **Xác thực dữ liệu**: Cần triển khai xác thực dữ liệu chi tiết để đảm bảo tính nhất quán của dữ liệu.

Để triển khai module này, cần thực hiện các bước sau:

1. Tạo các entity và enum đã được mô tả
2. Tạo các DTO cho các API
3. Tạo các repository để tương tác với database
4. Tạo các service để xử lý logic nghiệp vụ
5. Tạo các controller để xử lý các request từ client
6. Tạo các guard để kiểm soát quyền truy cập
7. Viết unit test và integration test

Với việc triển khai module Todolists, hệ thống sẽ có thể hỗ trợ đầy đủ quy trình quản lý công việc và dự án, giúp tổ chức theo dõi và quản lý công việc một cách hiệu quả.