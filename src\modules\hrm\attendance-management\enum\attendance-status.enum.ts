/**
 * Enum for attendance status
 */
export enum AttendanceStatus {
  /**
   * Employee is present and on time
   */
  PRESENT = 'present',

  /**
   * Employee is absent without notice
   */
  ABSENT = 'absent',

  /**
   * Employee arrived late
   */
  LATE = 'late',

  /**
   * Employee left early
   */
  EARLY_LEAVE = 'early_leave',

  /**
   * Employee is on approved leave
   */
  ON_LEAVE = 'on_leave',

  /**
   * Employee is on sick leave
   */
  SICK_LEAVE = 'sick_leave',

  /**
   * Employee is on vacation
   */
  VACATION = 'vacation',

  /**
   * Employee is on business trip
   */
  BUSINESS_TRIP = 'business_trip',

  /**
   * Employee is working from home
   */
  WORK_FROM_HOME = 'work_from_home',

  /**
   * Employee has a half-day attendance
   */
  HALF_DAY = 'half_day',

  /**
   * Attendance record is pending approval
   */
  PENDING = 'pending',

  /**
   * Attendance record was manually adjusted
   */
  ADJUSTED = 'adjusted',
}

/**
 * Helper function to get all attendance status values
 */
export function getAllAttendanceStatuses(): string[] {
  return Object.values(AttendanceStatus);
}

/**
 * Helper function to check if a status is valid
 */
export function isValidAttendanceStatus(status: string): boolean {
  return Object.values(AttendanceStatus).includes(status as AttendanceStatus);
}
