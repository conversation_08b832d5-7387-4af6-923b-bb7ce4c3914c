import { ApiProperty } from '@nestjs/swagger';
import { Exclude, Expose } from 'class-transformer';
import { UserStatus } from '../enum/user-status.enum';

/**
 * DTO cho phản hồi thông tin người dùng
 */
@Exclude()
export class UserResponseDto {
  @Expose()
  @ApiProperty({
    description: 'ID của người dùng',
    example: 1,
  })
  id: number;

  @Expose()
  @ApiProperty({
    description: 'Username của người dùng',
    example: 'johndoe',
  })
  username: string;

  @Expose()
  @ApiProperty({
    description: 'Email của người dùng',
    example: '<EMAIL>',
  })
  email: string;

  @Expose()
  @ApiProperty({
    description: 'Họ tên đầy đủ của người dùng',
    example: '<PERSON>',
    nullable: true,
  })
  fullName: string | null;

  @Expose()
  @ApiProperty({
    description: 'Tr<PERSON>ng thái tài khoản người dùng',
    enum: UserStatus,
    example: UserStatus.ACTIVE,
    nullable: true,
  })
  status: UserStatus | null;

  @Expose()
  @ApiProperty({
    description: 'Vị trí công việc của người dùng',
    example: 'Developer',
    nullable: true,
  })
  position: string | null;

  @Expose()
  @ApiProperty({
    description: 'Thời gian tạo tài khoản (timestamp)',
    example: 1672531200000,
    nullable: true,
  })
  createdAt: number | null;

  @Expose()
  @ApiProperty({
    description: 'URL ảnh đại diện của người dùng',
    example: 'https://example.com/avatar.jpg',
    nullable: true,
  })
  avatarUrl: string | null;

  constructor(partial: Partial<UserResponseDto>) {
    Object.assign(this, partial);
  }
}

/**
 * DTO cho phản hồi đăng nhập thành công
 */
export class UserLoginResponseDto {
  @ApiProperty({
    description: 'Token truy cập',
    example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
  })
  accessToken: string;

  @ApiProperty({
    description: 'Thông tin người dùng',
    type: UserResponseDto,
  })
  user: UserResponseDto;

  @ApiProperty({
    description: 'Danh sách quyền của người dùng',
    example: ['user:view', 'user:create', 'project:view'],
    type: [String],
  })
  permissions: string[];
}
