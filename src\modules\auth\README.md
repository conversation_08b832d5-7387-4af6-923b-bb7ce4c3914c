# Module Auth

## Tổng quan

Module Auth cung cấp các chức năng xác thực và phân quyền trong hệ thống. Module này quản lý việc đăng nh<PERSON>, đ<PERSON><PERSON> ký, <PERSON><PERSON><PERSON> thực token JWT và phân quyền truy cập cho các API.

## Cấu trúc module

```
auth/
├── controller/             # Controllers xử lý request xác thực
├── service/                # Services xử lý logic nghiệp vụ xác thực
├── guards/                 # Guards bảo vệ các endpoint
│   ├── jwt-auth.guard.ts   # Guard xác thực JWT
│   └── roles.guard.ts      # Guard kiểm tra quyền
├── decorators/             # Decorators tùy chỉnh
│   └── roles.decorator.ts  # Decorator đánh dấu quyền truy cập
└── auth.module.ts          # Module definition
```

## Chức năng chính

### X<PERSON><PERSON> thực người dùng
- <PERSON><PERSON><PERSON> nhập với email và mật khẩu
- Đăng ký tài khoản mới
- <PERSON><PERSON><PERSON> thực token JWT
- Quản lý phiên đăng nhập

### Phân quyền
- Kiểm tra quyền truy cập vào các endpoint
- Phân quyền dựa trên vai trò (admin, user, v.v.)

### Bảo mật
- Mã hóa mật khẩu
- Xác thực hai yếu tố (2FA)
- Quản lý token JWT

## Guards

### JwtAuthGuard
Guard này kiểm tra tính hợp lệ của JWT token trong header của request. Nếu token hợp lệ, request sẽ được cho phép tiếp tục, ngược lại sẽ trả về lỗi 401 Unauthorized.

### RolesGuard
Guard này kiểm tra quyền của người dùng dựa trên vai trò được định nghĩa trong decorator `@Roles()`. Nếu người dùng có quyền phù hợp, request sẽ được cho phép tiếp tục, ngược lại sẽ trả về lỗi 403 Forbidden.

## Decorators

### @Roles()
Decorator này được sử dụng để đánh dấu các endpoint yêu cầu quyền truy cập cụ thể. Ví dụ:

```typescript
@Roles('admin')
@Get('/admin/users')
getAllUsers() {
  // Chỉ admin mới có thể truy cập endpoint này
}
```

## API Endpoints

- `POST /auth/login` - Đăng nhập
- `POST /auth/register` - Đăng ký tài khoản mới
- `POST /auth/refresh-token` - Làm mới token
- `POST /auth/logout` - Đăng xuất
- `POST /auth/forgot-password` - Quên mật khẩu
- `POST /auth/reset-password` - Đặt lại mật khẩu

## Cách sử dụng

### Đăng nhập

```typescript
// Đăng nhập và lấy token
const { accessToken, user } = await authService.login({
  email: '<EMAIL>',
  password: 'password123'
});
```

## Hướng dẫn sử dụng Authentication

### Xác thực cho API người dùng

Đối với các API của người dùng, sử dụng `JwtAuthGuard` để bảo vệ các controller và sử dụng decorator `@CurrentUser` để lấy thông tin người dùng đang đăng nhập:

```typescript
import { Controller, Get, UseGuards } from '@nestjs/common';
import { JwtAuthGuard } from '@modules/auth/guards';
import { CurrentUser } from '@modules/auth/decorators';
import { JWTPayload } from '@modules/auth/types';

@Controller('users')
@UseGuards(JwtAuthGuard) // Áp dụng cho toàn bộ controller
export class UserController {
  @Get('profile')
  getProfile(@CurrentUser() user: JWTPayload) {
    // user chứa thông tin từ JWT payload
    // Ví dụ: user.id, user.email, user.roles, ...
    return user;
  }
}
```

### Xác thực cho API nhân viên/admin

Đối với các API của nhân viên/admin, sử dụng `JwtEmployeeGuard` để bảo vệ các controller và sử dụng decorator `@CurrentEmployee` để lấy thông tin nhân viên đang đăng nhập:

```typescript
import { Controller, Get, UseGuards } from '@nestjs/common';
import { JwtEmployeeGuard } from '@modules/auth/guards';
import { CurrentEmployee } from '@modules/auth/decorators';
import { JWTPayload } from '@modules/auth/types';

@Controller('admin/users')
@UseGuards(JwtEmployeeGuard) // Áp dụng cho toàn bộ controller
export class AdminUserController {
  @Get('dashboard')
  getDashboard(@CurrentEmployee() employee: JWTPayload) {
    // employee chứa thông tin từ JWT payload
    // Ví dụ: employee.id, employee.email, employee.roles, ...
    return employee;
  }
}
```

## Tài liệu chi tiết

- [Hướng dẫn sử dụng Authentication](./docs/authentication-guide.md) - Hướng dẫn chi tiết về cách sử dụng JwtAuthGuard, JwtEmployeeGuard và các decorator @CurrentUser, @CurrentEmployee
- [Luồng xác thực](./docs/auth.md) - Mô tả chi tiết các luồng xác thực trong hệ thống

## Liên kết với các module khác

- **User Module**: Quản lý thông tin người dùng
- **Email Module**: Gửi email xác thực và đặt lại mật khẩu
- **Employee Module**: Quản lý thông tin nhân viên