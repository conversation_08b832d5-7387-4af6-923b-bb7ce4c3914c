# Task ID: 3
# Title: Update OKRs Module - OkrCycle Repository
# Status: done
# Dependencies: 1
# Priority: high
# Description: Add tenantId filtering to all OkrCycle repository methods
# Details:
Update OkrCycleRepository methods:
- findAll(): Add tenantId to QueryBuilder
- findById(): Add tenantId to WHERE condition
- findActive(): Add tenantId filtering
- create(): Set tenantId on new entities
- update(): Add tenantId to WHERE condition
- delete(): Add tenantId to WHERE condition

# Test Strategy:
Unit tests for repository methods with tenantId validation
