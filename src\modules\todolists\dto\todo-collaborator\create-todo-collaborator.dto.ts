import { ApiProperty } from '@nestjs/swagger';
import { IsInt, IsNotEmpty, Min } from 'class-validator';

/**
 * DTO cho tạo mới cộng tác viên cho công việc
 */
export class CreateTodoCollaboratorDto {
  /**
   * ID của công việc
   * @example 1
   */
  @ApiProperty({
    description: 'ID của công việc',
    example: 1,
    required: true,
  })
  @IsNotEmpty({ message: 'ID công việc không được để trống' })
  @IsInt({ message: 'ID công việc phải là số nguyên' })
  @Min(1, { message: 'ID công việc phải lớn hơn 0' })
  todoId: number;

  /**
   * ID của người dùng được thêm làm cộng tác viên
   * @example 1
   */
  @ApiProperty({
    description: 'ID của người dùng được thêm làm cộng tác viên',
    example: 1,
    required: true,
  })
  @IsNotEmpty({ message: 'ID người dùng không được để trống' })
  @IsInt({ message: 'ID người dùng phải là số nguyên' })
  @Min(1, { message: 'ID người dùng phải lớn hơn 0' })
  userId: number;
}
