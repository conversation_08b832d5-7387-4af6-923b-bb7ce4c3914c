import { Injectable, Logger } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { ConfigService } from '@nestjs/config';
import { lastValueFrom } from 'rxjs';
import { AxiosError } from 'axios';
import { AppException, ErrorCode } from '@common/exceptions';
import { ZaloZnsMessage, ZaloZnsSendResult } from './zalo.interface';

@Injectable()
export class ZaloZnsService {
  private readonly logger = new Logger(ZaloZnsService.name);
  private readonly znsApiUrl =
    'https://business.openapi.zalo.me/message/template';
  private readonly appId: string;
  private readonly appSecret: string;

  constructor(
    private readonly httpService: HttpService,
    private readonly configService: ConfigService,
  ) {
    const appId = this.configService.get<string>('ZALO_APP_ID');
    const appSecret = this.configService.get<string>('ZALO_APP_SECRET');

    if (!appId || !appSecret) {
      throw new Error(
        'ZALO_APP_ID or ZALO_APP_SECRET is not defined in configuration',
      );
    }

    this.appId = appId;
    this.appSecret = appSecret;
  }

  /**
   * Gửi tin nhắn ZNS
   * @param accessToken Access token của Official Account
   * @param message Thông tin tin nhắn ZNS
   * @returns Kết quả gửi tin nhắn
   * @throws AppException nếu có lỗi xảy ra
   */
  async sendZnsMessage(
    accessToken: string,
    message: ZaloZnsMessage,
  ): Promise<ZaloZnsSendResult> {
    try {
      const data = {
        phone: message.phone,
        template_id: message.template_id,
        template_data: message.template_data,
        tracking_id: message.tracking_id || this.generateTrackingId(),
      };

      const headers = {
        'Content-Type': 'application/json',
        access_token: accessToken,
      };

      const response = await lastValueFrom(
        this.httpService.post<{
          error: number;
          message: string;
          data?: ZaloZnsSendResult;
        }>(this.znsApiUrl, data, { headers }),
      );

      if (response.data.error !== 0) {
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          `Lỗi khi gửi tin nhắn ZNS: ${response.data.message}`,
        );
      }

      if (!response.data.data) {
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          'Lỗi khi gửi tin nhắn ZNS: Không nhận được dữ liệu từ Zalo API',
        );
      }

      return response.data.data;
    } catch (error) {
      this.logger.error(
        `Error sending ZNS message: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      if (error instanceof AxiosError && error.response) {
        const response = error.response.data;
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          `Lỗi khi gửi tin nhắn ZNS: ${response.message || error.message}`,
        );
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        `Lỗi khi gửi tin nhắn ZNS: ${error.message}`,
      );
    }
  }

  /**
   * Kiểm tra trạng thái tin nhắn ZNS
   * @param accessToken Access token của Official Account
   * @param messageId ID của tin nhắn
   * @returns Trạng thái tin nhắn
   * @throws AppException nếu có lỗi xảy ra
   */
  async checkZnsMessageStatus(
    accessToken: string,
    messageId: string,
  ): Promise<{ message_id: string; status: string; delivered_time?: number }> {
    try {
      const url = 'https://business.openapi.zalo.me/message/status';
      const params = { message_id: messageId };
      const headers = {
        access_token: accessToken,
      };

      const response = await lastValueFrom(
        this.httpService.get<{
          error: number;
          message: string;
          data?: {
            message_id: string;
            status: string;
            delivered_time?: number;
          };
        }>(url, { params, headers }),
      );

      if (response.data.error !== 0) {
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          `Lỗi khi kiểm tra trạng thái tin nhắn ZNS: ${response.data.message}`,
        );
      }

      if (!response.data.data) {
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          'Lỗi khi kiểm tra trạng thái tin nhắn ZNS: Không nhận được dữ liệu từ Zalo API',
        );
      }

      return response.data.data;
    } catch (error) {
      this.logger.error(
        `Error checking ZNS message status: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      if (error instanceof AxiosError && error.response) {
        const response = error.response.data;
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          `Lỗi khi kiểm tra trạng thái tin nhắn ZNS: ${response.message || error.message}`,
        );
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        `Lỗi khi kiểm tra trạng thái tin nhắn ZNS: ${error.message}`,
      );
    }
  }

  /**
   * Lấy danh sách template ZNS
   * @param accessToken Access token của Official Account
   * @returns Danh sách template
   * @throws AppException nếu có lỗi xảy ra
   */
  async getZnsTemplates(
    accessToken: string,
  ): Promise<{ template_id: string; template_name: string; status: string }[]> {
    try {
      const url = 'https://business.openapi.zalo.me/template/all';
      const headers = {
        access_token: accessToken,
      };

      const response = await lastValueFrom(
        this.httpService.get<{
          error: number;
          message: string;
          data?: {
            template_id: string;
            template_name: string;
            status: string;
          }[];
        }>(url, { headers }),
      );

      if (response.data.error !== 0) {
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          `Lỗi khi lấy danh sách template ZNS: ${response.data.message}`,
        );
      }

      if (!response.data.data) {
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          'Lỗi khi lấy danh sách template ZNS: Không nhận được dữ liệu từ Zalo API',
        );
      }

      return response.data.data;
    } catch (error) {
      this.logger.error(
        `Error getting ZNS templates: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      if (error instanceof AxiosError && error.response) {
        const response = error.response.data;
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          `Lỗi khi lấy danh sách template ZNS: ${response.message || error.message}`,
        );
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        `Lỗi khi lấy danh sách template ZNS: ${error.message}`,
      );
    }
  }

  /**
   * Lấy chi tiết template ZNS
   * @param accessToken Access token của Official Account
   * @param templateId ID của template
   * @returns Chi tiết template
   * @throws AppException nếu có lỗi xảy ra
   */
  async getZnsTemplateDetail(
    accessToken: string,
    templateId: string,
  ): Promise<{
    template_id: string;
    template_name: string;
    status: string;
    template_content: string;
    params: { key: string; label: string; type: string }[];
  }> {
    try {
      const url = 'https://business.openapi.zalo.me/template/info';
      const params = { template_id: templateId };
      const headers = {
        access_token: accessToken,
      };

      const response = await lastValueFrom(
        this.httpService.get<{
          error: number;
          message: string;
          data?: {
            template_id: string;
            template_name: string;
            status: string;
            template_content: string;
            params: { key: string; label: string; type: string }[];
          };
        }>(url, { params, headers }),
      );

      if (response.data.error !== 0) {
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          `Lỗi khi lấy chi tiết template ZNS: ${response.data.message}`,
        );
      }

      if (!response.data.data) {
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          'Lỗi khi lấy chi tiết template ZNS: Không nhận được dữ liệu từ Zalo API',
        );
      }

      return response.data.data;
    } catch (error) {
      this.logger.error(
        `Error getting ZNS template detail: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      if (error instanceof AxiosError && error.response) {
        const response = error.response.data;
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          `Lỗi khi lấy chi tiết template ZNS: ${response.message || error.message}`,
        );
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        `Lỗi khi lấy chi tiết template ZNS: ${error.message}`,
      );
    }
  }

  /**
   * Tạo ID giao dịch ngẫu nhiên
   * @returns ID giao dịch
   */
  private generateTrackingId(): string {
    return `zns_${Date.now()}_${Math.random().toString(36).substring(2, 10)}`;
  }
}
