# Task ID: 35
# Title: Update DepartmentController with TenantId Extraction
# Status: done
# Dependencies: 32
# Priority: medium
# Description: Modify DepartmentController to use @CurrentUser() decorator and extract tenantId from JWT payload for all endpoints.
# Details:
Update src/modules/hrm/org-units/controllers/department.controller.ts to add @CurrentUser() decorator to all endpoints and extract tenantId using Number(user.tenantId) pattern.

# Test Strategy:
Test all department endpoints to ensure they properly extract and use tenantId.
