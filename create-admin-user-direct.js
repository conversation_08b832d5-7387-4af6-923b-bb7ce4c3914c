// Script để tạo user admin trực tiếp trong database
const { Client } = require('pg');
const bcrypt = require('bcrypt');

// Cấu hình database - từ file .env
const dbConfig = {
  host: 'pub-ai-erp-yzkr13ma-1099002.dbaas.bfcplatform.vn',
  port: 5432,
  database: 'postgres',
  user: 'root',
  password: 'dFmTCcoF8xZn0H21uBrZZrWZ0xI5OULiUA8i',
  ssl: {
    rejectUnauthorized: false
  }
};

async function createAdminUser() {
  const client = new Client(dbConfig);
  
  try {
    await client.connect();
    console.log('✅ Connected to database');

    // Hash password
    const hashedPassword = await bcrypt.hash('Admin@123', 10);
    console.log('✅ Password hashed');

    // Tạo company account trước (nếu chưa có)
    const companyInsertQuery = `
      INSERT INTO company_accounts (
        id, company_name, subdomain, tax_code, company_email, password, 
        phone_number, address, status, created_at, updated_at
      ) VALUES (
        1, 'Công ty TNHH ABC', 'abc', '0*********', '<EMAIL>', $1,
        '0*********', '123 Đường ABC, Quận 1, TP.HCM', 'ACTIVE', $2, $2
      ) ON CONFLICT (id) DO NOTHING;
    `;
    
    const now = Date.now();
    await client.query(companyInsertQuery, [hashedPassword, now]);
    console.log('✅ Company account created/exists');

    // Xóa user cũ nếu có và tạo lại
    await client.query(`DELETE FROM users WHERE email = '<EMAIL>';`);

    // Tạo user admin mới (không có username column)
    const userInsertQuery = `
      INSERT INTO users (
        email, password, full_name, department_id, status,
        position, created_at, address, phone_number, birth_date, gender,
        avatar_url, id_card_number, id_card_issue_date, id_card_issue_place,
        bank_account_number, bank_name, tax_code, insurance_number,
        user_type, tenant_id
      ) VALUES (
        '<EMAIL>', $1, 'Nguyễn Văn A', 1, 'active',
        'Quản trị viên', $2, '123 Đường ABC, Quận 1, TP.HCM', '0*********',
        '1990-01-01', 'Nam', null, '*********', '2010-01-01', 'TP.HCM',
        '*********0', 'Vietcombank', '*********', '*********',
        'FULL_TIME', 1
      );
    `;

    await client.query(userInsertQuery, [hashedPassword, now]);
    console.log('✅ Admin user created (without username field)');

    // Tạo một số nhân viên test
    const employees = [
      {
        employeeCode: 'REDAI1',
        employeeName: 'Nguyễn Văn A',
        status: 'active',
        hireDate: '2024-01-15'
      },
      {
        employeeCode: 'REDAI2', 
        employeeName: 'Trần Thị B',
        status: 'active',
        hireDate: '2024-02-20'
      },
      {
        employeeCode: 'REDAI3',
        employeeName: 'Lê Văn C',
        status: 'probation',
        hireDate: new Date().toISOString().split('T')[0] // Hôm nay
      },
      {
        employeeCode: 'REDAI4',
        employeeName: 'Phạm Thị D',
        status: 'inactive',
        hireDate: '2024-03-10'
      },
      {
        employeeCode: 'REDAI5',
        employeeName: 'Hoàng Văn E',
        status: 'probation',
        hireDate: new Date().toISOString().split('T')[0] // Hôm nay
      }
    ];

    for (const emp of employees) {
      const employeeInsertQuery = `
        INSERT INTO employees (
          employee_code, employee_name, status, hire_date, 
          created_at, updated_at, created_by, updated_by, tenant_id
        ) VALUES (
          $1, $2, $3, $4, $5, $5, 1, 1, 1
        ) ON CONFLICT (employee_code, tenant_id) DO NOTHING;
      `;
      
      await client.query(employeeInsertQuery, [
        emp.employeeCode, emp.employeeName, emp.status, emp.hireDate, now
      ]);
      console.log(`✅ Employee created: ${emp.employeeCode} - ${emp.employeeName}`);
    }

    console.log('\n🎉 Tất cả dữ liệu test đã được tạo thành công!');
    console.log('📋 Thông tin đăng nhập:');
    console.log('   Username: admin');
    console.log('   Password: Admin@123');
    console.log('   Email: <EMAIL>');
    console.log('\n💡 Bây giờ bạn có thể chạy test-employee-overview-api.js');

  } catch (error) {
    console.error('❌ Error:', error.message);
    console.error('💡 Hãy kiểm tra cấu hình database trong script này');
  } finally {
    await client.end();
    console.log('✅ Database connection closed');
  }
}

// Chạy script
createAdminUser().catch(console.error);
