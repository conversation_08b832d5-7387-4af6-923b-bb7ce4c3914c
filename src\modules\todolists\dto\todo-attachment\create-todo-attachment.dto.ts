import { ApiProperty } from '@nestjs/swagger';
import {
  IsInt,
  IsNotEmpty,
  IsOptional,
  IsString,
  Min,
  MaxLength,
} from 'class-validator';

/**
 * DTO cho tạo mới tệp đính kèm cho công việc
 */
export class CreateTodoAttachmentDto {
  /**
   * ID của công việc
   * @example 1
   */
  @ApiProperty({
    description: 'ID của công việc',
    example: 1,
    required: true,
  })
  @IsNotEmpty({ message: 'ID công việc không được để trống' })
  @IsInt({ message: 'ID công việc phải là số nguyên' })
  @Min(1, { message: 'ID công việc phải lớn hơn 0' })
  todoId: number;

  /**
   * Tên tệp đính kèm
   * @example "tài liệu dự án.pdf"
   */
  @ApiProperty({
    description: 'Tên tệp đính kèm',
    example: 'tài liệu dự án.pdf',
    required: true,
  })
  @IsNotEmpty({ message: 'Tên tệp không được để trống' })
  @IsString({ message: 'Tên tệp phải là chuỗi' })
  @MaxLength(255, { message: 'Tên tệp không được vượt quá 255 ký tự' })
  filename: string;

  /**
   * URL của tệp đính kèm
   * @example "https://example.com/files/document.pdf"
   */
  @ApiProperty({
    description: 'URL của tệp đính kèm',
    example: 'https://example.com/files/document.pdf',
    required: true,
  })
  @IsNotEmpty({ message: 'URL không được để trống' })
  @IsString({ message: 'URL phải là chuỗi' })
  @MaxLength(500, { message: 'URL không được vượt quá 500 ký tự' })
  url: string;

  /**
   * Loại nội dung của tệp (MIME type)
   * @example "application/pdf"
   */
  @ApiProperty({
    description: 'Loại nội dung của tệp (MIME type)',
    example: 'application/pdf',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Loại nội dung phải là chuỗi' })
  @MaxLength(100, { message: 'Loại nội dung không được vượt quá 100 ký tự' })
  contentType?: string;

  /**
   * Kích thước tệp (byte)
   * @example 1024000
   */
  @ApiProperty({
    description: 'Kích thước tệp (byte)',
    example: 1024000,
    required: false,
  })
  @IsOptional()
  @IsInt({ message: 'Kích thước phải là số nguyên' })
  @Min(0, { message: 'Kích thước không được âm' })
  size?: number;
}
