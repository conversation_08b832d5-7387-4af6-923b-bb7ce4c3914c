import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsOptional, IsInt, IsDate } from 'class-validator';
import { Type } from 'class-transformer';
import { QueryDto } from '@/common/dto/query.dto';
import { ContractStatus } from '../enum/contract-status.enum';
import { ContractType } from '../enum/contract-type.enum';

/**
 * DTO for querying contracts
 */
export class ContractQueryDto extends QueryDto {
  /**
   * Filter by employee ID
   * @example 1
   */
  @ApiProperty({
    required: false,
    description: 'Filter by employee ID',
    example: 1,
  })
  @IsOptional()
  @IsInt()
  @Type(() => Number)
  employeeId?: number;

  /**
   * Filter by contract status
   * @example "active"
   */
  @ApiProperty({
    required: false,
    description: 'Filter by contract status',
    enum: ContractStatus,
    example: ContractStatus.ACTIVE,
  })
  @IsOptional()
  @IsEnum(ContractStatus)
  status?: ContractStatus;

  /**
   * Filter by contract type
   * @example "definite"
   */
  @ApiProperty({
    required: false,
    description: 'Filter by contract type',
    enum: ContractType,
    example: ContractType.DEFINITE,
  })
  @IsOptional()
  @IsEnum(ContractType)
  contractType?: ContractType;

  /**
   * Filter by start date (from)
   * @example "2023-01-01"
   */
  @ApiProperty({
    required: false,
    description: 'Filter by start date (from)',
    example: '2023-01-01',
  })
  @IsOptional()
  @IsDate()
  @Type(() => Date)
  startDateFrom?: Date;

  /**
   * Filter by start date (to)
   * @example "2023-12-31"
   */
  @ApiProperty({
    required: false,
    description: 'Filter by start date (to)',
    example: '2023-12-31',
  })
  @IsOptional()
  @IsDate()
  @Type(() => Date)
  startDateTo?: Date;

  /**
   * Filter by end date (from)
   * @example "2023-01-01"
   */
  @ApiProperty({
    required: false,
    description: 'Filter by end date (from)',
    example: '2023-01-01',
  })
  @IsOptional()
  @IsDate()
  @Type(() => Date)
  endDateFrom?: Date;

  /**
   * Filter by end date (to)
   * @example "2023-12-31"
   */
  @ApiProperty({
    required: false,
    description: 'Filter by end date (to)',
    example: '2023-12-31',
  })
  @IsOptional()
  @IsDate()
  @Type(() => Date)
  endDateTo?: Date;
}
