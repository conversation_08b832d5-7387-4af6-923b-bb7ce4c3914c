import { Entity, PrimaryGeneratedColumn, Column, Check } from 'typeorm';
import { KeyResultStatus } from '../enum/key-result-status.enum';
import { CheckInFrequency } from '../enum/check-in-frequency.enum';

/**
 * Entity representing key results that measure the success of objectives
 */
@Entity('key_results')
@Check(`"target_value" <> "start_value"`)
export class KeyResult {
  /**
   * Unique identifier for the key result
   */
  @PrimaryGeneratedColumn()
  id: number;

  /**
   * ID of the objective this key result belongs to
   */
  @Column({ name: 'objective_id', type: 'integer', nullable: true })
  objectiveId: number | null;

  /**
   * Key result title
   */
  @Column({ type: 'varchar', length: 255, nullable: false })
  title: string;

  /**
   * Detailed description of the key result
   */
  @Column({ type: 'text', nullable: true })
  description: string | null;

  /**
   * Target value of the key result
   */
  @Column({
    name: 'target_value',
    type: 'numeric',
    precision: 18,
    scale: 2,
    nullable: false,
  })
  targetValue: number;

  /**
   * Current value of the key result
   */
  @Column({
    name: 'current_value',
    type: 'numeric',
    precision: 18,
    scale: 2,
    default: 0,
    nullable: true,
  })
  currentValue: number | null;

  /**
   * Initial value of the key result
   */
  @Column({
    name: 'start_value',
    type: 'numeric',
    precision: 18,
    scale: 2,
    default: 0,
    nullable: true,
  })
  startValue: number | null;

  /**
   * Unit of measurement for the key result (%, VND, count, etc.)
   */
  @Column({ type: 'varchar', length: 50, nullable: true })
  unit: string | null;

  /**
   * Display format for the key result value (number, percentage, currency, boolean)
   */
  @Column({ type: 'varchar', length: 50, default: 'number', nullable: true })
  format: string | null;

  /**
   * Key result completion progress (percentage)
   */
  @Column({ type: 'integer', default: 0, nullable: true })
  progress: number | null;

  /**
   * Key result status (active, completed, at-risk, behind)
   */
  @Column({
    type: 'enum',
    enum: KeyResultStatus,
    default: KeyResultStatus.ACTIVE,
    nullable: true,
  })
  status: KeyResultStatus | null;

  /**
   * Method used to measure the key result
   */
  @Column({
    name: 'measurement_method',
    type: 'varchar',
    length: 100,
    nullable: true,
  })
  measurementMethod: string | null;

  /**
   * Weight of the key result within its objective (0-100)
   */
  @Column({ type: 'integer', default: 100, nullable: true })
  weight: number | null;

  /**
   * Frequency of key result updates (daily, weekly, monthly)
   */
  @Column({
    name: 'check_in_frequency',
    type: 'enum',
    enum: CheckInFrequency,
    default: CheckInFrequency.WEEKLY,
    nullable: true,
  })
  checkInFrequency: CheckInFrequency | null;

  /**
   * Creation timestamp (in milliseconds)
   */
  @Column({ name: 'created_at', type: 'bigint', nullable: true })
  createdAt: number | null;

  /**
   * Last update timestamp (in milliseconds)
   */
  @Column({ name: 'updated_at', type: 'bigint', nullable: true })
  updatedAt: number | null;

  /**
   * ID of the company/organization that owns this record
   */
  @Column({ name: 'tenant_id', type: 'integer', nullable: true })
  tenantId: number | null;
}
