import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsInt } from 'class-validator';
import { Type } from 'class-transformer';

/**
 * DTO for assigning employee to department
 */
export class AssignDepartmentDto {
  /**
   * Department ID to assign
   * @example 1
   */
  @ApiProperty({ description: 'Department ID to assign', example: 1 })
  @IsNotEmpty()
  @IsInt()
  @Type(() => Number)
  departmentId: number;
}
