import { Test, TestingModule } from '@nestjs/testing';
import { TypeOrmModule } from '@nestjs/typeorm';
import { DataSource } from 'typeorm';
import { Module } from '@nestjs/common';

// Create a simple entity for testing
class TestEntity {
  id: number;
  name: string;
}

// Create a mock database module using SQLite in-memory
@Module({
  imports: [
    TypeOrmModule.forRoot({
      type: 'sqlite',
      database: ':memory:',
      entities: [TestEntity],
      synchronize: true,
    }),
  ],
})
class MockDatabaseModule {}

describe('DatabaseModule with SQLite', () => {
  let module: TestingModule;
  let dataSource: DataSource;

  beforeEach(async () => {
    // Create a testing module with SQLite in-memory database
    module = await Test.createTestingModule({
      imports: [
        TypeOrmModule.forRoot({
          type: 'sqlite',
          database: ':memory:',
          entities: [TestEntity],
          synchronize: true,
        }),
      ],
    }).compile();

    // Get the DataSource instance
    dataSource = module.get<DataSource>(DataSource);
  });

  afterEach(async () => {
    // Close the connection after each test
    if (dataSource && dataSource.isInitialized) {
      await dataSource.destroy();
    }
    await module.close();
  });

  it('should be defined', () => {
    expect(module).toBeDefined();
    expect(dataSource).toBeDefined();
  });

  it('should use SQLite in-memory database', () => {
    // Verify that the DataSource was configured correctly
    expect(dataSource.options.type).toBe('sqlite');
    expect(dataSource.options.database).toBe(':memory:');
  });

  it('should have entities configured correctly', () => {
    // Check if entities are configured
    const options = dataSource.options as any;

    // Check if synchronize is set
    expect(options.synchronize).toBe(true);

    // Check if entities are configured
    expect(options.entities).toBeDefined();
    expect(options.entities).toContain(TestEntity);
  });

  it('should be able to connect to the database', async () => {
    // Check if the connection is established
    expect(dataSource.isInitialized).toBe(true);

    // Try to execute a simple query
    const result = await dataSource.query('SELECT 1 as value');
    expect(result).toBeDefined();
    expect(result[0].value).toBe(1);
  });

  it('should be able to create a table', async () => {
    // Create a test table
    await dataSource.query(`
      CREATE TABLE IF NOT EXISTS test_table (
        id INTEGER PRIMARY KEY,
        name TEXT
      )
    `);

    // Insert a test record
    await dataSource.query(`
      INSERT INTO test_table (id, name) VALUES (1, 'Test')
    `);

    // Query the test record
    const result = await dataSource.query('SELECT * FROM test_table');
    expect(result).toBeDefined();
    expect(result.length).toBe(1);
    expect(result[0].id).toBe(1);
    expect(result[0].name).toBe('Test');
  });
});
