/**
 * Interface định nghĩa cấu trúc phản hồi từ Telegram API
 */
export interface TelegramResponse<T> {
  ok: boolean;
  result: T;
  description?: string;
  error_code?: number;
}

/**
 * Interface định nghĩa cấu trúc của một user Telegram
 */
export interface TelegramUser {
  id: number;
  is_bot: boolean;
  first_name: string;
  last_name?: string;
  username?: string;
  language_code?: string;
}

/**
 * Interface định nghĩa cấu trúc của một chat Telegram
 */
export interface TelegramChat {
  id: number;
  type: 'private' | 'group' | 'supergroup' | 'channel';
  title?: string;
  username?: string;
  first_name?: string;
  last_name?: string;
}

/**
 * Interface định nghĩa cấu trúc của một message Telegram
 */
export interface TelegramMessage {
  message_id: number;
  from?: TelegramUser;
  chat: TelegramChat;
  date: number;
  text?: string;
  photo?: TelegramPhotoSize[];
  document?: TelegramDocument;
  video?: TelegramVideo;
  audio?: TelegramAudio;
  voice?: TelegramVoice;
  caption?: string;
  reply_to_message?: TelegramMessage;
  entities?: TelegramMessageEntity[];
}

/**
 * Interface định nghĩa cấu trúc của một entity trong message Telegram
 */
export interface TelegramMessageEntity {
  type:
    | 'mention'
    | 'hashtag'
    | 'cashtag'
    | 'bot_command'
    | 'url'
    | 'email'
    | 'phone_number'
    | 'bold'
    | 'italic'
    | 'underline'
    | 'strikethrough'
    | 'code'
    | 'pre'
    | 'text_link'
    | 'text_mention';
  offset: number;
  length: number;
  url?: string;
  user?: TelegramUser;
}

/**
 * Interface định nghĩa cấu trúc của một kích thước ảnh Telegram
 */
export interface TelegramPhotoSize {
  file_id: string;
  file_unique_id: string;
  width: number;
  height: number;
  file_size?: number;
}

/**
 * Interface định nghĩa cấu trúc của một document Telegram
 */
export interface TelegramDocument {
  file_id: string;
  file_unique_id: string;
  file_name?: string;
  mime_type?: string;
  file_size?: number;
}

/**
 * Interface định nghĩa cấu trúc của một video Telegram
 */
export interface TelegramVideo {
  file_id: string;
  file_unique_id: string;
  width: number;
  height: number;
  duration: number;
  mime_type?: string;
  file_size?: number;
}

/**
 * Interface định nghĩa cấu trúc của một audio Telegram
 */
export interface TelegramAudio {
  file_id: string;
  file_unique_id: string;
  duration: number;
  performer?: string;
  title?: string;
  mime_type?: string;
  file_size?: number;
}

/**
 * Interface định nghĩa cấu trúc của một voice Telegram
 */
export interface TelegramVoice {
  file_id: string;
  file_unique_id: string;
  duration: number;
  mime_type?: string;
  file_size?: number;
}

/**
 * Interface định nghĩa cấu trúc của một update Telegram
 */
export interface TelegramUpdate {
  update_id: number;
  message?: TelegramMessage;
  edited_message?: TelegramMessage;
  channel_post?: TelegramMessage;
  edited_channel_post?: TelegramMessage;
  callback_query?: TelegramCallbackQuery;
}

/**
 * Interface định nghĩa cấu trúc của một callback query Telegram
 */
export interface TelegramCallbackQuery {
  id: string;
  from: TelegramUser;
  message?: TelegramMessage;
  inline_message_id?: string;
  chat_instance: string;
  data?: string;
  game_short_name?: string;
}

/**
 * Interface định nghĩa cấu trúc của một webhook info Telegram
 */
export interface TelegramWebhookInfo {
  url: string;
  has_custom_certificate: boolean;
  pending_update_count: number;
  last_error_date?: number;
  last_error_message?: string;
  max_connections?: number;
  allowed_updates?: string[];
}

/**
 * Interface định nghĩa cấu trúc của một file Telegram
 */
export interface TelegramFile {
  file_id: string;
  file_unique_id: string;
  file_size?: number;
  file_path?: string;
}

/**
 * Interface định nghĩa cấu trúc của một bot command Telegram
 */
export interface TelegramBotCommand {
  command: string;
  description: string;
}

/**
 * Interface định nghĩa cấu trúc của một inline keyboard button Telegram
 */
export interface TelegramInlineKeyboardButton {
  text: string;
  url?: string;
  callback_data?: string;
  web_app?: { url: string };
  login_url?: {
    url: string;
    forward_text?: string;
    bot_username?: string;
    request_write_access?: boolean;
  };
  switch_inline_query?: string;
  switch_inline_query_current_chat?: string;
  callback_game?: {};
  pay?: boolean;
}

/**
 * Interface định nghĩa cấu trúc của một inline keyboard markup Telegram
 */
export interface TelegramInlineKeyboardMarkup {
  inline_keyboard: TelegramInlineKeyboardButton[][];
}

/**
 * Interface định nghĩa cấu trúc của một reply keyboard button Telegram
 */
export interface TelegramKeyboardButton {
  text: string;
  request_contact?: boolean;
  request_location?: boolean;
  request_poll?: { type: 'quiz' | 'regular' };
  web_app?: { url: string };
}

/**
 * Interface định nghĩa cấu trúc của một reply keyboard markup Telegram
 */
export interface TelegramReplyKeyboardMarkup {
  keyboard: TelegramKeyboardButton[][];
  resize_keyboard?: boolean;
  one_time_keyboard?: boolean;
  input_field_placeholder?: string;
  selective?: boolean;
}

/**
 * Interface định nghĩa cấu trúc của một reply keyboard remove Telegram
 */
export interface TelegramReplyKeyboardRemove {
  remove_keyboard: true;
  selective?: boolean;
}

/**
 * Interface định nghĩa cấu trúc của một force reply Telegram
 */
export interface TelegramForceReply {
  force_reply: true;
  input_field_placeholder?: string;
  selective?: boolean;
}

/**
 * Type định nghĩa các loại reply markup Telegram
 */
export type TelegramReplyMarkup =
  | TelegramInlineKeyboardMarkup
  | TelegramReplyKeyboardMarkup
  | TelegramReplyKeyboardRemove
  | TelegramForceReply;

/**
 * Interface định nghĩa cấu trúc của một send message options Telegram
 */
export interface TelegramSendMessageOptions {
  parse_mode?: 'Markdown' | 'MarkdownV2' | 'HTML';
  entities?: TelegramMessageEntity[];
  disable_web_page_preview?: boolean;
  disable_notification?: boolean;
  protect_content?: boolean;
  reply_to_message_id?: number;
  allow_sending_without_reply?: boolean;
  reply_markup?: TelegramReplyMarkup;
}

/**
 * Interface định nghĩa cấu trúc của một send photo options Telegram
 */
export interface TelegramSendPhotoOptions {
  caption?: string;
  parse_mode?: 'Markdown' | 'MarkdownV2' | 'HTML';
  caption_entities?: TelegramMessageEntity[];
  disable_notification?: boolean;
  protect_content?: boolean;
  reply_to_message_id?: number;
  allow_sending_without_reply?: boolean;
  reply_markup?: TelegramReplyMarkup;
}

/**
 * Interface định nghĩa cấu trúc của một send document options Telegram
 */
export interface TelegramSendDocumentOptions {
  thumb?: string;
  caption?: string;
  parse_mode?: 'Markdown' | 'MarkdownV2' | 'HTML';
  caption_entities?: TelegramMessageEntity[];
  disable_content_type_detection?: boolean;
  disable_notification?: boolean;
  protect_content?: boolean;
  reply_to_message_id?: number;
  allow_sending_without_reply?: boolean;
  reply_markup?: TelegramReplyMarkup;
}

/**
 * Interface định nghĩa cấu trúc của một webhook options Telegram
 */
export interface TelegramSetWebhookOptions {
  certificate?: string;
  ip_address?: string;
  max_connections?: number;
  allowed_updates?: string[];
  drop_pending_updates?: boolean;
  secret_token?: string;
}

/**
 * Interface định nghĩa cấu trúc của một bot info Telegram
 */
export interface TelegramBotInfo {
  id: number;
  is_bot: boolean;
  first_name: string;
  username: string;
  can_join_groups?: boolean;
  can_read_all_group_messages?: boolean;
  supports_inline_queries?: boolean;
}
