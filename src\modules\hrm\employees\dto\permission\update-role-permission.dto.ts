import { ApiProperty } from '@nestjs/swagger';
import {
  IsArray,
  IsInt,
  IsNotEmpty,
  IsPositive,
  IsString,
  MaxLength,
} from 'class-validator';

/**
 * DTO để cập nhật quyền cho vai trò
 */
export class UpdateRolePermissionDto {
  /**
   * ID của vai trò cần cập nhật quyền
   */
  @ApiProperty({
    description: 'ID của vai trò cần cập nhật quyền',
    example: 1,
  })
  @IsInt()
  @IsPositive()
  @IsNotEmpty()
  roleId: number;

  /**
   * Danh sách ID của các quyền sẽ được gán cho vai trò
   */
  @ApiProperty({
    description: 'Danh sách ID của các quyền sẽ được gán cho vai trò',
    example: [1, 2, 3],
    type: [Number],
  })
  @IsArray()
  @IsInt({ each: true })
  @IsPositive({ each: true })
  permissionIds: number[];
}

/**
 * DTO phản hồi sau khi cập nhật quyền cho vai trò
 */
export class UpdateRolePermissionResponseDto {
  /**
   * ID của vai trò đã được cập nhật quyền
   */
  @ApiProperty({
    description: 'ID của vai trò đã được cập nhật quyền',
    example: 1,
  })
  roleId: number;

  /**
   * Tên của vai trò
   */
  @ApiProperty({
    description: 'Tên của vai trò',
    example: 'Admin',
  })
  roleName: string;

  /**
   * Danh sách ID của các quyền đã được gán cho vai trò
   */
  @ApiProperty({
    description: 'Danh sách ID của các quyền đã được gán cho vai trò',
    example: [1, 2, 3],
    type: [Number],
  })
  permissionIds: number[];
}
