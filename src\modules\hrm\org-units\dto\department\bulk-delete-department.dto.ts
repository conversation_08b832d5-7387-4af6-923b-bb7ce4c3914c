import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsNotEmpty, IsNumber, ArrayMinSize } from 'class-validator';
import { Type } from 'class-transformer';

/**
 * DTO cho việc xóa nhiều phòng ban
 */
export class BulkDeleteDepartmentDto {
  /**
   * Danh sách ID các phòng ban cần xóa
   */
  @ApiProperty({
    description: 'Danh sách ID các phòng ban cần xóa',
    example: [1, 2, 3],
    type: [Number],
    required: true,
  })
  @IsNotEmpty()
  @IsArray()
  @ArrayMinSize(1, { message: '<PERSON>ải có ít nhất 1 phòng ban để xóa' })
  @IsNumber({}, { each: true, message: 'Mỗi ID phải là số' })
  @Type(() => Number)
  ids: number[];
}
