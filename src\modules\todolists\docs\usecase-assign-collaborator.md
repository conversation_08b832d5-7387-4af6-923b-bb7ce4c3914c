## 2.2.2.12 <PERSON><PERSON> tả use case Thêm cộng tác viên vào công việc

**<PERSON><PERSON> tả ngắn:** Cho phép người dùng thêm cộng tác viên vào công việc để làm việc cùng nhau trên một nhiệm vụ.

### Luồng các sự kiện:

#### Luồng cơ bản:
1) Khi người dùng chọn "Thêm cộng tác viên" trên màn hình chi tiết công việc, hệ thống hiển thị danh sách người dùng có thể được thêm vào.
2) Người dùng tìm kiếm và chọn một hoặc nhiều người dùng từ danh sách.
3) Người dùng chọn vai trò cho mỗi cộng tác viên (ng<PERSON><PERSON><PERSON> xem, người đóng góp, người có quyền chỉnh sửa).
4) Người dùng nhấn "Thêm" và hệ thống lưu thông tin cộng tác viên vào công việc.
5) Hệ thống gửi thông báo cho các cộng tác viên vừa được thêm vào và hiển thị thông báo "Đã thêm cộng tác viên thành công".
6) Use case kết thúc.

#### Luồng rẽ nhánh:
1) Nếu người dùng không chọn cộng tác viên, hệ thống hiển thị cảnh báo "Vui lòng chọn ít nhất một cộng tác viên".
2) Nếu người dùng không chọn vai trò cho cộng tác viên, hệ thống tự động gán vai trò mặc định là "người xem".
3) Nếu người dùng chọn một người đã là cộng tác viên, hệ thống hiển thị thông báo "Người dùng đã là cộng tác viên của công việc này" và cho phép cập nhật vai trò.
4) Nếu người dùng nhấn "Hủy", hệ thống trở về màn hình chi tiết công việc mà không thêm cộng tác viên.
5) Khi có lỗi hệ thống/DB, hiển thị thông báo "Lỗi hệ thống, vui lòng thử lại sau" và kết thúc use case.

### Các yêu cầu đặc biệt:
- Người dùng chỉ có thể thêm cộng tác viên là thành viên của cùng dự án hoặc cùng phòng ban.
- Cho phép thiết lập các quyền khác nhau cho từng cộng tác viên (xem, bình luận, chỉnh sửa).

### Tiền điều kiện:
- Người dùng đã đăng nhập.
- Người dùng là người tạo công việc hoặc admin của dự án.
- Công việc đang ở trạng thái chưa hoàn thành.

### Hậu điều kiện:
- Cộng tác viên được thêm vào công việc với vai trò tương ứng.
- Cộng tác viên nhận được thông báo về việc được thêm vào công việc.
- Danh sách cộng tác viên của công việc được cập nhật và hiển thị trên giao diện.

### Điểm mở rộng:
- Tích hợp với hệ thống thông báo để gửi email hoặc thông báo di động cho cộng tác viên.
- Cho phép người dùng thiết lập lịch trình làm việc và đặt mốc thời gian cho từng cộng tác viên. 